from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal

class Currency(models.Model):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=3)
    symbol = models.CharField(max_length=5)
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=2)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.exchange_rate}"

class Product(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    quantity = models.IntegerField(default=0)
    min_quantity = models.IntegerField(default=5)
    purchase_price_syp = models.DecimalField(max_digits=10, decimal_places=2)
    purchase_price_usd = models.DecimalField(max_digits=10, decimal_places=2)
    selling_price_syp = models.DecimalField(max_digits=10, decimal_places=2)
    selling_price_usd = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class Customer(models.Model):
    name = models.CharField(max_length=200)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    total_debt = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class Invoice(models.Model):
    PAYMENT_TYPES = [
        ('CASH', 'نقدي'),
        ('DEBT', 'دين'),
        ('PARTIAL', 'دفع جزئي')
    ]

    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    payment_type = models.CharField(max_length=10, choices=PAYMENT_TYPES)
    total_amount_syp = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount_usd = models.DecimalField(max_digits=10, decimal_places=2)
    paid_amount_syp = models.DecimalField(max_digits=10, decimal_places=2)
    remaining_amount_syp = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"Invoice #{self.id} - {self.customer.name if self.customer else 'Walk-in'}"

class InvoiceItem(models.Model):
    invoice = models.ForeignKey(Invoice, related_name='items', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    unit_price_syp = models.DecimalField(max_digits=10, decimal_places=2)
    unit_price_usd = models.DecimalField(max_digits=10, decimal_places=2)
    total_price_syp = models.DecimalField(max_digits=10, decimal_places=2)
    total_price_usd = models.DecimalField(max_digits=10, decimal_places=2)

    def save(self, *args, **kwargs):
        self.total_price_syp = self.quantity * self.unit_price_syp
        self.total_price_usd = self.quantity * self.unit_price_usd
        super().save(*args, **kwargs)

class Payment(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, null=True, blank=True)
    amount_syp = models.DecimalField(max_digits=10, decimal_places=2)
    amount_usd = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"Payment of {self.amount_syp} SYP from {self.customer.name}"