import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_system/models/product.dart';

void main() {
  group('Product Model Tests', () {
    late Product testProduct;

    setUp(() {
      testProduct = Product(
        id: 1,
        name: 'Test Product',
        barcode: '1234567890',
        purchasePrice: 100.0,
        salePrice: 150.0,
        quantity: 50,
        notes: 'Test product notes',
        minQuantity: 10,
        sellingPriceSyp: 150000.0,
        sellingPriceUsd: 10.0,
        purchasePriceSyp: 100000.0,
        purchasePriceUsd: 7.5,
        purchaseCurrency: 'USD',
      );
    });

    group('Product Creation', () {
      test('should create product with all required fields', () {
        // Assert
        expect(testProduct.id, 1);
        expect(testProduct.name, 'Test Product');
        expect(testProduct.barcode, '1234567890');
        expect(testProduct.purchasePrice, 100.0);
        expect(testProduct.salePrice, 150.0);
        expect(testProduct.quantity, 50);
        expect(testProduct.minQuantity, 10);
        expect(testProduct.purchaseCurrency, 'USD');
      });

      test('should create product with default values', () {
        // Arrange & Act
        final product = Product(
          id: 2,
          name: 'Simple Product',
          barcode: '123',
          purchasePrice: 50.0,
          salePrice: 75.0,
          quantity: 20,
          notes: '',
          minQuantity: 5,
          sellingPriceSyp: 75000.0,
          sellingPriceUsd: 5.0,
          purchasePriceSyp: 50000.0,
          purchasePriceUsd: 3.5,
          purchaseCurrency: 'SYP',
        );

        // Assert
        expect(product.notes, '');
        expect(product.purchaseCurrency, 'SYP');
      });
    });

    group('JSON Serialization', () {
      test('should convert product to JSON correctly', () {
        // Act
        final json = testProduct.toJson();

        // Assert
        expect(json['id'], 1);
        expect(json['name'], 'Test Product');
        expect(json['barcode'], '1234567890');
        expect(json['purchase_price'], 100.0);
        expect(json['sale_price'], 150.0);
        expect(json['quantity'], 50);
        expect(json['notes'], 'Test product notes');
        expect(json['min_quantity'], 10);
        expect(json['selling_price_syp'], 150000.0);
        expect(json['selling_price_usd'], 10.0);
        expect(json['purchase_price_syp'], 100000.0);
        expect(json['purchase_price_usd'], 7.5);
        expect(json['purchase_currency'], 'USD');
      });

      test('should create product from JSON correctly', () {
        // Arrange
        final json = {
          'id': 2,
          'name': 'JSON Product',
          'barcode': '9876543210',
          'purchase_price': 200.0,
          'sale_price': 300.0,
          'quantity': 25,
          'notes': 'JSON notes',
          'min_quantity': 5,
          'selling_price_syp': 300000.0,
          'selling_price_usd': 20.0,
          'purchase_price_syp': 200000.0,
          'purchase_price_usd': 15.0,
          'purchase_currency': 'SYP',
        };

        // Act
        final product = Product.fromJson(json);

        // Assert
        expect(product.id, 2);
        expect(product.name, 'JSON Product');
        expect(product.barcode, '9876543210');
        expect(product.purchasePrice, 200.0);
        expect(product.salePrice, 300.0);
        expect(product.quantity, 25);
        expect(product.notes, 'JSON notes');
        expect(product.minQuantity, 5);
        expect(product.purchaseCurrency, 'SYP');
      });

      test('should handle missing optional fields in JSON', () {
        // Arrange
        final json = {
          'id': 3,
          'name': 'Minimal Product',
          'barcode': '111',
          'purchase_price': 10.0,
          'sale_price': 15.0,
          'quantity': 5,
          'min_quantity': 1,
          'selling_price_syp': 15000.0,
          'selling_price_usd': 1.0,
          'purchase_price_syp': 10000.0,
          'purchase_price_usd': 0.75,
        };

        // Act
        final product = Product.fromJson(json);

        // Assert
        expect(product.notes, '');
        expect(product.purchaseCurrency, 'SYP'); // default value
      });
    });

    group('Product Operations', () {
      test('should copy product with updated fields', () {
        // Act
        final updatedProduct = testProduct.copyWith(
          quantity: 75,
          salePrice: 175.0,
          notes: 'Updated notes',
        );

        // Assert
        expect(updatedProduct.id, testProduct.id);
        expect(updatedProduct.name, testProduct.name);
        expect(updatedProduct.quantity, 75);
        expect(updatedProduct.salePrice, 175.0);
        expect(updatedProduct.notes, 'Updated notes');
        expect(updatedProduct.purchasePrice, testProduct.purchasePrice);
      });

      test('should check if product is low stock', () {
        // Arrange
        final lowStockProduct = testProduct.copyWith(quantity: 5);
        final normalStockProduct = testProduct.copyWith(quantity: 20);

        // Act & Assert
        expect(lowStockProduct.quantity <= lowStockProduct.minQuantity, true);
        expect(normalStockProduct.quantity <= normalStockProduct.minQuantity,
            false);
      });

      test('should check if product is out of stock', () {
        // Arrange
        final outOfStockProduct = testProduct.copyWith(quantity: 0);
        final inStockProduct = testProduct.copyWith(quantity: 1);

        // Act & Assert
        expect(outOfStockProduct.quantity == 0, true);
        expect(inStockProduct.quantity == 0, false);
      });

      test('should calculate profit margin correctly', () {
        // Act
        final profitMargin =
            ((testProduct.salePrice - testProduct.purchasePrice) /
                    testProduct.purchasePrice) *
                100;

        // Assert
        // (150 - 100) / 100 * 100 = 50%
        expect(profitMargin, 50.0);
      });

      test('should handle zero purchase price in profit margin', () {
        // Arrange
        final zeroPurchasePriceProduct = testProduct.copyWith(purchasePrice: 0);

        // Act
        final profitMargin = zeroPurchasePriceProduct.purchasePrice == 0
            ? 0.0
            : ((zeroPurchasePriceProduct.salePrice -
                        zeroPurchasePriceProduct.purchasePrice) /
                    zeroPurchasePriceProduct.purchasePrice) *
                100;

        // Assert
        expect(profitMargin, 0.0);
      });
    });

    group('Product Validation', () {
      test('should validate basic product properties', () {
        // Act & Assert
        expect(testProduct.name, isNotEmpty);
        expect(testProduct.barcode, isNotEmpty);
        expect(testProduct.purchasePrice, greaterThanOrEqualTo(0));
        expect(testProduct.salePrice, greaterThanOrEqualTo(0));
        expect(testProduct.quantity, greaterThanOrEqualTo(0));
        expect(testProduct.minQuantity, greaterThanOrEqualTo(0));
      });
    });

    group('Product Equality', () {
      test('should be equal when IDs are the same', () {
        // Arrange
        final product1 = testProduct;
        final product2 = testProduct.copyWith(name: 'Different Name');

        // Act & Assert
        expect(product1 == product2, true);
        expect(product1.hashCode, product2.hashCode);
      });

      test('should not be equal when IDs are different', () {
        // Arrange
        final product1 = testProduct;
        final product2 = testProduct.copyWith(id: 999);

        // Act & Assert
        expect(product1 == product2, false);
        expect(product1.hashCode, isNot(product2.hashCode));
      });
    });

    group('Product String Representation', () {
      test('should have valid string representation', () {
        // Act
        final stringRep = testProduct.toString();

        // Assert
        expect(stringRep, contains('Test Product'));
        expect(stringRep, contains('1234567890'));
        expect(stringRep, contains('100.0'));
        expect(stringRep, contains('150.0'));
      });
    });
  });
}
