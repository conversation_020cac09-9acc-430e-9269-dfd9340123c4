import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_system/models/invoice.dart';
import 'package:invoice_system/models/invoice_item.dart';

void main() {
  group('Invoice Model Tests', () {
    late Invoice testInvoice;
    late List<InvoiceItem> testItems;

    setUp(() {
      testItems = [
        InvoiceItem(
          id: '1',
          productId: '1',
          productName: 'Product 1',
          productBarcode: '123456',
          unitPriceSyp: 1000.0,
          unitPriceUsd: 1.0,
          quantity: 2,
          totalSyp: 2000.0,
          totalUsd: 2.0,
        ),
        InvoiceItem(
          id: '2',
          productId: '2',
          productName: 'Product 2',
          productBarcode: '789012',
          unitPriceSyp: 1500.0,
          unitPriceUsd: 1.5,
          quantity: 1,
          totalSyp: 1500.0,
          totalUsd: 1.5,
        ),
      ];

      testInvoice = Invoice(
        id: 1,
        customerId: 1,
        customerName: 'Test Customer',
        date: DateTime(2024, 1, 15),
        items: testItems,
        totalAmountSyp: 3500.0,
        totalAmountUsd: 3.5,
        paidAmountSyp: 2000.0,
        remainingAmountSyp: 1500.0,
        status: InvoiceStatus.partial,
        paymentType: PaymentType.partial,
        notes: 'Test invoice',
      );
    });

    group('Invoice Creation', () {
      test('should create invoice with all required fields', () {
        // Assert
        expect(testInvoice.id, 'INV001');
        expect(testInvoice.customerId, 'CUST001');
        expect(testInvoice.customerName, 'Test Customer');
        expect(testInvoice.items.length, 2);
        expect(testInvoice.totalAmountSyp, 3500.0);
        expect(testInvoice.status, InvoiceStatus.partial);
        expect(testInvoice.paymentType, PaymentType.partial);
      });

      test('should create invoice with default values', () {
        // Arrange & Act
        final invoice = Invoice(
          id: 2,
          customerId: 0,
          customerName: '',
          date: DateTime.now(),
          items: [],
          totalAmountSyp: 0,
          totalAmountUsd: 0,
          paidAmountSyp: 0,
          remainingAmountSyp: 0,
          status: InvoiceStatus.pending,
          paymentType: PaymentType.cash,
        );

        // Assert
        expect(invoice.notes, '');
        expect(invoice.isReturned, false);
        expect(invoice.returnedDate, isNull);
        expect(invoice.returnReason, isNull);
      });
    });

    group('JSON Serialization', () {
      test('should convert invoice to JSON correctly', () {
        // Act
        final json = testInvoice.toJson();

        // Assert
        expect(json['id'], 'INV001');
        expect(json['customer_id'], 'CUST001');
        expect(json['customer_name'], 'Test Customer');
        expect(json['total_amount_syp'], 3500.0);
        expect(json['total_amount_usd'], 3.5);
        expect(json['paid_amount_syp'], 2000.0);
        expect(json['remaining_amount_syp'], 1500.0);
        expect(json['status'], 'partial');
        expect(json['payment_type'], 'partial');
        expect(json['items'], isA<List>());
        expect(json['items'].length, 2);
      });

      test('should create invoice from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'INV002',
          'customer_id': 'CUST002',
          'customer_name': 'JSON Customer',
          'date': '2024-01-20T10:30:00.000Z',
          'items': [
            {
              'id': '1',
              'product_id': '1',
              'product_name': 'JSON Product',
              'product_barcode': '123456',
              'unit_price_syp': 1000.0,
              'unit_price_usd': 1.0,
              'quantity': 1,
              'total_syp': 1000.0,
              'total_usd': 1.0,
              'notes': '',
            }
          ],
          'total_amount_syp': 1000.0,
          'total_amount_usd': 1.0,
          'paid_amount_syp': 1000.0,
          'remaining_amount_syp': 0.0,
          'status': 'paid',
          'payment_type': 'cash',
          'notes': 'JSON Test',
          'is_returned': false,
        };

        // Act
        final invoice = Invoice.fromJson(json);

        // Assert
        expect(invoice.id, 'INV002');
        expect(invoice.customerId, 'CUST002');
        expect(invoice.customerName, 'JSON Customer');
        expect(invoice.totalAmountSyp, 1000.0);
        expect(invoice.status, InvoiceStatus.paid);
        expect(invoice.paymentType, PaymentType.cash);
        expect(invoice.items.length, 1);
        expect(invoice.notes, 'JSON Test');
      });

      test('should handle missing optional fields in JSON', () {
        // Arrange
        final json = {
          'id': 'INV003',
          'date': '2024-01-20T10:30:00.000Z',
          'items': [],
          'total_amount_syp': 0.0,
          'total_amount_usd': 0.0,
          'paid_amount_syp': 0.0,
          'remaining_amount_syp': 0.0,
          'status': 'pending',
          'payment_type': 'cash',
        };

        // Act
        final invoice = Invoice.fromJson(json);

        // Assert
        expect(invoice.customerId, '');
        expect(invoice.customerName, '');
        expect(invoice.notes, '');
        expect(invoice.isReturned, false);
        expect(invoice.returnedDate, isNull);
      });
    });

    group('Invoice Operations', () {
      test('should copy invoice with updated fields', () {
        // Act
        final updatedInvoice = testInvoice.copyWith(
          status: InvoiceStatus.paid,
          paidAmountSyp: 3500.0,
          remainingAmountSyp: 0.0,
        );

        // Assert
        expect(updatedInvoice.id, testInvoice.id);
        expect(updatedInvoice.status, InvoiceStatus.paid);
        expect(updatedInvoice.paidAmountSyp, 3500.0);
        expect(updatedInvoice.remainingAmountSyp, 0.0);
        expect(updatedInvoice.customerName, testInvoice.customerName);
      });

      test('should mark invoice as returned', () {
        // Arrange
        final returnDate = DateTime.now();
        const returnReason = 'Customer request';

        // Act
        final returnedInvoice = testInvoice.copyWith(
          isReturned: true,
          returnedDate: returnDate,
          returnReason: returnReason,
          status: InvoiceStatus.returned,
        );

        // Assert
        expect(returnedInvoice.isReturned, true);
        expect(returnedInvoice.returnedDate, returnDate);
        expect(returnedInvoice.returnReason, returnReason);
        expect(returnedInvoice.status, InvoiceStatus.returned);
      });
    });

    group('Invoice Equality', () {
      test('should be equal when IDs are the same', () {
        // Arrange
        final invoice1 = testInvoice;
        final invoice2 = testInvoice.copyWith(customerName: 'Different Name');

        // Act & Assert
        expect(invoice1 == invoice2, true);
        expect(invoice1.hashCode, invoice2.hashCode);
      });

      test('should not be equal when IDs are different', () {
        // Arrange
        final invoice1 = testInvoice;
        final invoice2 = testInvoice.copyWith(id: 2);

        // Act & Assert
        expect(invoice1 == invoice2, false);
        expect(invoice1.hashCode, isNot(invoice2.hashCode));
      });
    });

    group('Invoice Validation', () {
      test('should have valid string representation', () {
        // Act
        final stringRep = testInvoice.toString();

        // Assert
        expect(stringRep, contains('INV001'));
        expect(stringRep, contains('Test Customer'));
        expect(stringRep, contains('3500.0'));
        expect(stringRep, contains('partial'));
      });
    });
  });
}
