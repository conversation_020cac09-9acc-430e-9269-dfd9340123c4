#!/usr/bin/env python3
"""
سكريبت إصلاح الأخطاء التلقائي
يقوم بإصلاح الأخطاء الشائعة في كود Flutter
"""

import os
import re
import glob

def fix_create_state_methods():
    """إصلاح دوال createState"""
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    for file_path in dart_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن نمط createState القديم
            old_pattern = r'(\s+)(_\w+State)\s+createState\(\)\s*=>\s*\2\(\);'
            
            def replace_create_state(match):
                indent = match.group(1)
                state_class = match.group(2)
                widget_class = state_class.replace('State', '').replace('_', '')
                return f'{indent}State<{widget_class}> createState() => {state_class}();'
            
            new_content = re.sub(old_pattern, replace_create_state, content)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f'✅ تم إصلاح createState في {file_path}')
                
        except Exception as e:
            print(f'❌ خطأ في معالجة {file_path}: {e}')

def fix_with_opacity():
    """إصلاح withOpacity المهجورة"""
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    for file_path in dart_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال withOpacity بـ withValues
            new_content = re.sub(
                r'\.withOpacity\(([^)]+)\)',
                r'.withValues(alpha: \1)',
                content
            )
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f'✅ تم إصلاح withOpacity في {file_path}')
                
        except Exception as e:
            print(f'❌ خطأ في معالجة {file_path}: {e}')

def add_mounted_checks():
    """إضافة فحوصات mounted للـ BuildContext"""
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    patterns_to_fix = [
        r'(await\s+[^;]+;)\s*\n(\s*)(Navigator\.of\(context\))',
        r'(await\s+[^;]+;)\s*\n(\s*)(ScaffoldMessenger\.of\(context\))',
    ]
    
    for file_path in dart_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            new_content = content
            
            for pattern in patterns_to_fix:
                def add_mounted_check(match):
                    await_line = match.group(1)
                    indent = match.group(2)
                    context_usage = match.group(3)
                    
                    return f'{await_line}\n{indent}if (mounted) {{\n{indent}  {context_usage}'
                
                new_content = re.sub(pattern, add_mounted_check, new_content)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f'✅ تم إضافة mounted checks في {file_path}')
                
        except Exception as e:
            print(f'❌ خطأ في معالجة {file_path}: {e}')

def fix_const_snackbar():
    """إصلاح const SnackBar مع متغيرات"""
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    for file_path in dart_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن const SnackBar مع Text متغير
            pattern = r'const\s+SnackBar\(\s*content:\s*Text\([^)]*\w+[^)]*\)'
            
            if re.search(pattern, content):
                # إزالة const من SnackBar
                new_content = re.sub(r'const\s+(SnackBar\()', r'\1', content)
                
                if new_content != content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f'✅ تم إصلاح const SnackBar في {file_path}')
                
        except Exception as e:
            print(f'❌ خطأ في معالجة {file_path}: {e}')

def main():
    """تشغيل جميع الإصلاحات"""
    print('🚀 بدء إصلاح الأخطاء التلقائي...\n')
    
    print('1️⃣ إصلاح دوال createState...')
    fix_create_state_methods()
    
    print('\n2️⃣ إصلاح withOpacity المهجورة...')
    fix_with_opacity()
    
    print('\n3️⃣ إضافة فحوصات mounted...')
    add_mounted_checks()
    
    print('\n4️⃣ إصلاح const SnackBar...')
    fix_const_snackbar()
    
    print('\n✅ تم الانتهاء من جميع الإصلاحات!')
    print('\n📝 يُنصح بمراجعة التغييرات قبل الكوميت')
    print('🧪 قم بتشغيل flutter analyze للتأكد من عدم وجود أخطاء')

if __name__ == '__main__':
    main()
