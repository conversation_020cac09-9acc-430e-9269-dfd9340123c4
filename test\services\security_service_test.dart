import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_system/services/security_service.dart';

void main() {
  group('SecurityService Tests', () {
    setUp(() async {
      // تنظيف البيانات قبل كل اختبار
      await SecurityService.clearSecurityData();
    });

    tearDown(() async {
      // تنظيف البيانات بعد كل اختبار
      await SecurityService.clearSecurityData();
    });

    group('App Activation', () {
      test('should activate app with correct activation code', () async {
        // Arrange
        const correctCode = '125874369';

        // Act
        final result = await SecurityService.activateApp(correctCode);

        // Assert
        expect(result, isTrue);
        expect(SecurityService.isActivated(), isTrue);
      });

      test('should not activate app with incorrect activation code', () async {
        // Arrange
        const incorrectCode = '123456789';

        // Act
        final result = await SecurityService.activateApp(incorrectCode);

        // Assert
        expect(result, isFalse);
        expect(SecurityService.isActivated(), isFalse);
      });

      test('should not activate app with empty activation code', () async {
        // Arrange
        const emptyCode = '';

        // Act
        final result = await SecurityService.activateApp(emptyCode);

        // Assert
        expect(result, isFalse);
        expect(SecurityService.isActivated(), isFalse);
      });
    });

    group('PIN Management', () {
      test('should set PIN with valid 4-digit code', () async {
        // Arrange
        const validPin = '1234';

        // Act & Assert
        expect(() => SecurityService.setPin(validPin), returnsNormally);
        expect(SecurityService.isPinSet(), isTrue);
      });

      test('should throw exception for invalid PIN length', () async {
        // Arrange
        const invalidPin = '123'; // أقل من 4 أرقام

        // Act & Assert
        expect(
          () => SecurityService.setPin(invalidPin),
          throwsA(isA<Exception>()),
        );
      });

      test('should throw exception for non-numeric PIN', () async {
        // Arrange
        const invalidPin = 'abcd';

        // Act & Assert
        expect(
          () => SecurityService.setPin(invalidPin),
          throwsA(isA<Exception>()),
        );
      });

      test('should verify correct PIN', () async {
        // Arrange
        const pin = '1234';
        await SecurityService.setPin(pin);

        // Act
        final result = SecurityService.verifyPin(pin);

        // Assert
        expect(result, isTrue);
      });

      test('should not verify incorrect PIN', () async {
        // Arrange
        const correctPin = '1234';
        const incorrectPin = '5678';
        await SecurityService.setPin(correctPin);

        // Act
        final result = SecurityService.verifyPin(incorrectPin);

        // Assert
        expect(result, isFalse);
      });
    });

    group('PIN Reset', () {
      test('should reset PIN with correct activation code', () async {
        // Arrange
        const oldPin = '1234';
        const newPin = '5678';
        const activationCode = '125874369';

        await SecurityService.setPin(oldPin);

        // Act
        final result = await SecurityService.resetPin(activationCode, newPin);

        // Assert
        expect(result, isTrue);
        expect(SecurityService.verifyPin(newPin), isTrue);
        expect(SecurityService.verifyPin(oldPin), isFalse);
      });

      test('should not reset PIN with incorrect activation code', () async {
        // Arrange
        const oldPin = '1234';
        const newPin = '5678';
        const incorrectActivationCode = '123456789';

        await SecurityService.setPin(oldPin);

        // Act
        final result =
            await SecurityService.resetPin(incorrectActivationCode, newPin);

        // Assert
        expect(result, isFalse);
        expect(SecurityService.verifyPin(oldPin), isTrue);
        expect(SecurityService.verifyPin(newPin), isFalse);
      });
    });

    group('Security Status', () {
      test('should return needsActivation when app is not activated', () {
        // Act
        final status = SecurityService.getSecurityStatus();

        // Assert
        expect(status, SecurityStatus.needsActivation);
      });

      test('should return needsPinSetup when app is activated but PIN not set',
          () async {
        // Arrange
        await SecurityService.activateApp('125874369');

        // Act
        final status = SecurityService.getSecurityStatus();

        // Assert
        expect(status, SecurityStatus.needsPinSetup);
      });

      test(
          'should return needsPinVerification when app is activated and PIN is set',
          () async {
        // Arrange
        await SecurityService.activateApp('125874369');
        await SecurityService.setPin('1234');

        // Act
        final status = SecurityService.getSecurityStatus();

        // Assert
        expect(status, SecurityStatus.needsPinVerification);
      });
    });

    group('Data Persistence', () {
      test('should persist activation status', () async {
        // Arrange
        await SecurityService.activateApp('125874369');

        // Act - إنشاء instance جديد لمحاكاة إعادة تشغيل التطبيق
        final isActivated = SecurityService.isActivated();

        // Assert
        expect(isActivated, isTrue);
      });

      test('should persist PIN setting status', () async {
        // Arrange
        await SecurityService.activateApp('125874369');
        await SecurityService.setPin('1234');

        // Act
        final isPinSet = SecurityService.isPinSet();

        // Assert
        expect(isPinSet, isTrue);
      });
    });
  });
}
