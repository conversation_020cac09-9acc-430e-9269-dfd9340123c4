import 'package:flutter/material.dart';
import '../models/invoice.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';
import '../services/auto_journal_service.dart';

class InvoiceProvider with ChangeNotifier {
  List<Invoice> _invoices = [];
  bool _isLoading = false;
  String _error = '';

  InvoiceProvider();

  List<Invoice> get invoices => _invoices;
  bool get isLoading => _isLoading;
  String get error => _error;

  Future<void> fetchInvoices() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _invoices = await OfflineDatabaseService.getAllInvoices();
      debugPrint(
          '✅ تم تحميل ${_invoices.length} فاتورة من قاعدة البيانات المحلية');
    } catch (e) {
      _error = 'فشل في تحميل الفواتير';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Invoice> createInvoice(Invoice invoice) async {
    try {
      final newInvoice = await OfflineDatabaseService.addInvoice(invoice);
      _invoices.add(newInvoice);

      // إنشاء قيد محاسبي تلقائي للفاتورة
      try {
        await AutoJournalService.createSaleJournalEntry(newInvoice);
        debugPrint('✅ تم إنشاء القيد المحاسبي للفاتورة رقم: ${newInvoice.id}');
      } catch (e) {
        debugPrint('⚠️ فشل في إنشاء القيد المحاسبي: $e');
        // لا نوقف العملية إذا فشل القيد المحاسبي
      }

      // إرسال إشعار نجاح
      await OfflineNotificationService.showNewInvoiceNotification(
        newInvoice.id.toString(),
        newInvoice.totalAmountSyp,
      );

      notifyListeners();
      debugPrint('✅ تم إنشاء الفاتورة رقم: ${newInvoice.id}');
      return newInvoice;
    } catch (e) {
      _error = 'فشل في إنشاء الفاتورة';
      await OfflineNotificationService.showErrorNotification(
          'فشل في إنشاء الفاتورة: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }

  Future<void> updateInvoice(Invoice invoice) async {
    try {
      final updatedInvoice =
          await OfflineDatabaseService.updateInvoice(invoice);
      final index = _invoices.indexWhere((i) => i.id == invoice.id);
      if (index != -1) {
        _invoices[index] = updatedInvoice;
      }

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم تحديث الفاتورة رقم ${updatedInvoice.id} بنجاح');

      notifyListeners();
      debugPrint('✅ تم تحديث الفاتورة رقم: ${updatedInvoice.id}');
    } catch (e) {
      _error = 'فشل في تحديث الفاتورة';
      await OfflineNotificationService.showErrorNotification(
          'فشل في تحديث الفاتورة: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }

  Future<void> deleteInvoice(String id) async {
    try {
      // تحويل id إلى int
      final invoiceId = int.parse(id);

      // الحصول على الفاتورة قبل الحذف
      final invoiceToDelete =
          _invoices.firstWhere((invoice) => invoice.id.toString() == id);

      await OfflineDatabaseService.deleteInvoice(invoiceId);
      _invoices.removeWhere((invoice) => invoice.id.toString() == id);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم حذف الفاتورة رقم ${invoiceToDelete.id} بنجاح');

      notifyListeners();
      debugPrint('✅ تم حذف الفاتورة رقم: ${invoiceToDelete.id}');
    } catch (e) {
      _error = 'فشل في حذف الفاتورة';
      await OfflineNotificationService.showErrorNotification(
          'فشل في حذف الفاتورة: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }

  /// دفع دين عميل
  Future<void> payCustomerDebt({
    required int customerId,
    required String customerName,
    required double amount,
    String? notes,
  }) async {
    try {
      // إنشاء قيد محاسبي لدفع الدين
      await AutoJournalService.createPaymentJournalEntry(
        customerId: customerId,
        customerName: customerName,
        amount: amount,
        paymentDate: DateTime.now(),
        notes: notes,
      );

      // تحديث الفواتير المرتبطة بالعميل
      await _updateCustomerInvoicesAfterPayment(customerId, amount);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
        'تم تسجيل دفع بقيمة ${amount.toStringAsFixed(2)} ل.س من العميل $customerName',
      );

      notifyListeners();
      debugPrint('✅ تم تسجيل دفع دين للعميل: $customerName');
    } catch (e) {
      _error = 'فشل في تسجيل دفع الدين';
      await OfflineNotificationService.showErrorNotification(
        'فشل في تسجيل دفع الدين: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// تحديث فواتير العميل بعد الدفع
  Future<void> _updateCustomerInvoicesAfterPayment(
      int customerId, double paidAmount) async {
    try {
      // جلب فواتير العميل غير المدفوعة
      final customerInvoices = _invoices
          .where((invoice) =>
              invoice.customerId == customerId &&
              invoice.remainingAmountSyp > 0)
          .toList();

      // ترتيب حسب التاريخ (الأقدم أولاً)
      customerInvoices.sort((a, b) => a.date.compareTo(b.date));

      double remainingPayment = paidAmount;

      for (var invoice in customerInvoices) {
        if (remainingPayment <= 0) break;

        if (remainingPayment >= invoice.remainingAmountSyp) {
          // دفع كامل للفاتورة
          remainingPayment -= invoice.remainingAmountSyp;
          final updatedInvoice = invoice.copyWith(
            paidAmountSyp: invoice.totalAmountSyp,
            remainingAmountSyp: 0,
            status: InvoiceStatus.paid,
          );
          await updateInvoice(updatedInvoice);
        } else {
          // دفع جزئي للفاتورة
          final updatedInvoice = invoice.copyWith(
            paidAmountSyp: invoice.paidAmountSyp + remainingPayment,
            remainingAmountSyp: invoice.remainingAmountSyp - remainingPayment,
            status: InvoiceStatus.partial,
          );
          await updateInvoice(updatedInvoice);
          remainingPayment = 0;
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في تحديث فواتير العميل: $e');
    }
  }

  /// الحصول على إجمالي ديون عميل
  double getCustomerTotalDebt(int customerId) {
    return _invoices
        .where((invoice) => invoice.customerId == customerId)
        .fold(0.0, (total, invoice) => total + invoice.remainingAmountSyp);
  }

  /// الحصول على فواتير عميل
  List<Invoice> getCustomerInvoices(int customerId) {
    return _invoices
        .where((invoice) => invoice.customerId == customerId)
        .toList();
  }

  /// الحصول على إحصائيات المبيعات
  Map<String, dynamic> getSalesStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisMonth = DateTime(now.year, now.month, 1);

    double todaySales = 0;
    double monthSales = 0;
    double totalSales = 0;
    double totalDebt = 0;

    for (var invoice in _invoices) {
      totalSales += invoice.totalAmountSyp;
      totalDebt += invoice.remainingAmountSyp;

      if (invoice.date.isAfter(today.subtract(const Duration(days: 1)))) {
        todaySales += invoice.totalAmountSyp;
      }

      if (invoice.date.isAfter(thisMonth.subtract(const Duration(days: 1)))) {
        monthSales += invoice.totalAmountSyp;
      }
    }

    return {
      'today_sales': todaySales,
      'month_sales': monthSales,
      'total_sales': totalSales,
      'total_debt': totalDebt,
      'total_invoices': _invoices.length,
      'paid_invoices':
          _invoices.where((i) => i.status == InvoiceStatus.paid).length,
      'pending_invoices':
          _invoices.where((i) => i.status == InvoiceStatus.pending).length,
    };
  }
}
