import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/customer_provider.dart';
import '../mobile_main.dart';

class MobileCustomersScreen extends StatefulWidget {
  const MobileCustomersScreen({super.key});

  @override
  State<MobileCustomersScreen> createState() => _MobileCustomersScreenState();
}

class _MobileCustomersScreenState extends State<MobileCustomersScreen> {
  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  Future<void> _loadCustomers() async {
    await context.read<CustomerProvider>().fetchCustomers();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MobileAppBar(
        title: 'العملاء',
        automaticallyImplyLeading: false,
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const MobileLoadingWidget(message: 'جاري تحميل العملاء...');
          }

          if (provider.customers.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: _loadCustomers,
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: provider.customers.length,
              itemBuilder: (context, index) {
                final customer = provider.customers[index];
                return MobileCard(
                  child: MobileListTile(
                    leading: CircleAvatar(
                      backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      child: Icon(
                        Icons.person,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    title: Text(
                      customer.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (customer.phone.isNotEmpty)
                          Text('الهاتف: ${customer.phone}'),
                        if (customer.email.isNotEmpty)
                          Text('البريد: ${customer.email}'),
                        Text('الرصيد: ${customer.balance.toStringAsFixed(0)} ل.س'),
                      ],
                    ),
                    trailing: customer.balance > 0
                        ? Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'دين',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.check_circle,
                            color: Colors.green,
                          ),
                    onTap: () => _showCustomerDetails(customer),
                  ),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: MobileFloatingActionButton(
        onPressed: _addCustomer,
        icon: Icons.add,
        tooltip: 'إضافة عميل',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد عملاء',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة عميل جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addCustomer,
            icon: const Icon(Icons.add),
            label: const Text('إضافة عميل'),
          ),
        ],
      ),
    );
  }

  void _showCustomerDetails(customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(customer.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (customer.phone.isNotEmpty)
              _buildDetailRow('الهاتف', customer.phone),
            if (customer.email.isNotEmpty)
              _buildDetailRow('البريد الإلكتروني', customer.email),
            if (customer.address.isNotEmpty)
              _buildDetailRow('العنوان', customer.address),
            _buildDetailRow('الرصيد', '${customer.balance.toStringAsFixed(0)} ل.س'),
          ],
        ),
        actions: [
          if (customer.balance > 0)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _payDebt(customer);
              },
              child: const Text('دفع الدين'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _addCustomer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إضافة عميل ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _payDebt(customer) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة دفع دين ${customer.name} ستكون متاحة قريباً'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
