import 'package:hive/hive.dart';

part 'api_integration.g.dart';

/// أنواع التكامل الخارجي
@HiveType(typeId: 20)
enum IntegrationType {
  @HiveField(0)
  accounting,    // أنظمة محاسبية

  @HiveField(1)
  ecommerce,     // متاجر إلكترونية

  @HiveField(2)
  payment,       // بوابات دفع

  @HiveField(3)
  shipping,      // شركات شحن

  @HiveField(4)
  banking,       // البنوك

  @HiveField(5)
  crm,          // إدارة علاقات العملاء

  @HiveField(6)
  erp,          // أنظمة تخطيط الموارد

  @HiveField(7)
  pos,          // أنظمة نقاط البيع

  @HiveField(8)
  warehouse,    // أنظمة إدارة المخازن

  @HiveField(9)
  custom,       // تكامل مخصص
}

/// حالة التكامل
@HiveType(typeId: 21)
enum IntegrationStatus {
  @HiveField(0)
  active,       // نشط

  @HiveField(1)
  inactive,     // غير نشط

  @HiveField(2)
  error,        // خطأ

  @HiveField(3)
  testing,      // قيد الاختبار

  @HiveField(4)
  pending,      // في الانتظار
}

/// نموذج التكامل الخارجي
@HiveType(typeId: 22)
class APIIntegration extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String description;

  @HiveField(3)
  final IntegrationType type;

  @HiveField(4)
  final String baseUrl;

  @HiveField(5)
  final String apiKey;

  @HiveField(6)
  final String? secretKey;

  @HiveField(7)
  final Map<String, String> headers;

  @HiveField(8)
  final Map<String, dynamic> configuration;

  @HiveField(9)
  final IntegrationStatus status;

  @HiveField(10)
  final bool isEnabled;

  @HiveField(11)
  final DateTime createdAt;

  @HiveField(12)
  final DateTime? lastSyncAt;

  @HiveField(13)
  final String? lastError;

  @HiveField(14)
  final int syncCount;

  @HiveField(15)
  final List<String> supportedOperations;

  APIIntegration({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.baseUrl,
    required this.apiKey,
    this.secretKey,
    required this.headers,
    required this.configuration,
    this.status = IntegrationStatus.inactive,
    this.isEnabled = false,
    required this.createdAt,
    this.lastSyncAt,
    this.lastError,
    this.syncCount = 0,
    required this.supportedOperations,
  });

  /// إنشاء نسخة محدثة
  APIIntegration copyWith({
    int? id,
    String? name,
    String? description,
    IntegrationType? type,
    String? baseUrl,
    String? apiKey,
    String? secretKey,
    Map<String, String>? headers,
    Map<String, dynamic>? configuration,
    IntegrationStatus? status,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? lastSyncAt,
    String? lastError,
    int? syncCount,
    List<String>? supportedOperations,
  }) {
    return APIIntegration(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      baseUrl: baseUrl ?? this.baseUrl,
      apiKey: apiKey ?? this.apiKey,
      secretKey: secretKey ?? this.secretKey,
      headers: headers ?? this.headers,
      configuration: configuration ?? this.configuration,
      status: status ?? this.status,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      lastError: lastError ?? this.lastError,
      syncCount: syncCount ?? this.syncCount,
      supportedOperations: supportedOperations ?? this.supportedOperations,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'base_url': baseUrl,
      'api_key': apiKey,
      'secret_key': secretKey,
      'headers': headers,
      'configuration': configuration,
      'status': status.toString().split('.').last,
      'is_enabled': isEnabled,
      'created_at': createdAt.toIso8601String(),
      'last_sync_at': lastSyncAt?.toIso8601String(),
      'last_error': lastError,
      'sync_count': syncCount,
      'supported_operations': supportedOperations,
    };
  }

  /// إنشاء من JSON
  factory APIIntegration.fromJson(Map<String, dynamic> json) {
    return APIIntegration(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      type: _parseIntegrationType(json['type']),
      baseUrl: json['base_url'] ?? '',
      apiKey: json['api_key'] ?? '',
      secretKey: json['secret_key'],
      headers: Map<String, String>.from(json['headers'] ?? {}),
      configuration: Map<String, dynamic>.from(json['configuration'] ?? {}),
      status: _parseIntegrationStatus(json['status']),
      isEnabled: json['is_enabled'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      lastSyncAt: json['last_sync_at'] != null ? DateTime.parse(json['last_sync_at']) : null,
      lastError: json['last_error'],
      syncCount: json['sync_count'] ?? 0,
      supportedOperations: List<String>.from(json['supported_operations'] ?? []),
    );
  }

  /// تحليل نوع التكامل
  static IntegrationType _parseIntegrationType(String? type) {
    switch (type) {
      case 'accounting': return IntegrationType.accounting;
      case 'ecommerce': return IntegrationType.ecommerce;
      case 'payment': return IntegrationType.payment;
      case 'shipping': return IntegrationType.shipping;
      case 'banking': return IntegrationType.banking;
      case 'crm': return IntegrationType.crm;
      case 'erp': return IntegrationType.erp;
      case 'pos': return IntegrationType.pos;
      case 'warehouse': return IntegrationType.warehouse;
      case 'custom': return IntegrationType.custom;
      default: return IntegrationType.custom;
    }
  }

  /// تحليل حالة التكامل
  static IntegrationStatus _parseIntegrationStatus(String? status) {
    switch (status) {
      case 'active': return IntegrationStatus.active;
      case 'inactive': return IntegrationStatus.inactive;
      case 'error': return IntegrationStatus.error;
      case 'testing': return IntegrationStatus.testing;
      case 'pending': return IntegrationStatus.pending;
      default: return IntegrationStatus.inactive;
    }
  }

  /// الحصول على وصف النوع بالعربية
  String get typeDescription {
    switch (type) {
      case IntegrationType.accounting: return 'نظام محاسبي';
      case IntegrationType.ecommerce: return 'متجر إلكتروني';
      case IntegrationType.payment: return 'بوابة دفع';
      case IntegrationType.shipping: return 'شركة شحن';
      case IntegrationType.banking: return 'بنك';
      case IntegrationType.crm: return 'إدارة علاقات العملاء';
      case IntegrationType.erp: return 'تخطيط الموارد';
      case IntegrationType.pos: return 'نقطة بيع';
      case IntegrationType.warehouse: return 'إدارة مخازن';
      case IntegrationType.custom: return 'تكامل مخصص';
    }
  }

  /// الحصول على وصف الحالة بالعربية
  String get statusDescription {
    switch (status) {
      case IntegrationStatus.active: return 'نشط';
      case IntegrationStatus.inactive: return 'غير نشط';
      case IntegrationStatus.error: return 'خطأ';
      case IntegrationStatus.testing: return 'قيد الاختبار';
      case IntegrationStatus.pending: return 'في الانتظار';
    }
  }

  /// فحص إذا كان التكامل يدعم عملية معينة
  bool supportsOperation(String operation) {
    return supportedOperations.contains(operation);
  }

  /// فحص إذا كان التكامل جاهز للاستخدام
  bool get isReady {
    return isEnabled && status == IntegrationStatus.active && apiKey.isNotEmpty;
  }
}

/// نموذج سجل المزامنة
@HiveType(typeId: 23)
class SyncLog extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final int integrationId;

  @HiveField(2)
  final String operation;

  @HiveField(3)
  final String direction; // 'inbound' أو 'outbound'

  @HiveField(4)
  final Map<String, dynamic> data;

  @HiveField(5)
  final bool success;

  @HiveField(6)
  final String? error;

  @HiveField(7)
  final DateTime timestamp;

  @HiveField(8)
  final int? responseCode;

  @HiveField(9)
  final String? responseMessage;

  SyncLog({
    required this.id,
    required this.integrationId,
    required this.operation,
    required this.direction,
    required this.data,
    required this.success,
    this.error,
    required this.timestamp,
    this.responseCode,
    this.responseMessage,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'integration_id': integrationId,
      'operation': operation,
      'direction': direction,
      'data': data,
      'success': success,
      'error': error,
      'timestamp': timestamp.toIso8601String(),
      'response_code': responseCode,
      'response_message': responseMessage,
    };
  }

  /// إنشاء من JSON
  factory SyncLog.fromJson(Map<String, dynamic> json) {
    return SyncLog(
      id: json['id'] ?? 0,
      integrationId: json['integration_id'] ?? 0,
      operation: json['operation'] ?? '',
      direction: json['direction'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      success: json['success'] ?? false,
      error: json['error'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      responseCode: json['response_code'],
      responseMessage: json['response_message'],
    );
  }
}

/// نموذج تكوين التكامل المسبق
class IntegrationTemplate {
  final String name;
  final String description;
  final IntegrationType type;
  final String baseUrl;
  final Map<String, String> defaultHeaders;
  final Map<String, dynamic> defaultConfiguration;
  final List<String> supportedOperations;
  final String? documentationUrl;

  IntegrationTemplate({
    required this.name,
    required this.description,
    required this.type,
    required this.baseUrl,
    required this.defaultHeaders,
    required this.defaultConfiguration,
    required this.supportedOperations,
    this.documentationUrl,
  });

  /// تحويل إلى تكامل فعلي
  APIIntegration toIntegration({
    required int id,
    required String apiKey,
    String? secretKey,
  }) {
    return APIIntegration(
      id: id,
      name: name,
      description: description,
      type: type,
      baseUrl: baseUrl,
      apiKey: apiKey,
      secretKey: secretKey,
      headers: defaultHeaders,
      configuration: defaultConfiguration,
      supportedOperations: supportedOperations,
      createdAt: DateTime.now(),
    );
  }

  /// قوالب التكامل المسبقة
  static List<IntegrationTemplate> get predefinedTemplates => [
    // تكامل مع WooCommerce
    IntegrationTemplate(
      name: 'WooCommerce',
      description: 'تكامل مع متجر WooCommerce',
      type: IntegrationType.ecommerce,
      baseUrl: 'https://yourstore.com/wp-json/wc/v3/',
      defaultHeaders: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      defaultConfiguration: {
        'auto_sync': true,
        'sync_interval': 300, // 5 دقائق
        'sync_products': true,
        'sync_orders': true,
        'sync_customers': true,
      },
      supportedOperations: [
        'sync_products',
        'sync_orders',
        'sync_customers',
        'update_inventory',
      ],
      documentationUrl: 'https://woocommerce.github.io/woocommerce-rest-api-docs/',
    ),

    // تكامل مع PayPal
    IntegrationTemplate(
      name: 'PayPal',
      description: 'تكامل مع بوابة دفع PayPal',
      type: IntegrationType.payment,
      baseUrl: 'https://api.paypal.com/v1/',
      defaultHeaders: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      defaultConfiguration: {
        'environment': 'sandbox', // أو 'live'
        'currency': 'USD',
        'auto_capture': true,
      },
      supportedOperations: [
        'create_payment',
        'capture_payment',
        'refund_payment',
        'get_payment_status',
      ],
      documentationUrl: 'https://developer.paypal.com/docs/api/overview/',
    ),

    // تكامل مخصص
    IntegrationTemplate(
      name: 'تكامل مخصص',
      description: 'تكامل مخصص مع API خارجي',
      type: IntegrationType.custom,
      baseUrl: 'https://api.example.com/v1/',
      defaultHeaders: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      defaultConfiguration: {
        'timeout': 30,
        'retry_count': 3,
        'auto_sync': false,
      },
      supportedOperations: [
        'custom_operation',
      ],
    ),
  ];
}
