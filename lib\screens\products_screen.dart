import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/product.dart';
import '../providers/product_provider.dart';
import '../widgets/add_edit_product_dialog.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key}); //  إضافة Key

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  @override
  void initState() {
    super.initState();
    //  جلب المنتجات عند تهيئة الشاشة إذا لم يتم جلبها من قبل
    //  يمكن تحسين هذا عن طريق التحقق إذا كانت القائمة فارغة فقط
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).fetchProducts();
    });
  }

  Future<void> _deleteProduct(BuildContext context, int productId) async {
    try {
      // استخراج المراجع قبل العملية async
      final provider = Provider.of<ProductProvider>(context, listen: false);
      final messenger = ScaffoldMessenger.of(context);

      await provider.deleteProduct(productId);
      if (mounted) {
        messenger.showSnackBar(
          const SnackBar(content: Text('تم حذف المنتج بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(content: Text('فشل حذف المنتج: ${e.toString()}')),
        );
      }
    }
  }

  void _showAddEditProductDialog({Product? product}) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        //  تمرير context الأصلي إذا لزم الأمر لـ Provider
        return AddEditProductDialog(product: product);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المنتجات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEditProductDialog(),
          ),
        ],
      ),
      body: Consumer<ProductProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.products.isEmpty) {
            //  إظهار مؤشر التحميل فقط إذا كانت القائمة فارغة ويتم التحميل
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error.isNotEmpty && provider.products.isEmpty) {
            //  إظهار الخطأ فقط إذا كانت القائمة فارغة
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(provider.error),
                  ElevatedButton(
                    onPressed: () => provider.fetchProducts(),
                    child: const Text('إعادة المحاولة'),
                  )
                ],
              ),
            );
          }

          if (provider.products.isEmpty) {
            return Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('لا توجد منتجات متاحة. قم بإضافة منتج جديد.'),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                  onPressed: () => _showAddEditProductDialog(),
                )
              ],
            ));
          }

          return RefreshIndicator(
            onRefresh: () => provider.fetchProducts(),
            child: ListView.builder(
              itemCount: provider.products.length,
              itemBuilder: (context, index) {
                final product = provider.products[index];
                return ProductListItem(
                  product: product,
                  onEdit: () => _showAddEditProductDialog(product: product),
                  onDelete: () =>
                      _deleteProduct(context, product.id), //  تمرير context
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class ProductListItem extends StatelessWidget {
  final Product product;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const ProductListItem({
    super.key,
    required this.product,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final isLowStock = product.quantity <= product.minQuantity;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        title: Text(product.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الكمية: ${product.quantity}'),
            if (isLowStock)
              const Text(
                'تنبيه: الكمية منخفضة',
                style: TextStyle(color: Colors.red),
              ),
            Text('سعر البيع: ${product.sellingPriceSyp} ل.س'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }
}
