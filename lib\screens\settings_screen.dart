import 'package:flutter/material.dart';
import '../services/offline_database_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _dollarRateController;
  bool _isDarkMode = false;
  String _language = 'ar'; // القيمة الافتراضية للغة
  late SharedPreferences _prefs;

  @override
  void initState() {
    super.initState();
    _dollarRateController = TextEditingController();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = _prefs.getBool('darkMode') ?? false;
      _language = _prefs.getString('language') ?? 'ar';
      // جلب سعر الصرف الحالي من الخادم إذا كان متاحًا أو من التفضيلات المحلية
      // هذا الجزء يحتاج إلى نقطة نهاية API لجلب سعر الصرف الحالي
      // حاليًا، سنستخدم القيمة المحفوظة محليًا فقط
      _dollarRateController.text = _prefs.getString('dollarRate') ?? '';
    });
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // تحديث سعر صرف الدولار محلياً
      final newRate = double.parse(_dollarRateController.text);
      await OfflineDatabaseService.saveSetting('dollar_rate', newRate);
      await OfflineDatabaseService.saveDollarRate(newRate);

      // حفظ الإعدادات محلياً
      await _prefs.setBool('darkMode', _isDarkMode);
      await _prefs.setString('language', _language);
      await _prefs.setString('dollarRate', _dollarRateController.text);

      //  تطبيق التغييرات على الفور (مثلاً تغيير المظهر)
      //  هذا الجزء يعتمد على كيفية إدارة الحالة في التطبيق (Provider, Riverpod, etc.)
      //  مثال بسيط لتغيير المظهر:
      //  MyApp.of(context).setThemeMode(_isDarkMode ? ThemeMode.dark : ThemeMode.light);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ الإعدادات بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء حفظ الإعدادات: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'سعر صرف الدولار',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _dollarRateController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      decoration: InputDecoration(
                        labelText: 'سعر الدولار بالليرة السورية',
                        hintText: 'مثال: 13500.50',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.attach_money),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال سعر الصرف';
                        }
                        if (double.tryParse(value) == null) {
                          return 'الرجاء إدخال رقم صحيح';
                        }
                        if (double.parse(value) <= 0) {
                          return 'يجب أن يكون سعر الصرف أكبر من الصفر';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المظهر',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    SwitchListTile(
                      title: const Text('الوضع الداكن'),
                      value: _isDarkMode,
                      onChanged: (value) {
                        setState(() => _isDarkMode = value);
                      },
                      secondary: Icon(
                          _isDarkMode ? Icons.dark_mode : Icons.light_mode),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اللغة',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    //  يمكن إضافة المزيد من خيارات اللغة هنا
                    RadioListTile<String>(
                      title: const Text('العربية'),
                      value: 'ar',
                      groupValue: _language,
                      onChanged: (String? value) {
                        if (value != null) {
                          setState(() => _language = value);
                          //  هنا يمكنك إضافة منطق تغيير لغة التطبيق فعليًا
                          //  مثلاً باستخدام flutter_localizations و Provider
                        }
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('English'),
                      value: 'en',
                      groupValue: _language,
                      onChanged: (String? value) {
                        if (value != null) {
                          setState(() => _language = value);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: _saveSettings,
              child: const Text('حفظ الإعدادات'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _dollarRateController.dispose();
    super.dispose();
  }
}
