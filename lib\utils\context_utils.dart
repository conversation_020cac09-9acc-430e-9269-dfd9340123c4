import 'package:flutter/material.dart';

/// أدوات مساعدة للتعامل مع BuildContext بشكل آمن
class ContextUtils {
  /// عرض SnackBar بشكل آمن
  static void showSnackBarSafely(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
    bool mounted = true,
  }) {
    if (!mounted) return;
    
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
          duration: duration,
        ),
      );
    } catch (e) {
      debugPrint('فشل في عرض SnackBar: $e');
    }
  }

  /// التنقل بشكل آمن
  static void navigateSafely(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool mounted = true,
  }) {
    if (!mounted) return;
    
    try {
      Navigator.of(context).pushNamed(routeName, arguments: arguments);
    } catch (e) {
      debugPrint('فشل في التنقل: $e');
    }
  }

  /// التنقل مع الاستبدال بشكل آمن
  static void navigateReplacementSafely(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool mounted = true,
  }) {
    if (!mounted) return;
    
    try {
      Navigator.of(context).pushReplacementNamed(routeName, arguments: arguments);
    } catch (e) {
      debugPrint('فشل في التنقل مع الاستبدال: $e');
    }
  }

  /// العودة بشكل آمن
  static void popSafely(
    BuildContext context, {
    Object? result,
    bool mounted = true,
  }) {
    if (!mounted) return;
    
    try {
      Navigator.of(context).pop(result);
    } catch (e) {
      debugPrint('فشل في العودة: $e');
    }
  }

  /// عرض حوار بشكل آمن
  static Future<T?> showDialogSafely<T>(
    BuildContext context,
    Widget dialog, {
    bool barrierDismissible = true,
    bool mounted = true,
  }) async {
    if (!mounted) return null;
    
    try {
      return await showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => dialog,
      );
    } catch (e) {
      debugPrint('فشل في عرض الحوار: $e');
      return null;
    }
  }

  /// عرض BottomSheet بشكل آمن
  static Future<T?> showBottomSheetSafely<T>(
    BuildContext context,
    Widget bottomSheet, {
    bool mounted = true,
  }) async {
    if (!mounted) return null;
    
    try {
      return await showModalBottomSheet<T>(
        context: context,
        builder: (context) => bottomSheet,
      );
    } catch (e) {
      debugPrint('فشل في عرض BottomSheet: $e');
      return null;
    }
  }

  /// فحص إذا كان Widget ما زال mounted
  static bool isMounted(State state) {
    return state.mounted;
  }

  /// تنفيذ عملية مع فحص mounted
  static void executeIfMounted(State state, VoidCallback callback) {
    if (state.mounted) {
      callback();
    }
  }

  /// تنفيذ عملية async مع فحص mounted
  static Future<void> executeAsyncIfMounted(
    State state,
    Future<void> Function() callback,
  ) async {
    if (state.mounted) {
      await callback();
    }
  }
}
