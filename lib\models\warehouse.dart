import 'package:hive/hive.dart';

part 'warehouse.g.dart';

/// نموذج المستودع
@HiveType(typeId: 10)
class Warehouse extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String code;

  @HiveField(3)
  final String location;

  @HiveField(4)
  final String? description;

  @HiveField(5)
  final String manager;

  @HiveField(6)
  final String? phone;

  @HiveField(7)
  final String? email;

  @HiveField(8)
  final bool isActive;

  @HiveField(9)
  final bool isMainWarehouse;

  @HiveField(10)
  final double? maxCapacity;

  @HiveField(11)
  final String? address;

  @HiveField(12)
  final DateTime createdAt;

  @HiveField(13)
  final DateTime? updatedAt;

  Warehouse({
    required this.id,
    required this.name,
    required this.code,
    required this.location,
    this.description,
    required this.manager,
    this.phone,
    this.email,
    this.isActive = true,
    this.isMainWarehouse = false,
    this.maxCapacity,
    this.address,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نسخة محدثة من المستودع
  Warehouse copyWith({
    int? id,
    String? name,
    String? code,
    String? location,
    String? description,
    String? manager,
    String? phone,
    String? email,
    bool? isActive,
    bool? isMainWarehouse,
    double? maxCapacity,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Warehouse(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      location: location ?? this.location,
      description: description ?? this.description,
      manager: manager ?? this.manager,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      isActive: isActive ?? this.isActive,
      isMainWarehouse: isMainWarehouse ?? this.isMainWarehouse,
      maxCapacity: maxCapacity ?? this.maxCapacity,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'location': location,
      'description': description,
      'manager': manager,
      'phone': phone,
      'email': email,
      'is_active': isActive,
      'is_main_warehouse': isMainWarehouse,
      'max_capacity': maxCapacity,
      'address': address,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory Warehouse.fromJson(Map<String, dynamic> json) {
    return Warehouse(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      location: json['location'] ?? '',
      description: json['description'],
      manager: json['manager'] ?? '',
      phone: json['phone'],
      email: json['email'],
      isActive: json['is_active'] ?? true,
      isMainWarehouse: json['is_main_warehouse'] ?? false,
      maxCapacity: json['max_capacity']?.toDouble(),
      address: json['address'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  @override
  String toString() {
    return 'Warehouse(id: $id, name: $name, code: $code, location: $location, manager: $manager, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Warehouse && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج مخزون المنتج في المستودع
@HiveType(typeId: 11)
class ProductWarehouseStock extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final int productId;

  @HiveField(2)
  final int warehouseId;

  @HiveField(3)
  final int quantity;

  @HiveField(4)
  final int reservedQuantity;

  @HiveField(5)
  final int availableQuantity;

  @HiveField(6)
  final String? location; // رقم الرف/الموقع داخل المستودع

  @HiveField(7)
  final int? minQuantity;

  @HiveField(8)
  final int? maxQuantity;

  @HiveField(9)
  final DateTime lastUpdated;

  @HiveField(10)
  final DateTime? lastStockTake; // آخر جرد

  ProductWarehouseStock({
    required this.id,
    required this.productId,
    required this.warehouseId,
    required this.quantity,
    this.reservedQuantity = 0,
    required this.availableQuantity,
    this.location,
    this.minQuantity,
    this.maxQuantity,
    required this.lastUpdated,
    this.lastStockTake,
  });

  /// إنشاء نسخة محدثة
  ProductWarehouseStock copyWith({
    int? id,
    int? productId,
    int? warehouseId,
    int? quantity,
    int? reservedQuantity,
    int? availableQuantity,
    String? location,
    int? minQuantity,
    int? maxQuantity,
    DateTime? lastUpdated,
    DateTime? lastStockTake,
  }) {
    return ProductWarehouseStock(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      quantity: quantity ?? this.quantity,
      reservedQuantity: reservedQuantity ?? this.reservedQuantity,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      location: location ?? this.location,
      minQuantity: minQuantity ?? this.minQuantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      lastStockTake: lastStockTake ?? this.lastStockTake,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'quantity': quantity,
      'reserved_quantity': reservedQuantity,
      'available_quantity': availableQuantity,
      'location': location,
      'min_quantity': minQuantity,
      'max_quantity': maxQuantity,
      'last_updated': lastUpdated.toIso8601String(),
      'last_stock_take': lastStockTake?.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory ProductWarehouseStock.fromJson(Map<String, dynamic> json) {
    return ProductWarehouseStock(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      warehouseId: json['warehouse_id'] ?? 0,
      quantity: json['quantity'] ?? 0,
      reservedQuantity: json['reserved_quantity'] ?? 0,
      availableQuantity: json['available_quantity'] ?? 0,
      location: json['location'],
      minQuantity: json['min_quantity'],
      maxQuantity: json['max_quantity'],
      lastUpdated: DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
      lastStockTake: json['last_stock_take'] != null ? DateTime.parse(json['last_stock_take']) : null,
    );
  }

  /// فحص إذا كان المخزون منخفض
  bool isLowStock() {
    if (minQuantity == null) return false;
    return availableQuantity <= minQuantity!;
  }

  /// فحص إذا كان المخزون نفد
  bool isOutOfStock() {
    return availableQuantity <= 0;
  }

  /// فحص إذا كان المخزون ممتلئ
  bool isOverStock() {
    if (maxQuantity == null) return false;
    return quantity >= maxQuantity!;
  }

  @override
  String toString() {
    return 'ProductWarehouseStock(id: $id, productId: $productId, warehouseId: $warehouseId, quantity: $quantity, available: $availableQuantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductWarehouseStock && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
