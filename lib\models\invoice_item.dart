class InvoiceItem {
  final String id;
  final String productId;
  final String productName;
  final String productBarcode;
  final double unitPriceSyp;
  final double unitPriceUsd;
  final int quantity;
  final double totalSyp;
  final double totalUsd;
  final String notes;

  /// السعر الافتراضي (بالليرة السورية)
  double get price => unitPriceSyp;

  InvoiceItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productBarcode,
    required this.unitPriceSyp,
    required this.unitPriceUsd,
    required this.quantity,
    required this.totalSyp,
    required this.totalUsd,
    this.notes = '',
  });

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'] ?? '',
      productId: json['product_id'] ?? '',
      productName: json['product_name'] ?? '',
      productBarcode: json['product_barcode'] ?? '',
      unitPriceSyp: (json['unit_price_syp'] ?? 0).toDouble(),
      unitPriceUsd: (json['unit_price_usd'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      totalSyp: (json['total_syp'] ?? 0).toDouble(),
      totalUsd: (json['total_usd'] ?? 0).toDouble(),
      notes: json['notes'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'product_barcode': productBarcode,
      'unit_price_syp': unitPriceSyp,
      'unit_price_usd': unitPriceUsd,
      'quantity': quantity,
      'total_syp': totalSyp,
      'total_usd': totalUsd,
      'notes': notes,
    };
  }

  InvoiceItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productBarcode,
    double? unitPriceSyp,
    double? unitPriceUsd,
    int? quantity,
    double? totalSyp,
    double? totalUsd,
    String? notes,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productBarcode: productBarcode ?? this.productBarcode,
      unitPriceSyp: unitPriceSyp ?? this.unitPriceSyp,
      unitPriceUsd: unitPriceUsd ?? this.unitPriceUsd,
      quantity: quantity ?? this.quantity,
      totalSyp: totalSyp ?? this.totalSyp,
      totalUsd: totalUsd ?? this.totalUsd,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'InvoiceItem(id: $id, productName: $productName, quantity: $quantity, totalSyp: $totalSyp)';
  }
}
