import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_system/widgets/brand_footer.dart';

void main() {
  group('BrandFooter Widget Tests', () {
    testWidgets('should display brand name correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BrandFooter(),
          ),
        ),
      );

      // Assert
      expect(find.text('<PERSON>'), findsOneWidget);
    });

    testWidgets('should display copyright symbol', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BrandFooter(),
          ),
        ),
      );

      // Assert
      expect(find.textContaining('®'), findsOneWidget);
    });

    testWidgets('should have proper styling', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: <PERSON><PERSON>ooter(),
          ),
        ),
      );

      // Assert
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsWidgets);

      final textFinder = find.text('<PERSON> <PERSON> Nouh ®');
      expect(textFinder, findsOneWidget);
    });

    testWidgets('should be positioned at bottom', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                Expanded(child: SizedBox()),
                BrandFooter(),
              ],
            ),
          ),
        ),
      );

      // Assert
      final brandFooter = find.byType(BrandFooter);
      expect(brandFooter, findsOneWidget);
    });

    testWidgets('should handle different screen sizes', (WidgetTester tester) async {
      // Arrange - Test with small screen
      await tester.binding.setSurfaceSize(const Size(300, 600));
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BrandFooter(),
          ),
        ),
      );

      // Assert
      expect(find.text('Ali Ahmad Nouh ®'), findsOneWidget);

      // Arrange - Test with large screen
      await tester.binding.setSurfaceSize(const Size(800, 1200));
      await tester.pump();

      // Assert
      expect(find.text('Ali Ahmad Nouh ®'), findsOneWidget);
    });
  });
}
