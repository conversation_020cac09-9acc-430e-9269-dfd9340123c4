import 'package:hive/hive.dart';

part 'product_batch.g.dart';

/// حالة الدفعة
@HiveType(typeId: 14)
enum BatchStatus {
  @HiveField(0)
  active,    // نشط

  @HiveField(1)
  expired,   // منتهي الصلاحية

  @HiveField(2)
  recalled,  // مسحوب

  @HiveField(3)
  sold,      // مباع بالكامل

  @HiveField(4)
  damaged,   // تالف

  @HiveField(5)
  quarantine, // في الحجر الصحي
}

/// نموذج دفعة المنتج
@HiveType(typeId: 15)
class ProductBatch extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String batchNumber;

  @HiveField(2)
  final int productId;

  @HiveField(3)
  final int warehouseId;

  @HiveField(4)
  final int originalQuantity;

  @HiveField(5)
  final int currentQuantity;

  @HiveField(6)
  final int availableQuantity;

  @HiveField(7)
  final int reservedQuantity;

  @HiveField(8)
  final int soldQuantity;

  @HiveField(9)
  final double costPrice;

  @HiveField(10)
  final double? sellingPrice;

  @HiveField(11)
  final DateTime manufacturingDate;

  @HiveField(12)
  final DateTime? expiryDate;

  @HiveField(13)
  final String? supplier;

  @HiveField(14)
  final String? supplierBatchNumber;

  @HiveField(15)
  final String? location; // موقع الدفعة في المستودع

  @HiveField(16)
  final BatchStatus status;

  @HiveField(17)
  final String? notes;

  @HiveField(18)
  final DateTime createdAt;

  @HiveField(19)
  final DateTime? updatedAt;

  @HiveField(20)
  final String? qrCode; // رمز QR للدفعة

  @HiveField(21)
  final Map<String, dynamic>? customFields; // حقول مخصصة

  ProductBatch({
    required this.id,
    required this.batchNumber,
    required this.productId,
    required this.warehouseId,
    required this.originalQuantity,
    required this.currentQuantity,
    required this.availableQuantity,
    this.reservedQuantity = 0,
    this.soldQuantity = 0,
    required this.costPrice,
    this.sellingPrice,
    required this.manufacturingDate,
    this.expiryDate,
    this.supplier,
    this.supplierBatchNumber,
    this.location,
    this.status = BatchStatus.active,
    this.notes,
    required this.createdAt,
    this.updatedAt,
    this.qrCode,
    this.customFields,
  });

  /// إنشاء نسخة محدثة
  ProductBatch copyWith({
    int? id,
    String? batchNumber,
    int? productId,
    int? warehouseId,
    int? originalQuantity,
    int? currentQuantity,
    int? availableQuantity,
    int? reservedQuantity,
    int? soldQuantity,
    double? costPrice,
    double? sellingPrice,
    DateTime? manufacturingDate,
    DateTime? expiryDate,
    String? supplier,
    String? supplierBatchNumber,
    String? location,
    BatchStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? qrCode,
    Map<String, dynamic>? customFields,
  }) {
    return ProductBatch(
      id: id ?? this.id,
      batchNumber: batchNumber ?? this.batchNumber,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      originalQuantity: originalQuantity ?? this.originalQuantity,
      currentQuantity: currentQuantity ?? this.currentQuantity,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      reservedQuantity: reservedQuantity ?? this.reservedQuantity,
      soldQuantity: soldQuantity ?? this.soldQuantity,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      manufacturingDate: manufacturingDate ?? this.manufacturingDate,
      expiryDate: expiryDate ?? this.expiryDate,
      supplier: supplier ?? this.supplier,
      supplierBatchNumber: supplierBatchNumber ?? this.supplierBatchNumber,
      location: location ?? this.location,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      qrCode: qrCode ?? this.qrCode,
      customFields: customFields ?? this.customFields,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'batch_number': batchNumber,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'original_quantity': originalQuantity,
      'current_quantity': currentQuantity,
      'available_quantity': availableQuantity,
      'reserved_quantity': reservedQuantity,
      'sold_quantity': soldQuantity,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'manufacturing_date': manufacturingDate.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'supplier': supplier,
      'supplier_batch_number': supplierBatchNumber,
      'location': location,
      'status': status.toString().split('.').last,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'qr_code': qrCode,
      'custom_fields': customFields,
    };
  }

  /// إنشاء من JSON
  factory ProductBatch.fromJson(Map<String, dynamic> json) {
    return ProductBatch(
      id: json['id'] ?? 0,
      batchNumber: json['batch_number'] ?? '',
      productId: json['product_id'] ?? 0,
      warehouseId: json['warehouse_id'] ?? 0,
      originalQuantity: json['original_quantity'] ?? 0,
      currentQuantity: json['current_quantity'] ?? 0,
      availableQuantity: json['available_quantity'] ?? 0,
      reservedQuantity: json['reserved_quantity'] ?? 0,
      soldQuantity: json['sold_quantity'] ?? 0,
      costPrice: (json['cost_price'] ?? 0.0).toDouble(),
      sellingPrice: json['selling_price']?.toDouble(),
      manufacturingDate: DateTime.parse(json['manufacturing_date'] ?? DateTime.now().toIso8601String()),
      expiryDate: json['expiry_date'] != null ? DateTime.parse(json['expiry_date']) : null,
      supplier: json['supplier'],
      supplierBatchNumber: json['supplier_batch_number'],
      location: json['location'],
      status: _parseStatus(json['status']),
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      qrCode: json['qr_code'],
      customFields: json['custom_fields'] != null ? Map<String, dynamic>.from(json['custom_fields']) : null,
    );
  }

  /// تحليل حالة الدفعة من النص
  static BatchStatus _parseStatus(String? statusString) {
    switch (statusString) {
      case 'active':
        return BatchStatus.active;
      case 'expired':
        return BatchStatus.expired;
      case 'recalled':
        return BatchStatus.recalled;
      case 'sold':
        return BatchStatus.sold;
      case 'damaged':
        return BatchStatus.damaged;
      case 'quarantine':
        return BatchStatus.quarantine;
      default:
        return BatchStatus.active;
    }
  }

  /// الحصول على وصف الحالة بالعربية
  String get statusDescription {
    switch (status) {
      case BatchStatus.active:
        return 'نشط';
      case BatchStatus.expired:
        return 'منتهي الصلاحية';
      case BatchStatus.recalled:
        return 'مسحوب';
      case BatchStatus.sold:
        return 'مباع';
      case BatchStatus.damaged:
        return 'تالف';
      case BatchStatus.quarantine:
        return 'في الحجر الصحي';
    }
  }

  /// فحص إذا كانت الدفعة منتهية الصلاحية
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// فحص إذا كانت الدفعة ستنتهي صلاحيتها قريباً
  bool isExpiringWithin(int days) {
    if (expiryDate == null) return false;
    final warningDate = DateTime.now().add(Duration(days: days));
    return expiryDate!.isBefore(warningDate) && !isExpired;
  }

  /// الحصول على عدد الأيام المتبقية للانتهاء
  int? get daysUntilExpiry {
    if (expiryDate == null) return null;
    final difference = expiryDate!.difference(DateTime.now()).inDays;
    return difference < 0 ? 0 : difference;
  }

  /// فحص إذا كانت الدفعة متاحة للبيع
  bool get isAvailableForSale {
    return status == BatchStatus.active && 
           availableQuantity > 0 && 
           !isExpired;
  }

  /// حساب نسبة المباع
  double get soldPercentage {
    if (originalQuantity == 0) return 0;
    return (soldQuantity / originalQuantity) * 100;
  }

  /// حساب القيمة الإجمالية للدفعة
  double get totalValue {
    return currentQuantity * costPrice;
  }

  /// حساب القيمة المتاحة للبيع
  double get availableValue {
    return availableQuantity * (sellingPrice ?? costPrice);
  }

  @override
  String toString() {
    return 'ProductBatch(id: $id, batchNumber: $batchNumber, productId: $productId, currentQuantity: $currentQuantity, status: $statusDescription)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductBatch && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
