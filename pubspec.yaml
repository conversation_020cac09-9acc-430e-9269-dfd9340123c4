name: invoice_system
description: A Flutter invoice and inventory management system.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  http: ^1.1.0
  shared_preferences: ^2.2.2
  cupertino_icons: ^1.0.2
  fl_chart: ^0.68.0
  intl: ^0.19.0
  # Local Notifications Only
  flutter_local_notifications: ^16.3.2
  # Offline Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  # UI/UX
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  # Removed connectivity_plus - Full offline mode
  # Security
  crypto: ^3.0.3
  # File System
  path_provider: ^2.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
