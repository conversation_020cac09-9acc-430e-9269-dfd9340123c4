import 'package:hive/hive.dart';

part 'stock_movement.g.dart';

/// أنواع حركات المخزون
@HiveType(typeId: 12)
enum StockMovementType {
  @HiveField(0)
  purchase, // شراء

  @HiveField(1)
  sale, // بيع

  @HiveField(2)
  adjustment, // تسوية

  @HiveField(3)
  transfer, // نقل بين المستودعات

  @HiveField(4)
  returnIn, // إرجاع وارد

  @HiveField(5)
  returnOut, // إرجاع صادر

  @HiveField(6)
  damage, // تلف

  @HiveField(7)
  expired, // انتهاء صلاحية

  @HiveField(8)
  stocktake, // جرد

  @HiveField(9)
  production, // إنتاج

  @HiveField(10)
  consumption, // استهلاك
}

/// نموذج حركة المخزون
@HiveType(typeId: 13)
class StockMovement extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final int productId;

  @HiveField(2)
  final int warehouseId;

  @HiveField(3)
  final StockMovementType type;

  @HiveField(4)
  final int quantity;

  @HiveField(5)
  final int quantityBefore;

  @HiveField(6)
  final int quantityAfter;

  @HiveField(7)
  final double? unitCost;

  @HiveField(8)
  final double? totalCost;

  @HiveField(9)
  final String reference; // مرجع العملية (رقم فاتورة، رقم أمر نقل، إلخ)

  @HiveField(10)
  final String? notes;

  @HiveField(11)
  final String? batchNumber;

  @HiveField(12)
  final DateTime? expiryDate;

  @HiveField(13)
  final int? fromWarehouseId; // في حالة النقل

  @HiveField(14)
  final int? toWarehouseId; // في حالة النقل

  @HiveField(15)
  final String userId; // المستخدم الذي قام بالعملية

  @HiveField(16)
  final DateTime createdAt;

  @HiveField(17)
  final bool isApproved;

  @HiveField(18)
  final String? approvedBy;

  @HiveField(19)
  final DateTime? approvedAt;

  StockMovement({
    required this.id,
    required this.productId,
    required this.warehouseId,
    required this.type,
    required this.quantity,
    required this.quantityBefore,
    required this.quantityAfter,
    this.unitCost,
    this.totalCost,
    required this.reference,
    this.notes,
    this.batchNumber,
    this.expiryDate,
    this.fromWarehouseId,
    this.toWarehouseId,
    required this.userId,
    required this.createdAt,
    this.isApproved = true,
    this.approvedBy,
    this.approvedAt,
  });

  /// إنشاء نسخة محدثة
  StockMovement copyWith({
    int? id,
    int? productId,
    int? warehouseId,
    StockMovementType? type,
    int? quantity,
    int? quantityBefore,
    int? quantityAfter,
    double? unitCost,
    double? totalCost,
    String? reference,
    String? notes,
    String? batchNumber,
    DateTime? expiryDate,
    int? fromWarehouseId,
    int? toWarehouseId,
    String? userId,
    DateTime? createdAt,
    bool? isApproved,
    String? approvedBy,
    DateTime? approvedAt,
  }) {
    return StockMovement(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      type: type ?? this.type,
      quantity: quantity ?? this.quantity,
      quantityBefore: quantityBefore ?? this.quantityBefore,
      quantityAfter: quantityAfter ?? this.quantityAfter,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      reference: reference ?? this.reference,
      notes: notes ?? this.notes,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      fromWarehouseId: fromWarehouseId ?? this.fromWarehouseId,
      toWarehouseId: toWarehouseId ?? this.toWarehouseId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      isApproved: isApproved ?? this.isApproved,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'type': type.toString().split('.').last,
      'quantity': quantity,
      'quantity_before': quantityBefore,
      'quantity_after': quantityAfter,
      'unit_cost': unitCost,
      'total_cost': totalCost,
      'reference': reference,
      'notes': notes,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      'from_warehouse_id': fromWarehouseId,
      'to_warehouse_id': toWarehouseId,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'is_approved': isApproved,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory StockMovement.fromJson(Map<String, dynamic> json) {
    return StockMovement(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      warehouseId: json['warehouse_id'] ?? 0,
      type: _parseMovementType(json['type']),
      quantity: json['quantity'] ?? 0,
      quantityBefore: json['quantity_before'] ?? 0,
      quantityAfter: json['quantity_after'] ?? 0,
      unitCost: json['unit_cost']?.toDouble(),
      totalCost: json['total_cost']?.toDouble(),
      reference: json['reference'] ?? '',
      notes: json['notes'],
      batchNumber: json['batch_number'],
      expiryDate: json['expiry_date'] != null
          ? DateTime.parse(json['expiry_date'])
          : null,
      fromWarehouseId: json['from_warehouse_id'],
      toWarehouseId: json['to_warehouse_id'],
      userId: json['user_id'] ?? '',
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      isApproved: json['is_approved'] ?? true,
      approvedBy: json['approved_by'],
      approvedAt: json['approved_at'] != null
          ? DateTime.parse(json['approved_at'])
          : null,
    );
  }

  /// تحليل نوع الحركة من النص
  static StockMovementType _parseMovementType(String? typeString) {
    switch (typeString) {
      case 'purchase':
        return StockMovementType.purchase;
      case 'sale':
        return StockMovementType.sale;
      case 'adjustment':
        return StockMovementType.adjustment;
      case 'transfer':
        return StockMovementType.transfer;
      case 'return_in':
        return StockMovementType.returnIn;
      case 'return_out':
        return StockMovementType.returnOut;
      case 'damage':
        return StockMovementType.damage;
      case 'expired':
        return StockMovementType.expired;
      case 'stocktake':
        return StockMovementType.stocktake;
      case 'production':
        return StockMovementType.production;
      case 'consumption':
        return StockMovementType.consumption;
      default:
        return StockMovementType.adjustment;
    }
  }

  /// الحصول على وصف نوع الحركة بالعربية
  String get typeDescription {
    switch (type) {
      case StockMovementType.purchase:
        return 'شراء';
      case StockMovementType.sale:
        return 'بيع';
      case StockMovementType.adjustment:
        return 'تسوية';
      case StockMovementType.transfer:
        return 'نقل';
      case StockMovementType.returnIn:
        return 'إرجاع وارد';
      case StockMovementType.returnOut:
        return 'إرجاع صادر';
      case StockMovementType.damage:
        return 'تلف';
      case StockMovementType.expired:
        return 'انتهاء صلاحية';
      case StockMovementType.stocktake:
        return 'جرد';
      case StockMovementType.production:
        return 'إنتاج';
      case StockMovementType.consumption:
        return 'استهلاك';
    }
  }

  /// فحص إذا كانت الحركة تزيد المخزون
  bool get isInbound {
    return [
      StockMovementType.purchase,
      StockMovementType.returnIn,
      StockMovementType.production,
    ].contains(type);
  }

  /// فحص إذا كانت الحركة تقلل المخزون
  bool get isOutbound {
    return [
      StockMovementType.sale,
      StockMovementType.returnOut,
      StockMovementType.damage,
      StockMovementType.expired,
      StockMovementType.consumption,
    ].contains(type);
  }

  /// فحص إذا كانت الحركة نقل
  bool get isTransfer {
    return type == StockMovementType.transfer;
  }

  @override
  String toString() {
    return 'StockMovement(id: $id, productId: $productId, type: $typeDescription, quantity: $quantity, reference: $reference)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockMovement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
