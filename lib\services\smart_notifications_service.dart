import 'package:flutter/foundation.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';
import '../services/ai_insights_service.dart';

/// خدمة الإشعارات الذكية المتقدمة
class SmartNotificationsService {
  static const String _notificationsBox = 'smart_notifications';
  static const String _settingsBox = 'notification_settings';

  /// تهيئة خدمة الإشعارات الذكية
  static Future<void> initialize() async {
    try {
      // تعيين إعدادات افتراضية
      await _setDefaultSettings();

      // بدء المراقبة الذكية
      await _startSmartMonitoring();

      debugPrint('✅ تم تهيئة خدمة الإشعارات الذكية');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة الإشعارات الذكية: $e');
    }
  }

  /// إرسال إشعارات ذكية بناءً على الرؤى
  static Future<void> sendSmartNotifications() async {
    try {
      final settings = await _getNotificationSettings();
      if (!settings['enabled']) return;

      final insights = await AIInsightsService.generateBusinessInsights();

      for (var insight in insights) {
        if (insight.priority >= settings['min_priority']) {
          await _sendInsightNotification(insight);
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في إرسال الإشعارات الذكية: $e');
    }
  }

  /// إشعار تحديث سعر الدولار الذكي
  static Future<void> sendDollarRateAlert({
    required double oldRate,
    required double newRate,
    bool isAutoUpdate = false,
  }) async {
    try {
      final changePercent = ((newRate - oldRate) / oldRate) * 100;
      final isIncrease = newRate > oldRate;

      String title;
      String body;
      NotificationPriority priority;

      if (changePercent.abs() > 10) {
        // تغيير كبير
        title = isIncrease
            ? '📈 ارتفاع كبير في سعر الدولار'
            : '📉 انخفاض كبير في سعر الدولار';
        body =
            'تغير السعر بنسبة ${changePercent.abs().toStringAsFixed(1)}% من ${oldRate.toStringAsFixed(0)} إلى ${newRate.toStringAsFixed(0)}';
        priority = NotificationPriority.high;

        // إضافة توصيات
        if (isIncrease) {
          body += '\n💡 فكر في زيادة أسعار المنتجات المستوردة';
        } else {
          body += '\n💡 فرصة لتقليل أسعار المنتجات وزيادة المبيعات';
        }
      } else if (changePercent.abs() > 5) {
        // تغيير متوسط
        title = isIncrease
            ? '📊 ارتفاع في سعر الدولار'
            : '📊 انخفاض في سعر الدولار';
        body =
            'تغير السعر بنسبة ${changePercent.abs().toStringAsFixed(1)}% إلى ${newRate.toStringAsFixed(0)}';
        priority = NotificationPriority.medium;
      } else {
        // تغيير طفيف
        title = '💱 تحديث سعر الدولار';
        body = 'السعر الجديد: ${newRate.toStringAsFixed(0)} ل.س';
        priority = NotificationPriority.low;
      }

      if (isAutoUpdate) {
        title = '🤖 $title';
        body = 'تحديث تلقائي: $body';
      }

      await OfflineNotificationServiceExtension.showCustomNotification(
        title: title,
        body: body,
        priority: priority,
        category: NotificationCategory.currency,
        data: {
          'old_rate': oldRate,
          'new_rate': newRate,
          'change_percent': changePercent,
          'is_increase': isIncrease,
        },
      );

      // حفظ في سجل الإشعارات
      await _saveNotificationHistory(
        type: 'dollar_rate_update',
        title: title,
        body: body,
        data: {
          'old_rate': oldRate,
          'new_rate': newRate,
          'change_percent': changePercent,
        },
      );
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار سعر الدولار: $e');
    }
  }

  /// إشعارات المخزون الذكية
  static Future<void> sendInventoryAlerts() async {
    try {
      final productsData = await OfflineDatabaseService.getBoxData('products');

      int lowStockCount = 0;
      int outOfStockCount = 0;
      final criticalProducts = <String>[];

      for (var productData in productsData.values) {
        final product = Map<String, dynamic>.from(productData);
        final quantity = product['quantity'] ?? 0;
        final minQuantity = product['min_quantity'] ?? 0;
        final name = product['name'] ?? '';

        if (quantity == 0) {
          outOfStockCount++;
          criticalProducts.add(name);
        } else if (quantity <= minQuantity) {
          lowStockCount++;
        }
      }

      // إشعار المنتجات النافدة
      if (outOfStockCount > 0) {
        await OfflineNotificationServiceExtension.showCustomNotification(
          title: '🚨 منتجات نفدت من المخزون',
          body: '$outOfStockCount منتج نفد من المخزون ويحتاج إعادة تموين فورية',
          priority: NotificationPriority.high,
          category: NotificationCategory.inventory,
          data: {
            'out_of_stock_count': outOfStockCount,
            'critical_products': criticalProducts,
          },
        );
      }

      // إشعار المخزون المنخفض
      if (lowStockCount > 0) {
        await OfflineNotificationServiceExtension.showCustomNotification(
          title: '⚠️ مخزون منخفض',
          body: '$lowStockCount منتج يحتاج إعادة تموين قريباً',
          priority: NotificationPriority.medium,
          category: NotificationCategory.inventory,
          data: {
            'low_stock_count': lowStockCount,
          },
        );
      }
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعارات المخزون: $e');
    }
  }

  /// إشعارات المبيعات الذكية
  static Future<void> sendSalesAlerts() async {
    try {
      final invoicesData = await OfflineDatabaseService.getBoxData('invoices');
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      double todaySales = 0;
      int todayInvoicesCount = 0;

      for (var invoiceData in invoicesData.values) {
        final invoice = Map<String, dynamic>.from(invoiceData);
        final invoiceDate = DateTime.parse(invoice['date']);

        if (invoiceDate.isAfter(todayStart)) {
          todaySales += (invoice['total_amount_syp'] ?? 0.0);
          todayInvoicesCount++;
        }
      }

      // إشعار إنجاز يومي
      if (todayInvoicesCount > 0) {
        String message;
        NotificationPriority priority;

        if (todaySales > 500000) {
          // أكثر من 500 ألف
          message =
              '🎉 يوم ممتاز! حققت مبيعات ${(todaySales / 1000).toStringAsFixed(0)}K من $todayInvoicesCount فاتورة';
          priority = NotificationPriority.high;
        } else if (todaySales > 200000) {
          // أكثر من 200 ألف
          message =
              '👍 يوم جيد! مبيعات ${(todaySales / 1000).toStringAsFixed(0)}K من $todayInvoicesCount فاتورة';
          priority = NotificationPriority.medium;
        } else {
          message =
              '📊 مبيعات اليوم: ${(todaySales / 1000).toStringAsFixed(0)}K من $todayInvoicesCount فاتورة';
          priority = NotificationPriority.low;
        }

        await OfflineNotificationServiceExtension.showCustomNotification(
          title: 'تقرير المبيعات اليومي',
          body: message,
          priority: priority,
          category: NotificationCategory.sales,
          data: {
            'today_sales': todaySales,
            'today_invoices': todayInvoicesCount,
          },
        );
      }
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعارات المبيعات: $e');
    }
  }

  /// إشعارات العملاء الذكية
  static Future<void> sendCustomerAlerts() async {
    try {
      final customersData =
          await OfflineDatabaseService.getBoxData('customers');

      double totalDebt = 0;
      // int debtorsCount = 0;
      final highDebtCustomers = <String>[];

      for (var customerData in customersData.values) {
        final customer = Map<String, dynamic>.from(customerData);
        final debt = (customer['debt'] ?? 0.0).toDouble();
        final name = customer['name'] ?? '';

        if (debt > 0) {
          totalDebt += debt;
          // debtorsCount++;

          if (debt > 100000) {
            // دين أكثر من 100 ألف
            highDebtCustomers
                .add('$name (${(debt / 1000).toStringAsFixed(0)}K)');
          }
        }
      }

      // إشعار الديون المرتفعة
      if (highDebtCustomers.isNotEmpty) {
        await OfflineNotificationServiceExtension.showCustomNotification(
          title: '💰 ديون مرتفعة',
          body:
              '${highDebtCustomers.length} عميل لديه ديون مرتفعة تحتاج متابعة',
          priority: NotificationPriority.medium,
          category: NotificationCategory.customers,
          data: {
            'high_debt_customers': highDebtCustomers,
            'total_debt': totalDebt,
          },
        );
      }
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعارات العملاء: $e');
    }
  }

  /// إرسال إشعار بناءً على رؤية ذكية
  static Future<void> _sendInsightNotification(BusinessInsight insight) async {
    String emoji;
    NotificationPriority priority;

    switch (insight.type) {
      case InsightType.positive:
        emoji = '🎉';
        priority = NotificationPriority.medium;
        break;
      case InsightType.warning:
        emoji = '⚠️';
        priority = NotificationPriority.medium;
        break;
      case InsightType.critical:
        emoji = '🚨';
        priority = NotificationPriority.high;
        break;
      case InsightType.info:
        emoji = '💡';
        priority = NotificationPriority.low;
        break;
    }

    await OfflineNotificationServiceExtension.showCustomNotification(
      title: '$emoji ${insight.title}',
      body: insight.description,
      priority: priority,
      category: NotificationCategory.insights,
      data: insight.data ?? {},
    );
  }

  /// بدء المراقبة الذكية
  static Future<void> _startSmartMonitoring() async {
    // هنا يمكن إضافة مراقبة دورية للبيانات
    // مثل فحص المخزون كل ساعة، أو المبيعات كل يوم
    debugPrint('🤖 بدء المراقبة الذكية');
  }

  /// تعيين إعدادات افتراضية
  static Future<void> _setDefaultSettings() async {
    final defaultSettings = {
      'enabled': true,
      'min_priority': 2,
      'inventory_alerts': true,
      'sales_alerts': true,
      'customer_alerts': true,
      'currency_alerts': true,
      'insights_alerts': true,
      'daily_summary': true,
      'quiet_hours_start': 22, // 10 PM
      'quiet_hours_end': 7, // 7 AM
    };

    await OfflineDatabaseService.saveBoxItem(
      _settingsBox,
      'default',
      defaultSettings,
    );
  }

  /// الحصول على إعدادات الإشعارات
  static Future<Map<String, dynamic>> _getNotificationSettings() async {
    final settings =
        await OfflineDatabaseService.getBoxItem(_settingsBox, 'default');
    return settings != null ? Map<String, dynamic>.from(settings) : {};
  }

  /// حفظ في سجل الإشعارات
  static Future<void> _saveNotificationHistory(
      {required String type,
      required String title,
      required String body,
      Map<String, dynamic>? data}) async {
    final notification = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'type': type,
      'title': title,
      'body': body,
      'data': data ?? {},
      'created_at': DateTime.now().toIso8601String(),
      'read': false,
    };

    await OfflineDatabaseService.saveBoxItem(
      _notificationsBox,
      notification['id'] as String,
      notification,
    );
  }

  /// الحصول على سجل الإشعارات
  static Future<List<Map<String, dynamic>>> getNotificationHistory() async {
    try {
      final notificationsData =
          await OfflineDatabaseService.getBoxData(_notificationsBox);
      final notifications = notificationsData.values
          .map((data) => Map<String, dynamic>.from(data))
          .toList();

      // ترتيب حسب التاريخ (الأحدث أولاً)
      notifications.sort((a, b) => DateTime.parse(b['created_at'])
          .compareTo(DateTime.parse(a['created_at'])));

      return notifications;
    } catch (e) {
      debugPrint('❌ فشل في جلب سجل الإشعارات: $e');
      return [];
    }
  }

  /// تحديث إعدادات الإشعارات
  static Future<void> updateNotificationSettings(
      Map<String, dynamic> newSettings) async {
    try {
      await OfflineDatabaseService.saveBoxItem(
          _settingsBox, 'default', newSettings);
      debugPrint('✅ تم تحديث إعدادات الإشعارات');
    } catch (e) {
      debugPrint('❌ فشل في تحديث إعدادات الإشعارات: $e');
    }
  }
}
