import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'offline_storage_service.dart';

class SecurityService {
  static const String _activationCode = '125874369';
  static const String _isActivatedKey = 'is_activated';
  static const String _pinKey = 'user_pin';
  static const String _pinSetKey = 'pin_set';

  /// فحص ما إذا كان التطبيق مفعل
  static bool isActivated() {
    return OfflineStorageService.getSetting<bool>(_isActivatedKey, false) ??
        false;
  }

  /// تفعيل التطبيق برمز التفعيل
  static Future<bool> activateApp(String code) async {
    if (code == _activationCode) {
      await OfflineStorageService.saveSetting(_isActivatedKey, true);
      return true;
    }
    return false;
  }

  /// فحص ما إذا كان PIN محدد
  static bool isPinSet() {
    return OfflineStorageService.getSetting<bool>(_pinSetKey, false) ?? false;
  }

  /// تعيين PIN جديد
  static Future<void> setPin(String pin) async {
    if (pin.length != 4 || !RegExp(r'^\d{4}$').hasMatch(pin)) {
      throw Exception('رمز PIN يجب أن يكون 4 أرقام');
    }

    final hashedPin = _hashPin(pin);
    await OfflineStorageService.saveSetting(_pinKey, hashedPin);
    await OfflineStorageService.saveSetting(_pinSetKey, true);
  }

  /// التحقق من صحة PIN
  static bool verifyPin(String pin) {
    final storedPin = OfflineStorageService.getSetting<String>(_pinKey);
    if (storedPin == null) return false;

    final hashedPin = _hashPin(pin);
    return hashedPin == storedPin;
  }

  /// إعادة تعيين PIN (يتطلب رمز التفعيل)
  static Future<bool> resetPin(String activationCode, String newPin) async {
    if (activationCode != _activationCode) {
      return false;
    }

    await setPin(newPin);
    return true;
  }

  /// تشفير PIN
  static String _hashPin(String pin) {
    final bytes = utf8.encode('${pin}invoice_system_salt');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// مسح جميع بيانات الحماية (للاختبار فقط)
  static Future<void> clearSecurityData() async {
    await OfflineStorageService.saveSetting(_isActivatedKey, false);
    await OfflineStorageService.saveSetting(_pinSetKey, false);
    await OfflineStorageService.saveSetting(_pinKey, null);
  }

  /// فحص حالة الحماية
  static SecurityStatus getSecurityStatus() {
    final isActivated = SecurityService.isActivated();
    final isPinSet = SecurityService.isPinSet();

    if (!isActivated) {
      return SecurityStatus.needsActivation;
    } else if (!isPinSet) {
      return SecurityStatus.needsPinSetup;
    } else {
      return SecurityStatus.needsPinVerification;
    }
  }
}

enum SecurityStatus {
  needsActivation,
  needsPinSetup,
  needsPinVerification,
  authenticated,
}
