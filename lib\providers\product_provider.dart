import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

class ProductProvider with ChangeNotifier {
  List<Product> _products = [];
  bool _isLoading = false;
  String _error = '';

  List<Product> get products => _products;
  bool get isLoading => _isLoading;
  String get error => _error;

  Future<void> fetchProducts() async {
    _isLoading = true;
    _error = '';
    notifyListeners(); //  إعلام المستمعين قبل بدء العملية

    try {
      _products = await OfflineDatabaseService.getAllProducts();
      debugPrint(
          '✅ تم تحميل ${_products.length} منتج من قاعدة البيانات المحلية');
    } catch (e) {
      _error = 'فشل تحميل المنتجات: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addProduct(Product product) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final newProduct = await OfflineDatabaseService.addProduct(product);
      _products.add(newProduct);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم إضافة المنتج "${newProduct.name}" بنجاح');

      debugPrint('✅ تم إضافة المنتج: ${newProduct.name}');
    } catch (e) {
      _error = 'فشل إضافة المنتج: ${e.toString()}';
      //  يمكن إعادة رمي الخطأ إذا كنت تريد معالجته في الواجهة بشكل مختلف
      // throw e;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProduct(Product product) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final updatedProduct =
          await OfflineDatabaseService.updateProduct(product);
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        _products[index] = updatedProduct;
      }

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم تحديث المنتج "${updatedProduct.name}" بنجاح');

      debugPrint('✅ تم تحديث المنتج: ${updatedProduct.name}');
    } catch (e) {
      _error = 'فشل تحديث المنتج: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deleteProduct(int productId) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // الحصول على اسم المنتج قبل الحذف
      final productToDelete = _products.firstWhere((p) => p.id == productId);

      await OfflineDatabaseService.deleteProduct(productId);
      _products.removeWhere((p) => p.id == productId);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم حذف المنتج "${productToDelete.name}" بنجاح');

      debugPrint('✅ تم حذف المنتج: ${productToDelete.name}');
    } catch (e) {
      _error = 'فشل حذف المنتج: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
