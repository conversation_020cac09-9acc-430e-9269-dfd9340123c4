import 'package:flutter/material.dart';
import '../models/customer.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

class CustomerProvider with ChangeNotifier {
  List<Customer> _customers = [];
  bool _isLoading = false;
  String _error = '';

  CustomerProvider();

  List<Customer> get customers => _customers;
  bool get isLoading => _isLoading;
  String get error => _error;

  Future<void> fetchCustomers() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _customers = await OfflineDatabaseService.getAllCustomers();
      debugPrint(
          '✅ تم تحميل ${_customers.length} عميل من قاعدة البيانات المحلية');
    } catch (e) {
      _error = 'فشل في تحميل بيانات العملاء';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCustomer(Customer customer) async {
    try {
      final newCustomer = await OfflineDatabaseService.addCustomer(customer);
      _customers.add(newCustomer);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم إضافة العميل "${newCustomer.name}" بنجاح');

      notifyListeners();
      debugPrint('✅ تم إضافة العميل: ${newCustomer.name}');
    } catch (e) {
      _error = 'فشل في إضافة العميل';
      await OfflineNotificationService.showErrorNotification(
          'فشل في إضافة العميل: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }

  Future<void> updateCustomer(Customer customer) async {
    try {
      final updatedCustomer =
          await OfflineDatabaseService.updateCustomer(customer);
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = updatedCustomer;
      }

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم تحديث بيانات العميل "${updatedCustomer.name}" بنجاح');

      notifyListeners();
      debugPrint('✅ تم تحديث العميل: ${updatedCustomer.name}');
    } catch (e) {
      _error = 'فشل في تحديث بيانات العميل';
      await OfflineNotificationService.showErrorNotification(
          'فشل في تحديث بيانات العميل: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }

  Future<void> deleteCustomer(int id) async {
    try {
      // الحصول على اسم العميل قبل الحذف
      final customerToDelete =
          _customers.firstWhere((customer) => customer.id == id);

      await OfflineDatabaseService.deleteCustomer(id);
      _customers.removeWhere((customer) => customer.id == id);

      // إرسال إشعار نجاح
      await OfflineNotificationService.showSuccessNotification(
          'تم حذف العميل "${customerToDelete.name}" بنجاح');

      notifyListeners();
      debugPrint('✅ تم حذف العميل: ${customerToDelete.name}');
    } catch (e) {
      _error = 'فشل في حذف العميل';
      await OfflineNotificationService.showErrorNotification(
          'فشل في حذف العميل: ${e.toString()}');
      notifyListeners();
      rethrow;
    }
  }
}
