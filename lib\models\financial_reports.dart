/// قائمة المركز المالي (الميزانية العمومية)
class BalanceSheet {
  final DateTime asOfDate;
  final BalanceSheetSection assets;
  final BalanceSheetSection liabilities;
  final BalanceSheetSection equity;
  final String currency;
  final String companyName;

  const BalanceSheet({
    required this.asOfDate,
    required this.assets,
    required this.liabilities,
    required this.equity,
    this.currency = 'SYP',
    this.companyName = 'الشركة',
  });

  /// إجمالي الأصول
  double get totalAssets => assets.total;

  /// إجمالي الخصوم وحقوق الملكية
  double get totalLiabilitiesAndEquity => liabilities.total + equity.total;

  /// فحص توازن الميزانية
  bool get isBalanced => (totalAssets - totalLiabilitiesAndEquity).abs() < 0.01;

  Map<String, dynamic> toJson() {
    return {
      'as_of_date': asOfDate.toIso8601String(),
      'assets': assets.toJson(),
      'liabilities': liabilities.toJson(),
      'equity': equity.toJson(),
      'currency': currency,
      'company_name': companyName,
      'total_assets': totalAssets,
      'total_liabilities_and_equity': totalLiabilitiesAndEquity,
      'is_balanced': isBalanced,
    };
  }
}

/// قائمة الدخل
class IncomeStatement {
  final DateTime startDate;
  final DateTime endDate;
  final IncomeStatementSection revenue;
  final IncomeStatementSection expenses;
  final String currency;
  final String companyName;

  const IncomeStatement({
    required this.startDate,
    required this.endDate,
    required this.revenue,
    required this.expenses,
    this.currency = 'SYP',
    this.companyName = 'الشركة',
  });

  /// إجمالي الإيرادات
  double get totalRevenue => revenue.total;

  /// إجمالي المصروفات
  double get totalExpenses => expenses.total;

  /// صافي الدخل
  double get netIncome => totalRevenue - totalExpenses;

  /// هامش الربح
  double get profitMargin =>
      totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'revenue': revenue.toJson(),
      'expenses': expenses.toJson(),
      'currency': currency,
      'company_name': companyName,
      'total_revenue': totalRevenue,
      'total_expenses': totalExpenses,
      'net_income': netIncome,
      'profit_margin': profitMargin,
    };
  }
}

/// قائمة التدفقات النقدية
class CashFlowStatement {
  final DateTime startDate;
  final DateTime endDate;
  final CashFlowSection operatingActivities;
  final CashFlowSection investingActivities;
  final CashFlowSection financingActivities;
  final double beginningCash;
  final String currency;
  final String companyName;

  const CashFlowStatement({
    required this.startDate,
    required this.endDate,
    required this.operatingActivities,
    required this.investingActivities,
    required this.financingActivities,
    this.beginningCash = 0.0,
    this.currency = 'SYP',
    this.companyName = 'الشركة',
  });

  /// صافي التدفق النقدي من الأنشطة التشغيلية
  double get netOperatingCashFlow => operatingActivities.total;

  /// صافي التدفق النقدي من الأنشطة الاستثمارية
  double get netInvestingCashFlow => investingActivities.total;

  /// صافي التدفق النقدي من الأنشطة التمويلية
  double get netFinancingCashFlow => financingActivities.total;

  /// صافي التغير في النقدية
  double get netChangeInCash =>
      netOperatingCashFlow + netInvestingCashFlow + netFinancingCashFlow;

  /// النقدية في نهاية الفترة
  double get endingCash => beginningCash + netChangeInCash;

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'operating_activities': operatingActivities.toJson(),
      'investing_activities': investingActivities.toJson(),
      'financing_activities': financingActivities.toJson(),
      'beginning_cash': beginningCash,
      'currency': currency,
      'company_name': companyName,
      'net_operating_cash_flow': netOperatingCashFlow,
      'net_investing_cash_flow': netInvestingCashFlow,
      'net_financing_cash_flow': netFinancingCashFlow,
      'net_change_in_cash': netChangeInCash,
      'ending_cash': endingCash,
    };
  }
}

/// ميزان المراجعة
class TrialBalance {
  final DateTime asOfDate;
  final List<TrialBalanceAccount> accounts;
  final String currency;
  final String companyName;

  const TrialBalance({
    required this.asOfDate,
    required this.accounts,
    this.currency = 'SYP',
    this.companyName = 'الشركة',
  });

  /// إجمالي المدين
  double get totalDebit =>
      accounts.fold(0.0, (sum, account) => sum + account.debitBalance);

  /// إجمالي الدائن
  double get totalCredit =>
      accounts.fold(0.0, (sum, account) => sum + account.creditBalance);

  /// فحص توازن ميزان المراجعة
  bool get isBalanced => (totalDebit - totalCredit).abs() < 0.01;

  Map<String, dynamic> toJson() {
    return {
      'as_of_date': asOfDate.toIso8601String(),
      'accounts': accounts.map((account) => account.toJson()).toList(),
      'currency': currency,
      'company_name': companyName,
      'total_debit': totalDebit,
      'total_credit': totalCredit,
      'is_balanced': isBalanced,
    };
  }
}

/// قسم في الميزانية العمومية
class BalanceSheetSection {
  final String name;
  final List<BalanceSheetItem> items;

  const BalanceSheetSection({
    required this.name,
    required this.items,
  });

  double get total => items.fold(0.0, (sum, item) => sum + item.amount);

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'items': items.map((item) => item.toJson()).toList(),
      'total': total,
    };
  }
}

/// عنصر في الميزانية العمومية
class BalanceSheetItem {
  final String accountCode;
  final String accountName;
  final double amount;
  final String? notes;

  const BalanceSheetItem({
    required this.accountCode,
    required this.accountName,
    required this.amount,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_code': accountCode,
      'account_name': accountName,
      'amount': amount,
      'notes': notes,
    };
  }
}

/// قسم في قائمة الدخل
class IncomeStatementSection {
  final String name;
  final List<IncomeStatementItem> items;

  const IncomeStatementSection({
    required this.name,
    required this.items,
  });

  double get total => items.fold(0.0, (sum, item) => sum + item.amount);

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'items': items.map((item) => item.toJson()).toList(),
      'total': total,
    };
  }
}

/// عنصر في قائمة الدخل
class IncomeStatementItem {
  final String accountCode;
  final String accountName;
  final double amount;
  final double? percentage;
  final String? notes;

  const IncomeStatementItem({
    required this.accountCode,
    required this.accountName,
    required this.amount,
    this.percentage,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_code': accountCode,
      'account_name': accountName,
      'amount': amount,
      'percentage': percentage,
      'notes': notes,
    };
  }
}

/// قسم في قائمة التدفقات النقدية
class CashFlowSection {
  final String name;
  final List<CashFlowItem> items;

  const CashFlowSection({
    required this.name,
    required this.items,
  });

  double get total => items.fold(0.0, (sum, item) => sum + item.amount);

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'items': items.map((item) => item.toJson()).toList(),
      'total': total,
    };
  }
}

/// عنصر في قائمة التدفقات النقدية
class CashFlowItem {
  final String description;
  final double amount;
  final CashFlowType type;
  final String? notes;

  const CashFlowItem({
    required this.description,
    required this.amount,
    required this.type,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'amount': amount,
      'type': type.toString().split('.').last,
      'notes': notes,
    };
  }
}

/// حساب في ميزان المراجعة
class TrialBalanceAccount {
  final String accountCode;
  final String accountName;
  final double debitBalance;
  final double creditBalance;
  final String accountType;

  const TrialBalanceAccount({
    required this.accountCode,
    required this.accountName,
    required this.debitBalance,
    required this.creditBalance,
    required this.accountType,
  });

  Map<String, dynamic> toJson() {
    return {
      'account_code': accountCode,
      'account_name': accountName,
      'debit_balance': debitBalance,
      'credit_balance': creditBalance,
      'account_type': accountType,
    };
  }
}

/// أنواع التدفقات النقدية
enum CashFlowType {
  inflow, // تدفق داخل
  outflow, // تدفق خارج
}

/// تحليل النسب المالية
class FinancialRatios {
  final double currentRatio; // النسبة الجارية
  final double quickRatio; // النسبة السريعة
  final double debtToEquityRatio; // نسبة الدين إلى حقوق الملكية
  final double returnOnAssets; // العائد على الأصول
  final double returnOnEquity; // العائد على حقوق الملكية
  final double grossProfitMargin; // هامش الربح الإجمالي
  final double netProfitMargin; // هامش الربح الصافي
  final double assetTurnover; // معدل دوران الأصول
  final double inventoryTurnover; // معدل دوران المخزون
  final double receivablesTurnover; // معدل دوران المدينين

  const FinancialRatios({
    required this.currentRatio,
    required this.quickRatio,
    required this.debtToEquityRatio,
    required this.returnOnAssets,
    required this.returnOnEquity,
    required this.grossProfitMargin,
    required this.netProfitMargin,
    required this.assetTurnover,
    required this.inventoryTurnover,
    required this.receivablesTurnover,
  });

  Map<String, dynamic> toJson() {
    return {
      'current_ratio': currentRatio,
      'quick_ratio': quickRatio,
      'debt_to_equity_ratio': debtToEquityRatio,
      'return_on_assets': returnOnAssets,
      'return_on_equity': returnOnEquity,
      'gross_profit_margin': grossProfitMargin,
      'net_profit_margin': netProfitMargin,
      'asset_turnover': assetTurnover,
      'inventory_turnover': inventoryTurnover,
      'receivables_turnover': receivablesTurnover,
    };
  }

  /// تقييم الوضع المالي
  String getFinancialHealthAssessment() {
    int score = 0;

    // تقييم النسبة الجارية
    if (currentRatio >= 2.0) {
      score += 2;
    } else if (currentRatio >= 1.5) {
      score += 1;
    }

    // تقييم هامش الربح الصافي
    if (netProfitMargin >= 15) {
      score += 2;
    } else if (netProfitMargin >= 10) {
      score += 1;
    }

    // تقييم العائد على حقوق الملكية
    if (returnOnEquity >= 20) {
      score += 2;
    } else if (returnOnEquity >= 15) {
      score += 1;
    }

    // تقييم نسبة الدين إلى حقوق الملكية
    if (debtToEquityRatio <= 0.5) {
      score += 2;
    } else if (debtToEquityRatio <= 1.0) {
      score += 1;
    }

    if (score >= 7) return 'ممتاز';
    if (score >= 5) return 'جيد';
    if (score >= 3) return 'متوسط';
    return 'ضعيف';
  }
}
