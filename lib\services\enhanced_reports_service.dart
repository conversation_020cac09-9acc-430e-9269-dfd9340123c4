import '../models/invoice.dart';
import '../models/product.dart';
import '../models/customer.dart';

/// خدمة التقارير المحسنة
class EnhancedReportsService {
  /// تقرير المبيعات اليومية
  static Map<String, dynamic> getDailySalesReport(
    List<Invoice> invoices, {
    DateTime? targetDate,
  }) {
    final date = targetDate ?? DateTime.now();
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final dayInvoices = invoices
        .where((invoice) =>
            invoice.date.isAfter(startOfDay) && invoice.date.isBefore(endOfDay))
        .toList();

    final totalSales =
        dayInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp);
    final totalInvoices = dayInvoices.length;

    // تحليل المبيعات بالساعة
    final salesByHour = <int, double>{};
    final invoicesByHour = <int, int>{};

    for (int hour = 0; hour < 24; hour++) {
      salesByHour[hour] = 0.0;
      invoicesByHour[hour] = 0;
    }

    for (var invoice in dayInvoices) {
      final hour = invoice.date.hour;
      salesByHour[hour] = (salesByHour[hour] ?? 0) + invoice.totalAmountSyp;
      invoicesByHour[hour] = (invoicesByHour[hour] ?? 0) + 1;
    }

    final peakHour = _getPeakHour(salesByHour);
    final averageSale = totalInvoices > 0 ? totalSales / totalInvoices : 0.0;

    return {
      'date': startOfDay,
      'total_sales_syp': totalSales,
      'total_invoices': totalInvoices,
      'average_sale': averageSale,
      'sales_by_hour': salesByHour,
      'invoices_by_hour': invoicesByHour,
      'peak_hour': peakHour,
      'peak_hour_sales': salesByHour[peakHour] ?? 0.0,
      'invoices': dayInvoices,
    };
  }

  /// تقرير المبيعات الأسبوعية
  static Map<String, dynamic> getWeeklySalesReport(
    List<Invoice> invoices, {
    DateTime? weekStart,
  }) {
    final start = weekStart ?? _getWeekStart(DateTime.now());
    final end = start.add(const Duration(days: 7));

    final weekInvoices = invoices
        .where((invoice) =>
            invoice.date.isAfter(start) && invoice.date.isBefore(end))
        .toList();

    final totalSales =
        weekInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp);
    final totalInvoices = weekInvoices.length;

    // تحليل المبيعات بالأيام
    final salesByDay = <String, double>{};
    final invoicesByDay = <String, int>{};
    final daysOfWeek = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];

    for (var day in daysOfWeek) {
      salesByDay[day] = 0.0;
      invoicesByDay[day] = 0;
    }

    for (var invoice in weekInvoices) {
      final dayIndex = (invoice.date.weekday - 1) % 7;
      final dayName = daysOfWeek[dayIndex];
      salesByDay[dayName] = (salesByDay[dayName] ?? 0) + invoice.totalAmountSyp;
      invoicesByDay[dayName] = (invoicesByDay[dayName] ?? 0) + 1;
    }

    final bestDay = _getBestDay(salesByDay);
    final dailyAverage = totalSales / 7;

    return {
      'week_start': start,
      'week_end': end,
      'total_sales_syp': totalSales,
      'total_invoices': totalInvoices,
      'average_sale': totalInvoices > 0 ? totalSales / totalInvoices : 0.0,
      'sales_by_day': salesByDay,
      'invoices_by_day': invoicesByDay,
      'best_day': bestDay,
      'best_day_sales': salesByDay[bestDay] ?? 0.0,
      'daily_average': dailyAverage,
      'invoices': weekInvoices,
    };
  }

  /// تقرير أفضل المنتجات مبيعاً
  static List<Map<String, dynamic>> getTopSellingProducts(
    List<Invoice> invoices, {
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    var filteredInvoices = invoices;

    if (startDate != null || endDate != null) {
      filteredInvoices = invoices.where((invoice) {
        if (startDate != null && invoice.date.isBefore(startDate)) return false;
        if (endDate != null && invoice.date.isAfter(endDate)) return false;
        return true;
      }).toList();
    }

    final productSales = <String, Map<String, dynamic>>{};

    for (var invoice in filteredInvoices) {
      for (var item in invoice.items) {
        final productId = item.productId;

        if (productSales.containsKey(productId)) {
          productSales[productId]!['quantity'] += item.quantity;
          productSales[productId]!['total_sales_syp'] += item.totalSyp;
          productSales[productId]!['total_sales_usd'] += item.totalUsd;
          productSales[productId]!['invoice_count'] += 1;
        } else {
          productSales[productId] = {
            'product_id': productId,
            'product_name': item.productName,
            'product_barcode': item.productBarcode,
            'quantity': item.quantity,
            'total_sales_syp': item.totalSyp,
            'total_sales_usd': item.totalUsd,
            'unit_price_syp': item.unitPriceSyp,
            'unit_price_usd': item.unitPriceUsd,
            'invoice_count': 1,
          };
        }
      }
    }

    final sortedProducts = productSales.values.toList();
    sortedProducts.sort((a, b) => b['quantity'].compareTo(a['quantity']));

    return sortedProducts.take(limit).toList();
  }

  /// تقرير أداء العملاء
  static List<Map<String, dynamic>> getCustomerPerformanceReport(
    List<Invoice> invoices,
    List<Customer> customers, {
    int limit = 10,
  }) {
    final customerStats = <String, Map<String, dynamic>>{};

    // تهيئة إحصائيات العملاء
    for (var customer in customers) {
      customerStats[customer.id.toString()] = {
        'customer': customer,
        'total_invoices': 0,
        'total_purchases_syp': 0.0,
        'total_purchases_usd': 0.0,
        'average_purchase': 0.0,
        'last_purchase_date': null,
        'total_debt': customer.totalDebt,
      };
    }

    // حساب الإحصائيات من الفواتير
    for (var invoice in invoices) {
      final customerId = invoice.customerId.toString();

      if (customerStats.containsKey(customerId)) {
        customerStats[customerId]!['total_invoices'] += 1;
        customerStats[customerId]!['total_purchases_syp'] +=
            invoice.totalAmountSyp;
        customerStats[customerId]!['total_purchases_usd'] +=
            invoice.totalAmountUsd;

        final lastDate =
            customerStats[customerId]!['last_purchase_date'] as DateTime?;
        if (lastDate == null || invoice.date.isAfter(lastDate)) {
          customerStats[customerId]!['last_purchase_date'] = invoice.date;
        }
      }
    }

    // حساب المتوسطات
    for (var stats in customerStats.values) {
      final totalInvoices = stats['total_invoices'] as int;
      if (totalInvoices > 0) {
        stats['average_purchase'] =
            stats['total_purchases_syp'] / totalInvoices;
      }
    }

    final sortedCustomers = customerStats.values.toList();
    sortedCustomers.sort((a, b) => (b['total_purchases_syp'] as double)
        .compareTo(a['total_purchases_syp'] as double));

    return sortedCustomers.take(limit).toList();
  }

  /// تقرير الربحية
  static Map<String, dynamic> getProfitabilityReport(
    List<Invoice> invoices,
    List<Product> products, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    var filteredInvoices = invoices;

    if (startDate != null || endDate != null) {
      filteredInvoices = invoices.where((invoice) {
        if (startDate != null && invoice.date.isBefore(startDate)) return false;
        if (endDate != null && invoice.date.isAfter(endDate)) return false;
        return true;
      }).toList();
    }

    double totalRevenue = 0.0;
    double totalCost = 0.0;
    double totalProfit = 0.0;

    final productMap = {for (var p in products) p.id.toString(): p};

    for (var invoice in filteredInvoices) {
      for (var item in invoice.items) {
        final product = productMap[item.productId];
        if (product != null) {
          final revenue = item.totalSyp;
          final cost = product.purchasePriceSyp * item.quantity;
          final profit = revenue - cost;

          totalRevenue += revenue;
          totalCost += cost;
          totalProfit += profit;
        }
      }
    }

    final profitMargin =
        totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0.0;

    return {
      'period_start': startDate,
      'period_end': endDate,
      'total_revenue_syp': totalRevenue,
      'total_cost_syp': totalCost,
      'total_profit_syp': totalProfit,
      'profit_margin_percentage': profitMargin,
      'total_invoices': filteredInvoices.length,
    };
  }

  /// الحصول على ساعة الذروة
  static int _getPeakHour(Map<int, double> salesByHour) {
    int peakHour = 0;
    double maxSales = 0;

    salesByHour.forEach((hour, sales) {
      if (sales > maxSales) {
        maxSales = sales;
        peakHour = hour;
      }
    });

    return peakHour;
  }

  /// الحصول على أفضل يوم
  static String _getBestDay(Map<String, double> salesByDay) {
    String bestDay = '';
    double maxSales = 0;

    salesByDay.forEach((day, sales) {
      if (sales > maxSales) {
        maxSales = sales;
        bestDay = day;
      }
    });

    return bestDay;
  }

  /// الحصول على بداية الأسبوع
  static DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }
}
