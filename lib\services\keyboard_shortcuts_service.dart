import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة اختصارات لوحة المفاتيح
class KeyboardShortcutsService {
  static final Map<LogicalKeySet, VoidCallback> _shortcuts = {};
  static BuildContext? _context;

  /// تهيئة اختصارات لوحة المفاتيح
  static void initialize(BuildContext context) {
    _context = context;
    _setupDefaultShortcuts();
  }

  /// إعداد الاختصارات الافتراضية
  static void _setupDefaultShortcuts() {
    if (_context == null) return;

    // اختصارات التنقل
    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit1),
      () => _navigateTo('/'),
      'الصفحة الرئيسية',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit2),
      () => _navigateTo('/products'),
      'المنتجات',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit3),
      () => _navigateTo('/customers'),
      'العملاء',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit4),
      () => _navigateTo('/invoices'),
      'الفواتير',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.digit5),
      () => _navigateTo('/reports'),
      'التقارير',
    );

    // اختصارات الإجراءات
    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN),
      () => _navigateTo('/create-invoice'),
      'فاتورة جديدة',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyP),
      () => _showQuickProductDialog(),
      'منتج جديد',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyC),
      () => _showQuickCustomerDialog(),
      'عميل جديد',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyD),
      () => _showQuickDollarDialog(),
      'تحديث الدولار',
    );

    // اختصارات البحث
    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF),
      () => _showQuickSearchDialog(),
      'بحث سريع',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyK),
      () => _showCommandPalette(),
      'لوحة الأوامر',
    );

    // اختصارات النظام
    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS),
      () => _quickSave(),
      'حفظ سريع',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyB),
      () => _quickBackup(),
      'نسخ احتياطي',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.f1),
      () => _showHelpDialog(),
      'المساعدة',
    );

    addShortcut(
      LogicalKeySet(LogicalKeyboardKey.escape),
      () => _closeCurrentDialog(),
      'إغلاق النافذة',
    );
  }

  /// إضافة اختصار جديد
  static void addShortcut(
      LogicalKeySet keySet, VoidCallback callback, String description) {
    _shortcuts[keySet] = callback;
    _shortcutDescriptions[keySet] = description;
  }

  /// إزالة اختصار
  static void removeShortcut(LogicalKeySet keySet) {
    _shortcuts.remove(keySet);
    _shortcutDescriptions.remove(keySet);
  }

  /// الحصول على جميع الاختصارات
  static Map<LogicalKeySet, VoidCallback> get shortcuts => _shortcuts;

  /// وصف الاختصارات
  static final Map<LogicalKeySet, String> _shortcutDescriptions = {};

  /// الحصول على وصف الاختصارات
  static Map<LogicalKeySet, String> get shortcutDescriptions =>
      _shortcutDescriptions;

  // وظائف الاختصارات
  static void _navigateTo(String route) {
    if (_context != null) {
      Navigator.pushNamed(_context!, route);
    }
  }

  static void _showQuickProductDialog() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const QuickProductDialog(),
    );
  }

  static void _showQuickCustomerDialog() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const QuickCustomerDialog(),
    );
  }

  static void _showQuickDollarDialog() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const QuickDollarDialog(),
    );
  }

  static void _showQuickSearchDialog() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const QuickSearchDialog(),
    );
  }

  static void _showCommandPalette() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const CommandPaletteDialog(),
    );
  }

  static void _quickSave() {
    if (_context == null) return;

    ScaffoldMessenger.of(_context!).showSnackBar(
      const SnackBar(
        content: Text('تم الحفظ التلقائي'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  static void _quickBackup() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => AlertDialog(
        title: const Text('نسخ احتياطي سريع'),
        content: const Text('هل تريد إنشاء نسخة احتياطية الآن؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // تنفيذ النسخ الاحتياطي
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  static void _showHelpDialog() {
    if (_context == null) return;

    showDialog(
      context: _context!,
      builder: (context) => const KeyboardShortcutsHelpDialog(),
    );
  }

  static void _closeCurrentDialog() {
    if (_context != null && Navigator.canPop(_context!)) {
      Navigator.pop(_context!);
    }
  }

  /// تحويل LogicalKeySet إلى نص قابل للقراءة
  static String keySetToString(LogicalKeySet keySet) {
    final keys = keySet.keys.toList();
    final keyNames = <String>[];

    for (var key in keys) {
      if (key == LogicalKeyboardKey.control) {
        keyNames.add('Ctrl');
      } else if (key == LogicalKeyboardKey.alt) {
        keyNames.add('Alt');
      } else if (key == LogicalKeyboardKey.shift) {
        keyNames.add('Shift');
      } else if (key == LogicalKeyboardKey.meta) {
        keyNames.add('Cmd');
      } else if (key.keyLabel.isNotEmpty) {
        keyNames.add(key.keyLabel.toUpperCase());
      } else {
        keyNames.add(key.debugName ?? 'Unknown');
      }
    }

    return keyNames.join(' + ');
  }
}

/// نافذة مساعدة اختصارات لوحة المفاتيح
class KeyboardShortcutsHelpDialog extends StatelessWidget {
  const KeyboardShortcutsHelpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.keyboard),
          SizedBox(width: 8),
          Text('اختصارات لوحة المفاتيح'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'اختصارات التنقل:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              _buildShortcutGroup([
                ('Ctrl + 1', 'الصفحة الرئيسية'),
                ('Ctrl + 2', 'المنتجات'),
                ('Ctrl + 3', 'العملاء'),
                ('Ctrl + 4', 'الفواتير'),
                ('Ctrl + 5', 'التقارير'),
              ]),
              const SizedBox(height: 16),
              const Text(
                'اختصارات الإجراءات:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              _buildShortcutGroup([
                ('Ctrl + N', 'فاتورة جديدة'),
                ('Ctrl + P', 'منتج جديد'),
                ('Ctrl + C', 'عميل جديد'),
                ('Ctrl + D', 'تحديث الدولار'),
              ]),
              const SizedBox(height: 16),
              const Text(
                'اختصارات البحث:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              _buildShortcutGroup([
                ('Ctrl + F', 'بحث سريع'),
                ('Ctrl + K', 'لوحة الأوامر'),
              ]),
              const SizedBox(height: 16),
              const Text(
                'اختصارات النظام:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              _buildShortcutGroup([
                ('Ctrl + S', 'حفظ سريع'),
                ('Ctrl + B', 'نسخ احتياطي'),
                ('F1', 'المساعدة'),
                ('Esc', 'إغلاق النافذة'),
              ]),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  Widget _buildShortcutGroup(List<(String, String)> shortcuts) {
    return Column(
      children: shortcuts
          .map((shortcut) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey[400]!),
                      ),
                      child: Text(
                        shortcut.$1,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(shortcut.$2),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }
}

/// لوحة الأوامر السريعة
class CommandPaletteDialog extends StatefulWidget {
  const CommandPaletteDialog({super.key});

  @override
  State<CommandPaletteDialog> createState() => _CommandPaletteDialogState();
}

class _CommandPaletteDialogState extends State<CommandPaletteDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Command> _filteredCommands = [];
  int _selectedIndex = 0;

  final List<Command> _allCommands = [
    Command('فاتورة جديدة', Icons.add_shopping_cart, '/create-invoice'),
    Command('منتج جديد', Icons.add_box, '/products'),
    Command('عميل جديد', Icons.person_add, '/customers'),
    Command('تحديث الدولار', Icons.currency_exchange, null),
    Command('التقارير', Icons.analytics, '/reports'),
    Command('التقارير المالية', Icons.assessment, '/financial-reports'),
    Command('دليل الحسابات', Icons.account_balance, '/accounts'),
    Command('إدارة الشركات', Icons.business, '/companies'),
    Command('الإعدادات', Icons.settings, '/settings'),
    Command('نسخ احتياطي', Icons.backup, null),
  ];

  @override
  void initState() {
    super.initState();
    _filteredCommands = _allCommands;
    _searchController.addListener(_filterCommands);
  }

  void _filterCommands() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredCommands = _allCommands
          .where((command) => command.name.toLowerCase().contains(query))
          .toList();
      _selectedIndex = 0;
    });
  }

  void _executeCommand(Command command) {
    Navigator.pop(context);
    if (command.route != null) {
      Navigator.pushNamed(context, command.route!);
    } else {
      // تنفيذ أوامر خاصة
      switch (command.name) {
        case 'تحديث الدولار':
          KeyboardShortcutsService._showQuickDollarDialog();
          break;
        case 'نسخ احتياطي':
          KeyboardShortcutsService._quickBackup();
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        height: 400,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _searchController,
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'ابحث عن أمر...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _filteredCommands.length,
                itemBuilder: (context, index) {
                  final command = _filteredCommands[index];
                  final isSelected = index == _selectedIndex;

                  return ListTile(
                    leading: Icon(command.icon),
                    title: Text(command.name),
                    selected: isSelected,
                    onTap: () => _executeCommand(command),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// نموذج الأمر
class Command {
  final String name;
  final IconData icon;
  final String? route;

  Command(this.name, this.icon, this.route);
}

// نوافذ حوار سريعة بسيطة
class QuickProductDialog extends StatelessWidget {
  const QuickProductDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      title: Text('منتج جديد'),
      content: Text('نافذة إضافة منتج سريع'),
    );
  }
}

class QuickCustomerDialog extends StatelessWidget {
  const QuickCustomerDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      title: Text('عميل جديد'),
      content: Text('نافذة إضافة عميل سريع'),
    );
  }
}

class QuickDollarDialog extends StatelessWidget {
  const QuickDollarDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      title: Text('تحديث الدولار'),
      content: Text('نافذة تحديث سعر الدولار'),
    );
  }
}

class QuickSearchDialog extends StatelessWidget {
  const QuickSearchDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      title: Text('بحث سريع'),
      content: Text('نافذة البحث السريع'),
    );
  }
}
