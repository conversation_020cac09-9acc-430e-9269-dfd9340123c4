// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_integration.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class APIIntegrationAdapter extends TypeAdapter<APIIntegration> {
  @override
  final int typeId = 22;

  @override
  APIIntegration read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return APIIntegration(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String,
      type: fields[3] as IntegrationType,
      baseUrl: fields[4] as String,
      apiKey: fields[5] as String,
      secretKey: fields[6] as String?,
      headers: (fields[7] as Map).cast<String, String>(),
      configuration: (fields[8] as Map).cast<String, dynamic>(),
      status: fields[9] as IntegrationStatus,
      isEnabled: fields[10] as bool,
      createdAt: fields[11] as DateTime,
      lastSyncAt: fields[12] as DateTime?,
      lastError: fields[13] as String?,
      syncCount: fields[14] as int,
      supportedOperations: (fields[15] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, APIIntegration obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.baseUrl)
      ..writeByte(5)
      ..write(obj.apiKey)
      ..writeByte(6)
      ..write(obj.secretKey)
      ..writeByte(7)
      ..write(obj.headers)
      ..writeByte(8)
      ..write(obj.configuration)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.isEnabled)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.lastSyncAt)
      ..writeByte(13)
      ..write(obj.lastError)
      ..writeByte(14)
      ..write(obj.syncCount)
      ..writeByte(15)
      ..write(obj.supportedOperations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is APIIntegrationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SyncLogAdapter extends TypeAdapter<SyncLog> {
  @override
  final int typeId = 23;

  @override
  SyncLog read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SyncLog(
      id: fields[0] as int,
      integrationId: fields[1] as int,
      operation: fields[2] as String,
      direction: fields[3] as String,
      data: (fields[4] as Map).cast<String, dynamic>(),
      success: fields[5] as bool,
      error: fields[6] as String?,
      timestamp: fields[7] as DateTime,
      responseCode: fields[8] as int?,
      responseMessage: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SyncLog obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.integrationId)
      ..writeByte(2)
      ..write(obj.operation)
      ..writeByte(3)
      ..write(obj.direction)
      ..writeByte(4)
      ..write(obj.data)
      ..writeByte(5)
      ..write(obj.success)
      ..writeByte(6)
      ..write(obj.error)
      ..writeByte(7)
      ..write(obj.timestamp)
      ..writeByte(8)
      ..write(obj.responseCode)
      ..writeByte(9)
      ..write(obj.responseMessage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncLogAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IntegrationTypeAdapter extends TypeAdapter<IntegrationType> {
  @override
  final int typeId = 20;

  @override
  IntegrationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return IntegrationType.accounting;
      case 1:
        return IntegrationType.ecommerce;
      case 2:
        return IntegrationType.payment;
      case 3:
        return IntegrationType.shipping;
      case 4:
        return IntegrationType.banking;
      case 5:
        return IntegrationType.crm;
      case 6:
        return IntegrationType.erp;
      case 7:
        return IntegrationType.pos;
      case 8:
        return IntegrationType.warehouse;
      case 9:
        return IntegrationType.custom;
      default:
        return IntegrationType.accounting;
    }
  }

  @override
  void write(BinaryWriter writer, IntegrationType obj) {
    switch (obj) {
      case IntegrationType.accounting:
        writer.writeByte(0);
        break;
      case IntegrationType.ecommerce:
        writer.writeByte(1);
        break;
      case IntegrationType.payment:
        writer.writeByte(2);
        break;
      case IntegrationType.shipping:
        writer.writeByte(3);
        break;
      case IntegrationType.banking:
        writer.writeByte(4);
        break;
      case IntegrationType.crm:
        writer.writeByte(5);
        break;
      case IntegrationType.erp:
        writer.writeByte(6);
        break;
      case IntegrationType.pos:
        writer.writeByte(7);
        break;
      case IntegrationType.warehouse:
        writer.writeByte(8);
        break;
      case IntegrationType.custom:
        writer.writeByte(9);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IntegrationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IntegrationStatusAdapter extends TypeAdapter<IntegrationStatus> {
  @override
  final int typeId = 21;

  @override
  IntegrationStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return IntegrationStatus.active;
      case 1:
        return IntegrationStatus.inactive;
      case 2:
        return IntegrationStatus.error;
      case 3:
        return IntegrationStatus.testing;
      case 4:
        return IntegrationStatus.pending;
      default:
        return IntegrationStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, IntegrationStatus obj) {
    switch (obj) {
      case IntegrationStatus.active:
        writer.writeByte(0);
        break;
      case IntegrationStatus.inactive:
        writer.writeByte(1);
        break;
      case IntegrationStatus.error:
        writer.writeByte(2);
        break;
      case IntegrationStatus.testing:
        writer.writeByte(3);
        break;
      case IntegrationStatus.pending:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IntegrationStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
