import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/journal_entry.dart';
import '../models/product.dart';
import '../services/accounting_service.dart';
import '../services/offline_database_service.dart';

/// خدمة القيود المحاسبية التلقائية
class AutoJournalService {
  /// إنشاء قيد بيع تلقائي من الفاتورة
  static Future<JournalEntry?> createSaleJournalEntry(Invoice invoice) async {
    try {
      debugPrint('إنشاء قيد بيع للفاتورة رقم: ${invoice.id}');

      // حساب تكلفة البضاعة المباعة
      double totalCost = 0;
      final products = await OfflineDatabaseService.getAllProducts();
      
      for (var item in invoice.items) {
        final product = products.firstWhere(
          (p) => p.id.toString() == item.productId.toString(),
          orElse: () => Product(
            id: 0, name: '', purchasePrice: 0, salePrice: 0,
            quantity: 0, sellingPriceSyp: 0, sellingPriceUsd: 0,
            purchasePriceSyp: 0, purchasePriceUsd: 0,
          ),
        );
        totalCost += product.purchasePrice * item.quantity;
      }

      // إنشاء خطوط القيد
      final journalLines = <JournalLine>[];

      // 1. مدين: النقدية أو العملاء
      if (invoice.paymentType == PaymentType.cash) {
        // مدين: النقدية
        journalLines.add(JournalLine(
          id: 0,
          accountId: 1110,
          accountCode: '1110',
          accountName: 'النقدية',
          description: 'بيع نقدي للعميل ${invoice.customerName}',
          debitAmount: invoice.totalAmountSyp,
          creditAmount: 0,
        ));
      } else {
        // مدين: العملاء
        journalLines.add(JournalLine(
          id: 0,
          accountId: 1130,
          accountCode: '1130',
          accountName: 'العملاء',
          description: 'بيع آجل للعميل ${invoice.customerName}',
          debitAmount: invoice.totalAmountSyp,
          creditAmount: 0,
        ));
      }

      // 2. دائن: إيرادات المبيعات
      journalLines.add(JournalLine(
        id: 0,
        accountId: 4100,
        accountCode: '4100',
        accountName: 'إيرادات المبيعات',
        description: 'إيرادات بيع للعميل ${invoice.customerName}',
        debitAmount: 0,
        creditAmount: invoice.totalAmountSyp,
      ));

      // 3. مدين: تكلفة البضاعة المباعة (إذا كانت التكلفة > 0)
      if (totalCost > 0) {
        journalLines.add(JournalLine(
          id: 0,
          accountId: 5100,
          accountCode: '5100',
          accountName: 'تكلفة البضاعة المباعة',
          description: 'تكلفة البضاعة المباعة للفاتورة ${invoice.id}',
          debitAmount: totalCost,
          creditAmount: 0,
        ));

        // 4. دائن: المخزون
        journalLines.add(JournalLine(
          id: 0,
          accountId: 1140,
          accountCode: '1140',
          accountName: 'المخزون',
          description: 'خروج بضاعة من المخزون للفاتورة ${invoice.id}',
          debitAmount: 0,
          creditAmount: totalCost,
        ));
      }

      // إنشاء القيد
      final journalEntry = JournalEntry(
        id: 0,
        reference: 'INV-${invoice.id}',
        date: invoice.date,
        description: 'قيد بيع للفاتورة رقم ${invoice.id} - العميل: ${invoice.customerName}',
        lines: journalLines,
        status: EntryStatus.draft,
        createdAt: DateTime.now(),
      );

      // حفظ القيد
      final savedEntry = await AccountingService.addJournalEntry(journalEntry);
      
      // ترحيل القيد تلقائياً
      await AccountingService.postJournalEntry(savedEntry.id);

      debugPrint('✅ تم إنشاء قيد البيع بنجاح: ${savedEntry.reference}');
      return savedEntry;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد البيع: $e');
      return null;
    }
  }

  /// إنشاء قيد دفع دين العميل
  static Future<JournalEntry?> createPaymentJournalEntry({
    required int customerId,
    required String customerName,
    required double amount,
    required DateTime paymentDate,
    String? notes,
  }) async {
    try {
      debugPrint('إنشاء قيد دفع دين للعميل: $customerName');

      final journalLines = <JournalLine>[
        // مدين: النقدية
        JournalLine(
          id: 0,
          accountId: 1110,
          accountCode: '1110',
          accountName: 'النقدية',
          description: 'تحصيل دين من العميل $customerName',
          debitAmount: amount,
          creditAmount: 0,
        ),
        // دائن: العملاء
        JournalLine(
          id: 0,
          accountId: 1130,
          accountCode: '1130',
          accountName: 'العملاء',
          description: 'تحصيل دين من العميل $customerName',
          debitAmount: 0,
          creditAmount: amount,
        ),
      ];

      final journalEntry = JournalEntry(
        id: 0,
        reference: 'PAY-${DateTime.now().millisecondsSinceEpoch}',
        date: paymentDate,
        description: 'تحصيل دين من العميل $customerName${notes != null ? ' - $notes' : ''}',
        lines: journalLines,
        status: EntryStatus.draft,
        createdAt: DateTime.now(),
      );

      final savedEntry = await AccountingService.addJournalEntry(journalEntry);
      await AccountingService.postJournalEntry(savedEntry.id);

      debugPrint('✅ تم إنشاء قيد الدفع بنجاح: ${savedEntry.reference}');
      return savedEntry;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد الدفع: $e');
      return null;
    }
  }

  /// إنشاء قيد شراء بضاعة
  static Future<JournalEntry?> createPurchaseJournalEntry({
    required String supplierName,
    required double totalAmount,
    required DateTime purchaseDate,
    required bool isCash,
    String? notes,
  }) async {
    try {
      debugPrint('إنشاء قيد شراء من المورد: $supplierName');

      final journalLines = <JournalLine>[
        // مدين: المخزون
        JournalLine(
          id: 0,
          accountId: 1140,
          accountCode: '1140',
          accountName: 'المخزون',
          description: 'شراء بضاعة من المورد $supplierName',
          debitAmount: totalAmount,
          creditAmount: 0,
        ),
      ];

      if (isCash) {
        // دائن: النقدية
        journalLines.add(JournalLine(
          id: 0,
          accountId: 1110,
          accountCode: '1110',
          accountName: 'النقدية',
          description: 'دفع نقدي للمورد $supplierName',
          debitAmount: 0,
          creditAmount: totalAmount,
        ));
      } else {
        // دائن: الموردون
        journalLines.add(JournalLine(
          id: 0,
          accountId: 2110,
          accountCode: '2110',
          accountName: 'الموردون',
          description: 'شراء آجل من المورد $supplierName',
          debitAmount: 0,
          creditAmount: totalAmount,
        ));
      }

      final journalEntry = JournalEntry(
        id: 0,
        reference: 'PUR-${DateTime.now().millisecondsSinceEpoch}',
        date: purchaseDate,
        description: 'شراء بضاعة من المورد $supplierName${notes != null ? ' - $notes' : ''}',
        lines: journalLines,
        status: EntryStatus.draft,
        createdAt: DateTime.now(),
      );

      final savedEntry = await AccountingService.addJournalEntry(journalEntry);
      await AccountingService.postJournalEntry(savedEntry.id);

      debugPrint('✅ تم إنشاء قيد الشراء بنجاح: ${savedEntry.reference}');
      return savedEntry;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد الشراء: $e');
      return null;
    }
  }

  /// إنشاء قيد مصروف
  static Future<JournalEntry?> createExpenseJournalEntry({
    required String expenseType,
    required double amount,
    required DateTime expenseDate,
    String? description,
  }) async {
    try {
      debugPrint('إنشاء قيد مصروف: $expenseType');

      // تحديد حساب المصروف حسب النوع
      int expenseAccountId = 5200;
      String expenseAccountCode = '5200';
      String expenseAccountName = 'مصروفات عامة';
      
      switch (expenseType.toLowerCase()) {
        case 'إيجار':
        case 'rent':
          expenseAccountId = 5210;
          expenseAccountCode = '5210';
          expenseAccountName = 'مصروف الإيجار';
          break;
        case 'كهرباء':
        case 'electricity':
          expenseAccountId = 5220;
          expenseAccountCode = '5220';
          expenseAccountName = 'مصروف الكهرباء';
          break;
        case 'مواصلات':
        case 'transportation':
          expenseAccountId = 5230;
          expenseAccountCode = '5230';
          expenseAccountName = 'مصروف المواصلات';
          break;
        case 'رواتب':
        case 'salaries':
          expenseAccountId = 5240;
          expenseAccountCode = '5240';
          expenseAccountName = 'مصروف الرواتب';
          break;
      }

      final journalLines = <JournalLine>[
        // مدين: المصروف
        JournalLine(
          id: 0,
          accountId: expenseAccountId,
          accountCode: expenseAccountCode,
          accountName: expenseAccountName,
          description: description ?? 'مصروف $expenseType',
          debitAmount: amount,
          creditAmount: 0,
        ),
        // دائن: النقدية
        JournalLine(
          id: 0,
          accountId: 1110,
          accountCode: '1110',
          accountName: 'النقدية',
          description: 'دفع مصروف $expenseType',
          debitAmount: 0,
          creditAmount: amount,
        ),
      ];

      final journalEntry = JournalEntry(
        id: 0,
        reference: 'EXP-${DateTime.now().millisecondsSinceEpoch}',
        date: expenseDate,
        description: description ?? 'مصروف $expenseType',
        lines: journalLines,
        status: EntryStatus.draft,
        createdAt: DateTime.now(),
      );

      final savedEntry = await AccountingService.addJournalEntry(journalEntry);
      await AccountingService.postJournalEntry(savedEntry.id);

      debugPrint('✅ تم إنشاء قيد المصروف بنجاح: ${savedEntry.reference}');
      return savedEntry;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد المصروف: $e');
      return null;
    }
  }
}
