import 'package:flutter/material.dart';
import '../services/financial_reports_service.dart';
import '../widgets/brand_footer.dart';

/// شاشة التقارير المالية المتقدمة
class FinancialReportsScreen extends StatefulWidget {
  const FinancialReportsScreen({super.key});

  @override
  State<FinancialReportsScreen> createState() => _FinancialReportsScreenState();
}

class _FinancialReportsScreenState extends State<FinancialReportsScreen> {
  String _selectedReportType = 'balance_sheet';
  DateTime _selectedDate = DateTime.now();
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 365));
  DateTime _endDate = DateTime.now();
  bool _isLoading = false;
  Map<String, dynamic>? _reportData;

  final Map<String, String> _reportTypes = {
    'balance_sheet': 'قائمة المركز المالي',
    'income_statement': 'قائمة الدخل',
    'trial_balance': 'ميزان المراجعة',
    'financial_ratios': 'النسب المالية',
    'comparative_report': 'التقرير المقارن',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المالية'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _reportData != null ? _printReport : null,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _reportData != null ? _shareReport : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التحكم
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).cardColor,
            child: Column(
              children: [
                // نوع التقرير
                DropdownButtonFormField<String>(
                  value: _selectedReportType,
                  decoration: const InputDecoration(
                    labelText: 'نوع التقرير',
                    border: OutlineInputBorder(),
                  ),
                  items: _reportTypes.entries
                      .map((entry) => DropdownMenuItem(
                            value: entry.key,
                            child: Text(entry.value),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedReportType = value;
                        _reportData = null;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // التواريخ
                if (_selectedReportType == 'income_statement' ||
                    _selectedReportType == 'comparative_report') ...[
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context, true),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'من تاريخ',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(
                              '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context, false),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'إلى تاريخ',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(
                              '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  InkWell(
                    onTap: () => _selectSingleDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'كما في تاريخ',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // زر إنشاء التقرير
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _generateReport,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.assessment),
                    label:
                        Text(_isLoading ? 'جاري الإنشاء...' : 'إنشاء التقرير'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: _reportData == null
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.assessment,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'اختر نوع التقرير والتاريخ ثم اضغط "إنشاء التقرير"',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : _buildReportContent(),
          ),

          const BrandFooter(),
        ],
      ),
    );
  }

  Widget _buildReportContent() {
    if (_reportData == null) return const SizedBox();

    switch (_selectedReportType) {
      case 'balance_sheet':
        return _buildBalanceSheetReport();
      case 'income_statement':
        return _buildIncomeStatementReport();
      case 'trial_balance':
        return _buildTrialBalanceReport();
      case 'financial_ratios':
        return _buildFinancialRatiosReport();
      case 'comparative_report':
        return _buildComparativeReport();
      default:
        return const Center(child: Text('نوع تقرير غير مدعوم'));
    }
  }

  Widget _buildBalanceSheetReport() {
    final data = _reportData!;
    final assets = data['assets'];
    final liabilities = data['liabilities'];
    final equity = data['equity'];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader('قائمة المركز المالي'),
          const SizedBox(height: 24),

          // الأصول
          _buildSection('الأصول', assets['items'], assets['formatted_total']),
          const SizedBox(height: 24),

          // الخصوم
          _buildSection(
              'الخصوم', liabilities['items'], liabilities['formatted_total']),
          const SizedBox(height: 24),

          // حقوق الملكية
          _buildSection(
              'حقوق الملكية', equity['items'], equity['formatted_total']),
          const SizedBox(height: 24),

          // الإجماليات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTotalRow('إجمالي الأصول', assets['formatted_total']),
                  const Divider(),
                  _buildTotalRow('إجمالي الخصوم وحقوق الملكية',
                      '${(liabilities['total'] + equity['total']).toStringAsFixed(2)} ${data['currency']}'),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'حالة التوازن:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color:
                              data['is_balanced'] ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          data['is_balanced'] ? 'متوازنة' : 'غير متوازنة',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementReport() {
    final data = _reportData!;
    final revenues = data['revenues'];
    final expenses = data['expenses'];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader('قائمة الدخل'),
          const SizedBox(height: 24),

          // الإيرادات
          _buildSection(
              'الإيرادات', revenues['items'], revenues['formatted_total']),
          const SizedBox(height: 24),

          // المصروفات
          _buildSection(
              'المصروفات', expenses['items'], expenses['formatted_total']),
          const SizedBox(height: 24),

          // النتائج
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTotalRow(
                      'إجمالي الإيرادات', revenues['formatted_total']),
                  _buildTotalRow(
                      'إجمالي المصروفات', expenses['formatted_total']),
                  const Divider(thickness: 2),
                  _buildTotalRow('صافي الدخل', data['formatted_net_income']),
                  _buildTotalRow('هامش الربح', data['formatted_profit_margin']),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrialBalanceReport() {
    final data = _reportData!;
    final accounts = data['accounts'] as List;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader('ميزان المراجعة'),
          const SizedBox(height: 24),

          // جدول الحسابات
          Card(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('رمز الحساب')),
                  DataColumn(label: Text('اسم الحساب')),
                  DataColumn(label: Text('مدين')),
                  DataColumn(label: Text('دائن')),
                ],
                rows: accounts
                    .map<DataRow>((account) => DataRow(
                          cells: [
                            DataCell(Text(account['account_code'])),
                            DataCell(Text(account['account_name'])),
                            DataCell(Text(account['formatted_debit'])),
                            DataCell(Text(account['formatted_credit'])),
                          ],
                        ))
                    .toList(),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // الإجماليات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTotalRow(
                      'إجمالي المدين', data['formatted_total_debit']),
                  _buildTotalRow(
                      'إجمالي الدائن', data['formatted_total_credit']),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'حالة التوازن:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color:
                              data['is_balanced'] ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          data['is_balanced'] ? 'متوازن' : 'غير متوازن',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRatiosReport() {
    final data = _reportData!;
    final ratios = data['ratios'];
    final assessment = data['overall_assessment'];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader('تقرير النسب المالية'),
          const SizedBox(height: 24),

          // التقييم العام
          Card(
            color: _getAssessmentColor(assessment['rating']),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'التقييم العام: ${assessment['rating']}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'النقاط: ${assessment['score']} من ${assessment['max_score']}',
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    assessment['description'],
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // النسب المالية
          ...ratios.entries.map<Widget>((entry) {
            final ratio = entry.value;
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ratio['description'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'القيمة: ${ratio['formatted']}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getInterpretationColor(
                                ratio['interpretation']),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            ratio['interpretation'],
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildComparativeReport() {
    final data = _reportData!;
    final comparison = data['comparison'];
    final analysis = data['analysis'];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التقرير
          _buildReportHeader('التقرير المقارن'),
          const SizedBox(height: 24),

          // مقارنة الإيرادات
          _buildComparisonCard(
            'الإيرادات',
            comparison['revenue'],
            Icons.trending_up,
          ),

          const SizedBox(height: 16),

          // مقارنة صافي الدخل
          _buildComparisonCard(
            'صافي الدخل',
            comparison['net_income'],
            Icons.account_balance,
          ),

          const SizedBox(height: 24),

          // التحليل
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'التحليل',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildAnalysisRow(
                      'اتجاه الإيرادات', analysis['revenue_trend']),
                  _buildAnalysisRow('اتجاه الدخل', analysis['income_trend']),
                  _buildAnalysisRow(
                      'الأداء العام', analysis['overall_performance']),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportHeader(String title) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _reportData!['company_name'],
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 4),
            Text(
              _getDateText(),
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List items, String total) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...items
                .map<Widget>((item) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                                '${item['account_code']} - ${item['account_name']}'),
                          ),
                          Text(
                            item['formatted_amount'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ))
                ,
            const Divider(),
            _buildTotalRow('الإجمالي', total),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonCard(
      String title, Map<String, dynamic> data, IconData icon) {
    final isPositive = data['growth_rate'] >= 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('السنة الحالية'),
                    Text(
                      data['formatted_current'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    const Text('السنة السابقة'),
                    Text(
                      data['formatted_previous'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isPositive
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'معدل النمو',
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    data['formatted_growth'],
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getAnalysisColor(value),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Color _getAssessmentColor(String rating) {
    switch (rating) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد':
        return Colors.blue;
      case 'متوسط':
        return Colors.orange;
      case 'ضعيف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getInterpretationColor(String interpretation) {
    switch (interpretation) {
      case 'ممتاز':
        return Colors.green;
      case 'جيد':
        return Colors.blue;
      case 'ضعيف':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getAnalysisColor(String value) {
    if (value.contains('نمو') ||
        value.contains('تحسن') ||
        value.contains('إيجابي')) {
      return Colors.green;
    } else if (value.contains('انخفاض') ||
        value.contains('تراجع') ||
        value.contains('تحسين')) {
      return Colors.red;
    }
    return Colors.orange;
  }

  String _getDateText() {
    if (_selectedReportType == 'income_statement' ||
        _selectedReportType == 'comparative_report') {
      return 'من ${_startDate.day}/${_startDate.month}/${_startDate.year} إلى ${_endDate.day}/${_endDate.month}/${_endDate.year}';
    } else {
      return 'كما في ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}';
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
        } else {
          _endDate = date;
        }
        _reportData = null;
      });
    }
  }

  Future<void> _selectSingleDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
        _reportData = null;
      });
    }
  }

  Future<void> _generateReport() async {
    setState(() => _isLoading = true);

    try {
      Map<String, dynamic> reportData;

      switch (_selectedReportType) {
        case 'balance_sheet':
          reportData = await FinancialReportsService.generateBalanceSheet(
            asOfDate: _selectedDate,
          );
          break;
        case 'income_statement':
          reportData = await FinancialReportsService.generateIncomeStatement(
            startDate: _startDate,
            endDate: _endDate,
          );
          break;
        case 'trial_balance':
          reportData = await FinancialReportsService.generateTrialBalance(
            asOfDate: _selectedDate,
          );
          break;
        case 'financial_ratios':
          reportData = await FinancialReportsService.generateFinancialRatios(
            asOfDate: _selectedDate,
          );
          break;
        case 'comparative_report':
          reportData = await FinancialReportsService.generateComparativeReport(
            currentYearEnd: _endDate,
            previousYearEnd:
                DateTime(_endDate.year - 1, _endDate.month, _endDate.day),
          );
          break;
        default:
          throw Exception('نوع تقرير غير مدعوم');
      }

      setState(() {
        _reportData = reportData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إنشاء التقرير: ${e.toString()}')),
        );
      }
    }
  }

  void _printReport() {
    // TODO: تنفيذ طباعة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة الطباعة قيد التطوير')),
    );
  }

  void _shareReport() {
    // TODO: تنفيذ مشاركة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة المشاركة قيد التطوير')),
    );
  }
}
