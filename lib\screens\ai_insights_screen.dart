import 'package:flutter/material.dart';
import '../services/ai_insights_service.dart';
import '../widgets/brand_footer.dart';

/// شاشة الرؤى الذكية والتحليلات المدعومة بالذكاء الاصطناعي
class AIInsightsScreen extends StatefulWidget {
  const AIInsightsScreen({super.key});

  @override
  State<AIInsightsScreen> createState() => _AIInsightsScreenState();
}

class _AIInsightsScreenState extends State<AIInsightsScreen> {
  List<BusinessInsight> _insights = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadInsights();
  }

  Future<void> _loadInsights() async {
    setState(() => _isLoading = true);

    try {
      final insights = await AIInsightsService.generateBusinessInsights();
      if (mounted) {
        setState(() {
          _insights = insights;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل الرؤى: ${e.toString()}')),
        );
      }
    }
  }

  List<BusinessInsight> get _filteredInsights {
    if (_selectedFilter == 'all') return _insights;

    final filterType = InsightType.values.firstWhere(
      (type) => type.toString().split('.').last == _selectedFilter,
      orElse: () => InsightType.info,
    );

    return _insights.where((insight) => insight.type == filterType).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.white),
            SizedBox(width: 8),
            Text('الرؤى الذكية'),
          ],
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInsights,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلترة
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).cardColor,
            child: Row(
              children: [
                const Text(
                  'فلترة حسب النوع:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip('all', 'الكل', Colors.grey),
                        const SizedBox(width: 8),
                        _buildFilterChip('positive', 'إيجابي', Colors.green),
                        const SizedBox(width: 8),
                        _buildFilterChip('warning', 'تحذير', Colors.orange),
                        const SizedBox(width: 8),
                        _buildFilterChip('critical', 'حرج', Colors.red),
                        const SizedBox(width: 8),
                        _buildFilterChip('info', 'معلومات', Colors.blue),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // المحتوى
          Expanded(
            child: _isLoading
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('🤖 جاري تحليل البيانات...'),
                      ],
                    ),
                  )
                : _filteredInsights.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.lightbulb_outline,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _selectedFilter == 'all'
                                  ? 'لا توجد رؤى متاحة حالياً'
                                  : 'لا توجد رؤى من هذا النوع',
                              style: const TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'أضف المزيد من البيانات للحصول على رؤى ذكية',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadInsights,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredInsights.length,
                          itemBuilder: (context, index) {
                            final insight = _filteredInsights[index];
                            return _buildInsightCard(insight);
                          },
                        ),
                      ),
          ),

          const BrandFooter(),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, Color color) {
    final isSelected = _selectedFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() => _selectedFilter = value);
      },
      backgroundColor: color.withValues(alpha: 0.1),
      selectedColor: color.withValues(alpha: 0.2),
      checkmarkColor: color,
      labelStyle: TextStyle(
        color: isSelected ? color : Colors.grey[600],
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildInsightCard(BusinessInsight insight) {
    final color = _getInsightColor(insight.type);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      insight.icon,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          insight.title,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            _buildPriorityIndicator(insight.priority),
                            const SizedBox(width: 8),
                            Text(
                              _getInsightTypeName(insight.type),
                              style: TextStyle(
                                color: color,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (insight.actionable)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'قابل للتنفيذ',
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    insight.description,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                    ),
                  ),

                  // التوصيات
                  if (insight.recommendations.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'التوصيات:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...insight.recommendations.map((recommendation) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(top: 6),
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  recommendation,
                                  style: const TextStyle(height: 1.4),
                                ),
                              ),
                            ],
                          ),
                        )),
                  ],

                  // البيانات الإضافية
                  if (insight.data != null && insight.data!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    ExpansionTile(
                      title: const Text(
                        'تفاصيل إضافية',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: insight.data!.entries
                                .map((entry) => Padding(
                                      padding: const EdgeInsets.only(bottom: 4),
                                      child: Text(
                                        '${entry.key}: ${entry.value}',
                                        style: const TextStyle(
                                          fontFamily: 'monospace',
                                          fontSize: 12,
                                        ),
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                      ],
                    ),
                  ],

                  // وقت الإنشاء
                  const SizedBox(height: 12),
                  Text(
                    'تم الإنشاء: ${_formatDateTime(insight.createdAt)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityIndicator(int priority) {
    final stars = List.generate(
        5,
        (index) => Icon(
              index < priority ? Icons.star : Icons.star_border,
              size: 16,
              color: Colors.amber,
            ));

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: stars,
    );
  }

  Color _getInsightColor(InsightType type) {
    switch (type) {
      case InsightType.positive:
        return Colors.green;
      case InsightType.warning:
        return Colors.orange;
      case InsightType.critical:
        return Colors.red;
      case InsightType.info:
        return Colors.blue;
    }
  }

  String _getInsightTypeName(InsightType type) {
    switch (type) {
      case InsightType.positive:
        return 'إيجابي';
      case InsightType.warning:
        return 'تحذير';
      case InsightType.critical:
        return 'حرج';
      case InsightType.info:
        return 'معلومات';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
