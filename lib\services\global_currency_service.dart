import 'package:flutter/foundation.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';
import '../services/accounting_service.dart';
import '../models/journal_entry.dart';

/// خدمة العملات العالمية المتقدمة
class GlobalCurrencyService {
  static const String _exchangeRatesBox = 'exchange_rates';
  static const String _currencyHistoryBox = 'currency_history';

  /// العملات المدعومة
  static const Map<String, CurrencyInfo> supportedCurrencies = {
    'USD': CurrencyInfo(
      code: 'USD',
      name: 'الدولار الأمريكي',
      nameEn: 'US Dollar',
      symbol: '\$',
      flag: '🇺🇸',
      isBaseCurrency: true,
    ),
    'EUR': CurrencyInfo(
      code: 'EUR',
      name: 'اليورو',
      nameEn: 'Euro',
      symbol: '€',
      flag: '🇪🇺',
    ),
    'GBP': CurrencyInfo(
      code: 'GBP',
      name: 'الجنيه الإسترليني',
      nameEn: 'British Pound',
      symbol: '£',
      flag: '🇬🇧',
    ),
    'SYP': CurrencyInfo(
      code: 'SYP',
      name: 'الليرة السورية',
      nameEn: 'Syrian Pound',
      symbol: 'ل.س',
      flag: '🇸🇾',
    ),
    'SAR': CurrencyInfo(
      code: 'SAR',
      name: 'الريال السعودي',
      nameEn: 'Saudi Riyal',
      symbol: 'ر.س',
      flag: '🇸🇦',
    ),
    'AED': CurrencyInfo(
      code: 'AED',
      name: 'الدرهم الإماراتي',
      nameEn: 'UAE Dirham',
      symbol: 'د.إ',
      flag: '🇦🇪',
    ),
    'EGP': CurrencyInfo(
      code: 'EGP',
      name: 'الجنيه المصري',
      nameEn: 'Egyptian Pound',
      symbol: 'ج.م',
      flag: '🇪🇬',
    ),
    'JOD': CurrencyInfo(
      code: 'JOD',
      name: 'الدينار الأردني',
      nameEn: 'Jordanian Dinar',
      symbol: 'د.أ',
      flag: '🇯🇴',
    ),
    'LBP': CurrencyInfo(
      code: 'LBP',
      name: 'الليرة اللبنانية',
      nameEn: 'Lebanese Pound',
      symbol: 'ل.ل',
      flag: '🇱🇧',
    ),
    'TRY': CurrencyInfo(
      code: 'TRY',
      name: 'الليرة التركية',
      nameEn: 'Turkish Lira',
      symbol: '₺',
      flag: '🇹🇷',
    ),
  };

  /// تهيئة خدمة العملات
  static Future<void> initialize() async {
    try {
      // تعيين أسعار صرف افتراضية
      await _setDefaultExchangeRates();

      debugPrint('✅ تم تهيئة خدمة العملات العالمية');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة العملات: $e');
      rethrow;
    }
  }

  /// الحصول على جميع أسعار الصرف
  static Future<Map<String, double>> getAllExchangeRates() async {
    try {
      final ratesData =
          await OfflineDatabaseService.getBoxData(_exchangeRatesBox);
      final rates = <String, double>{};

      for (var entry in ratesData.entries) {
        final rateInfo = Map<String, dynamic>.from(entry.value);
        rates[entry.key] = (rateInfo['rate'] ?? 1.0).toDouble();
      }

      return rates;
    } catch (e) {
      debugPrint('❌ فشل في جلب أسعار الصرف: $e');
      return {};
    }
  }

  /// الحصول على سعر صرف عملة محددة
  static Future<double> getExchangeRate(String currencyCode) async {
    try {
      final rateData = await OfflineDatabaseService.getBoxItem(
        _exchangeRatesBox,
        currencyCode,
      );

      if (rateData != null) {
        return (rateData['rate'] ?? 1.0).toDouble();
      }

      return 1.0;
    } catch (e) {
      debugPrint('❌ فشل في جلب سعر الصرف لـ $currencyCode: $e');
      return 1.0;
    }
  }

  /// تحديث سعر صرف عملة واحدة (الميزة المميزة!)
  static Future<void> updateSingleCurrencyRate({
    required String currencyCode,
    required double newRate,
    bool createJournalEntry = true,
    String? notes,
  }) async {
    try {
      final oldRate = await getExchangeRate(currencyCode);

      if ((newRate - oldRate).abs() < 0.0001) {
        debugPrint('ℹ️ لا يوجد تغيير في سعر $currencyCode');
        return;
      }

      // حفظ السعر الجديد
      await OfflineDatabaseService.saveBoxItem(
        _exchangeRatesBox,
        currencyCode,
        {
          'rate': newRate,
          'updated_at': DateTime.now().toIso8601String(),
          'previous_rate': oldRate,
          'notes': notes,
        },
      );

      // حفظ في التاريخ
      await _saveCurrencyHistory(currencyCode, oldRate, newRate, notes);

      // تحديث أسعار المنتجات المرتبطة بهذه العملة
      await _updateProductPricesForCurrency(currencyCode, oldRate, newRate);

      // إنشاء قيد محاسبي لتسوية أسعار الصرف (إذا طُلب)
      if (createJournalEntry) {
        await _createCurrencyAdjustmentEntry(currencyCode, oldRate, newRate);
      }

      // إرسال إشعار
      await OfflineNotificationService.showDollarRateUpdateNotification(
        oldRate,
        newRate,
      );

      debugPrint('✅ تم تحديث سعر $currencyCode من $oldRate إلى $newRate');
    } catch (e) {
      debugPrint('❌ فشل في تحديث سعر $currencyCode: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث سعر $currencyCode: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث جميع أسعار الصرف (الزر السحري!)
  static Future<void> updateAllExchangeRates(
      Map<String, double> newRates) async {
    try {
      final oldRates = await getAllExchangeRates();
      final changedCurrencies = <String>[];

      for (var entry in newRates.entries) {
        final currencyCode = entry.key;
        final newRate = entry.value;
        final oldRate = oldRates[currencyCode] ?? 1.0;

        if ((newRate - oldRate).abs() >= 0.0001) {
          changedCurrencies.add(currencyCode);
          await updateSingleCurrencyRate(
            currencyCode: currencyCode,
            newRate: newRate,
            createJournalEntry: false, // سننشئ قيد واحد شامل
          );
        }
      }

      // إنشاء قيد محاسبي شامل لجميع التغييرات
      if (changedCurrencies.isNotEmpty) {
        await _createBulkCurrencyAdjustmentEntry(
            oldRates, newRates, changedCurrencies);
      }

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث أسعار ${changedCurrencies.length} عملة بنجاح',
      );

      debugPrint('✅ تم تحديث أسعار ${changedCurrencies.length} عملة');
    } catch (e) {
      debugPrint('❌ فشل في تحديث أسعار الصرف: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث أسعار الصرف: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحويل مبلغ من عملة إلى أخرى
  static Future<double> convertAmount({
    required double amount,
    required String fromCurrency,
    required String toCurrency,
  }) async {
    try {
      if (fromCurrency == toCurrency) return amount;

      final fromRate = await getExchangeRate(fromCurrency);
      final toRate = await getExchangeRate(toCurrency);

      // تحويل إلى العملة الأساسية (USD) ثم إلى العملة المطلوبة
      final amountInUSD = amount / fromRate;
      final convertedAmount = amountInUSD * toRate;

      return convertedAmount;
    } catch (e) {
      debugPrint('❌ فشل في تحويل المبلغ: $e');
      return amount;
    }
  }

  /// الحصول على تاريخ أسعار عملة محددة
  static Future<List<CurrencyHistoryEntry>> getCurrencyHistory(
      String currencyCode) async {
    try {
      final historyData =
          await OfflineDatabaseService.getBoxData(_currencyHistoryBox);
      final history = <CurrencyHistoryEntry>[];

      for (var entry in historyData.values) {
        final historyEntry = Map<String, dynamic>.from(entry);
        if (historyEntry['currency_code'] == currencyCode) {
          history.add(CurrencyHistoryEntry.fromJson(historyEntry));
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      history.sort((a, b) => b.date.compareTo(a.date));

      return history;
    } catch (e) {
      debugPrint('❌ فشل في جلب تاريخ العملة: $e');
      return [];
    }
  }

  // ==================== وظائف مساعدة ====================

  /// تعيين أسعار صرف افتراضية
  static Future<void> _setDefaultExchangeRates() async {
    final defaultRates = {
      'USD': 1.0, // العملة الأساسية
      'EUR': 0.85,
      'GBP': 0.73,
      'SYP': 13500.0,
      'SAR': 3.75,
      'AED': 3.67,
      'EGP': 48.5,
      'JOD': 0.71,
      'LBP': 89500.0,
      'TRY': 27.5,
    };

    for (var entry in defaultRates.entries) {
      final existingRate = await getExchangeRate(entry.key);
      if (existingRate == 1.0 && entry.key != 'USD') {
        // لا يوجد سعر محفوظ، استخدم الافتراضي
        await OfflineDatabaseService.saveBoxItem(
          _exchangeRatesBox,
          entry.key,
          {
            'rate': entry.value,
            'updated_at': DateTime.now().toIso8601String(),
            'is_default': true,
          },
        );
      }
    }
  }

  /// حفظ تاريخ العملة
  static Future<void> _saveCurrencyHistory(
    String currencyCode,
    double oldRate,
    double newRate,
    String? notes,
  ) async {
    final historyEntry = CurrencyHistoryEntry(
      id: DateTime.now().millisecondsSinceEpoch,
      currencyCode: currencyCode,
      oldRate: oldRate,
      newRate: newRate,
      changePercent: ((newRate - oldRate) / oldRate) * 100,
      date: DateTime.now(),
      notes: notes,
    );

    await OfflineDatabaseService.saveBoxItem(
      _currencyHistoryBox,
      historyEntry.id.toString(),
      historyEntry.toJson(),
    );
  }

  /// تحديث أسعار المنتجات لعملة محددة
  static Future<void> _updateProductPricesForCurrency(
    String currencyCode,
    double oldRate,
    double newRate,
  ) async {
    // سيتم تنفيذ هذا في ProductProvider
    debugPrint('🔄 تحديث أسعار المنتجات لعملة $currencyCode');
  }

  /// إنشاء قيد تسوية أسعار الصرف
  static Future<void> _createCurrencyAdjustmentEntry(
    String currencyCode,
    double oldRate,
    double newRate,
  ) async {
    try {
      final adjustments = <Map<String, dynamic>>[];
      final difference = newRate - oldRate;
      final isGain = difference > 0;

      // هنا يمكن إضافة منطق أكثر تعقيداً لحساب تأثير التغيير على الحسابات
      adjustments.add({
        'amount': difference.abs(),
        'account_id': isGain ? 16 : 17, // حساب أرباح/خسائر الصرف
        'account_code': isGain ? '4200' : '5200',
        'account_name': isGain ? 'أرباح أسعار الصرف' : 'خسائر أسعار الصرف',
        'is_gain': isGain,
      });

      final entry = JournalEntryHelper.createCurrencyAdjustmentEntry(
        reference: 'CUR${DateTime.now().millisecondsSinceEpoch}',
        date: DateTime.now(),
        adjustments: adjustments,
        description: 'تسوية سعر صرف $currencyCode من $oldRate إلى $newRate',
      );

      await AccountingService.addJournalEntry(entry);
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد تسوية العملة: $e');
    }
  }

  /// إنشاء قيد تسوية شامل لعدة عملات
  static Future<void> _createBulkCurrencyAdjustmentEntry(
    Map<String, double> oldRates,
    Map<String, double> newRates,
    List<String> changedCurrencies,
  ) async {
    try {
      final adjustments = <Map<String, dynamic>>[];

      for (var currencyCode in changedCurrencies) {
        final oldRate = oldRates[currencyCode] ?? 1.0;
        final newRate = newRates[currencyCode] ?? 1.0;
        final difference = newRate - oldRate;
        final isGain = difference > 0;

        adjustments.add({
          'amount': difference.abs(),
          'account_id': isGain ? 16 : 17,
          'account_code': isGain ? '4200' : '5200',
          'account_name': isGain ? 'أرباح أسعار الصرف' : 'خسائر أسعار الصرف',
          'is_gain': isGain,
        });
      }

      final entry = JournalEntryHelper.createCurrencyAdjustmentEntry(
        reference: 'BULK${DateTime.now().millisecondsSinceEpoch}',
        date: DateTime.now(),
        adjustments: adjustments,
        description:
            'تسوية شاملة لأسعار الصرف - ${changedCurrencies.join(', ')}',
      );

      await AccountingService.addJournalEntry(entry);
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قيد التسوية الشامل: $e');
    }
  }
}

/// معلومات العملة
class CurrencyInfo {
  final String code;
  final String name;
  final String nameEn;
  final String symbol;
  final String flag;
  final bool isBaseCurrency;

  const CurrencyInfo({
    required this.code,
    required this.name,
    required this.nameEn,
    required this.symbol,
    required this.flag,
    this.isBaseCurrency = false,
  });
}

/// سجل تاريخ العملة
class CurrencyHistoryEntry {
  final int id;
  final String currencyCode;
  final double oldRate;
  final double newRate;
  final double changePercent;
  final DateTime date;
  final String? notes;

  const CurrencyHistoryEntry({
    required this.id,
    required this.currencyCode,
    required this.oldRate,
    required this.newRate,
    required this.changePercent,
    required this.date,
    this.notes,
  });

  factory CurrencyHistoryEntry.fromJson(Map<String, dynamic> json) {
    return CurrencyHistoryEntry(
      id: json['id'] ?? 0,
      currencyCode: json['currency_code'] ?? '',
      oldRate: (json['old_rate'] ?? 0.0).toDouble(),
      newRate: (json['new_rate'] ?? 0.0).toDouble(),
      changePercent: (json['change_percent'] ?? 0.0).toDouble(),
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currency_code': currencyCode,
      'old_rate': oldRate,
      'new_rate': newRate,
      'change_percent': changePercent,
      'date': date.toIso8601String(),
      'notes': notes,
    };
  }
}
