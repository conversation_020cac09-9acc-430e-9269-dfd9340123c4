import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/warehouse_provider.dart';
import '../mobile_main.dart';

class MobileWarehousesScreen extends StatefulWidget {
  const MobileWarehousesScreen({super.key});

  @override
  State<MobileWarehousesScreen> createState() => _MobileWarehousesScreenState();
}

class _MobileWarehousesScreenState extends State<MobileWarehousesScreen> {
  @override
  void initState() {
    super.initState();
    _loadWarehouses();
  }

  Future<void> _loadWarehouses() async {
    await context.read<WarehouseProvider>().fetchWarehouses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MobileAppBar(
        title: 'المخازن',
        automaticallyImplyLeading: false,
      ),
      body: Consumer<WarehouseProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const MobileLoadingWidget(message: 'جاري تحميل المخازن...');
          }

          if (provider.warehouses.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: _loadWarehouses,
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: provider.warehouses.length,
              itemBuilder: (context, index) {
                final warehouse = provider.warehouses[index];
                return MobileCard(
                  child: MobileListTile(
                    leading: CircleAvatar(
                      backgroundColor: warehouse.isActive 
                          ? (warehouse.isMainWarehouse ? Colors.blue : Colors.green)
                          : Colors.grey,
                      child: Icon(
                        warehouse.isMainWarehouse ? Icons.home : Icons.warehouse,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      warehouse.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الرمز: ${warehouse.code}'),
                        Text('الموقع: ${warehouse.location}'),
                        Text('المدير: ${warehouse.manager}'),
                        if (!warehouse.isActive)
                          const Text(
                            'غير نشط',
                            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                          ),
                        if (warehouse.isMainWarehouse)
                          const Text(
                            'المخزن الرئيسي',
                            style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
                          ),
                      ],
                    ),
                    trailing: Icon(
                      warehouse.isActive ? Icons.check_circle : Icons.warning,
                      color: warehouse.isActive ? Colors.green : Colors.orange,
                    ),
                    onTap: () => _showWarehouseDetails(warehouse),
                  ),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: MobileFloatingActionButton(
        onPressed: _addWarehouse,
        icon: Icons.add,
        tooltip: 'إضافة مخزن',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warehouse,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مخازن',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة مخزن جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addWarehouse,
            icon: const Icon(Icons.add),
            label: const Text('إضافة مخزن'),
          ),
        ],
      ),
    );
  }

  void _showWarehouseDetails(warehouse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(warehouse.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الرمز', warehouse.code),
            _buildDetailRow('الموقع', warehouse.location),
            _buildDetailRow('المدير', warehouse.manager),
            if (warehouse.phone != null)
              _buildDetailRow('الهاتف', warehouse.phone!),
            if (warehouse.email != null)
              _buildDetailRow('البريد الإلكتروني', warehouse.email!),
            if (warehouse.address != null)
              _buildDetailRow('العنوان', warehouse.address!),
            if (warehouse.description != null)
              _buildDetailRow('الوصف', warehouse.description!),
            _buildDetailRow('الحالة', warehouse.isActive ? 'نشط' : 'غير نشط'),
            _buildDetailRow('النوع', warehouse.isMainWarehouse ? 'رئيسي' : 'فرعي'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _addWarehouse() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إضافة مخزن ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
