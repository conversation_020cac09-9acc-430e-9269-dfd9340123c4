import 'package:flutter/material.dart';
import '../models/report.dart';
import '../services/offline_database_service.dart';

class ReportProvider with ChangeNotifier {
  SalesReport? _report;
  bool _isLoading = false;
  String _error = '';

  ReportProvider();

  SalesReport? get report => _report;
  bool get isLoading => _isLoading;
  String get error => _error;

  Future<void> fetchReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final queryParams = <String, String>{};
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      // إنشاء تقرير من البيانات المحلية
      final invoices = await OfflineDatabaseService.getAllInvoices();
      final customers = await OfflineDatabaseService.getAllCustomers();

      // حساب إحصائيات التقرير
      final totalSalesSyp = invoices.fold<double>(
          0, (sum, invoice) => sum + invoice.totalAmountSyp);
      final totalSalesUsd = invoices.fold<double>(
          0, (sum, invoice) => sum + invoice.totalAmountUsd);
      final totalDebtSyp = customers.fold<double>(
          0, (sum, customer) => sum + customer.totalDebt);
      final totalDebtUsd = totalDebtSyp / 13500; // تحويل تقريبي

      // إنشاء قوائم فارغة للبيانات المفصلة
      final dailySales = <DailySales>[];
      final topProducts = <TopProduct>[];
      final customerDebts = customers
          .map((customer) => CustomerDebt(
                customerId: customer.id,
                customerName: customer.name,
                debtAmountSyp: customer.totalDebt,
                debtAmountUsd: customer.totalDebt / 13500,
                lastPurchaseDate: customer.createdAt,
              ))
          .toList();

      _report = SalesReport(
        totalSalesSyp: totalSalesSyp,
        totalSalesUsd: totalSalesUsd,
        totalDebtSyp: totalDebtSyp,
        totalDebtUsd: totalDebtUsd,
        dailySales: dailySales,
        topProducts: topProducts,
        customerDebts: customerDebts,
      );
    } catch (e) {
      _error = 'فشل في تحميل التقرير';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تقرير المبيعات اليومية
  Map<String, dynamic> getDailySalesReport(List<dynamic> invoices) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final todayInvoices = invoices.where((invoice) {
      final invoiceDate =
          DateTime(invoice.date.year, invoice.date.month, invoice.date.day);
      return invoiceDate.isAtSameMomentAs(today);
    }).toList();

    final totalSales =
        todayInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp);
    final totalInvoices = todayInvoices.length;

    return {
      'date': today,
      'total_sales': totalSales,
      'total_invoices': totalInvoices,
      'average_sale': totalInvoices > 0 ? totalSales / totalInvoices : 0.0,
      'invoices': todayInvoices,
    };
  }

  /// تقرير المبيعات الأسبوعية
  Map<String, dynamic> getWeeklySalesReport(List<dynamic> invoices) {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekStartDate =
        DateTime(weekStart.year, weekStart.month, weekStart.day);

    final weekInvoices = invoices.where((invoice) {
      return invoice.date.isAfter(weekStartDate) ||
          invoice.date.isAtSameMomentAs(weekStartDate);
    }).toList();

    final totalSales =
        weekInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp);
    final totalInvoices = weekInvoices.length;

    return {
      'week_start': weekStartDate,
      'total_sales': totalSales,
      'total_invoices': totalInvoices,
      'average_sale': totalInvoices > 0 ? totalSales / totalInvoices : 0.0,
      'invoices': weekInvoices,
      'daily_average': totalSales / 7,
    };
  }

  /// تقرير أفضل المنتجات مبيعاً
  List<Map<String, dynamic>> getTopSellingProducts(List<dynamic> invoices,
      {int limit = 10}) {
    final productSales = <String, Map<String, dynamic>>{};

    for (var invoice in invoices) {
      for (var item in invoice.items) {
        if (productSales.containsKey(item.productId)) {
          productSales[item.productId]!['quantity'] += item.quantity;
          productSales[item.productId]!['total_sales'] += item.totalSyp;
        } else {
          productSales[item.productId] = {
            'product_id': item.productId,
            'product_name': item.productName,
            'quantity': item.quantity,
            'total_sales': item.totalSyp,
          };
        }
      }
    }

    final sortedProducts = productSales.values.toList();
    sortedProducts.sort((a, b) => b['quantity'].compareTo(a['quantity']));

    return sortedProducts.take(limit).toList();
  }
}
