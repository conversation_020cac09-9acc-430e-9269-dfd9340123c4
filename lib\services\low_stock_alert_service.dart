import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../services/advanced_notification_service.dart';
import '../services/offline_storage_service.dart';

/// خدمة تنبيهات المخزون المنخفض
class LowStockAlertService {
  static const String _lastAlertKey = 'last_low_stock_alert';
  static const Duration _alertCooldown = Duration(hours: 6); // تنبيه كل 6 ساعات

  /// فحص المخزون المنخفض وإرسال التنبيهات
  static Future<void> checkLowStockAndAlert(List<Product> products) async {
    try {
      final lowStockProducts = _getLowStockProducts(products);

      if (lowStockProducts.isNotEmpty && _shouldSendAlert()) {
        await _sendLowStockAlert(lowStockProducts);
        await _updateLastAlertTime();
      }
    } catch (e) {
      debugPrint('خطأ في فحص المخزون المنخفض: $e');
    }
  }

  /// الحصول على المنتجات منخفضة المخزون
  static List<Product> _getLowStockProducts(List<Product> products) {
    return products.where((product) => product.isLowStock()).toList();
  }

  /// فحص إذا كان يجب إرسال تنبيه
  static bool _shouldSendAlert() {
    final lastAlert = OfflineStorageService.getSetting<String>(_lastAlertKey);
    if (lastAlert == null) return true;

    final lastAlertTime = DateTime.tryParse(lastAlert);
    if (lastAlertTime == null) return true;

    return DateTime.now().difference(lastAlertTime) > _alertCooldown;
  }

  /// إرسال تنبيه المخزون المنخفض
  static Future<void> _sendLowStockAlert(List<Product> lowStockProducts) async {
    await AdvancedNotificationService.scheduleStockAlerts(lowStockProducts);
  }

  /// تحديث وقت آخر تنبيه
  static Future<void> _updateLastAlertTime() async {
    await OfflineStorageService.saveSetting(
      _lastAlertKey,
      DateTime.now().toIso8601String(),
    );
  }

  /// الحصول على إحصائيات المخزون
  static Map<String, dynamic> getStockStatistics(List<Product> products) {
    final totalProducts = products.length;
    final lowStockProducts = _getLowStockProducts(products);
    final outOfStockProducts = products.where((p) => p.isOutOfStock()).toList();

    final totalValue = products.fold(0.0,
        (sum, product) => sum + (product.purchasePriceSyp * product.quantity));

    return {
      'total_products': totalProducts,
      'low_stock_count': lowStockProducts.length,
      'out_of_stock_count': outOfStockProducts.length,
      'healthy_stock_count': totalProducts - lowStockProducts.length,
      'total_inventory_value': totalValue,
      'low_stock_products': lowStockProducts,
      'out_of_stock_products': outOfStockProducts,
      'stock_health_percentage': totalProducts > 0
          ? ((totalProducts - lowStockProducts.length) / totalProducts * 100)
          : 100.0,
    };
  }

  /// فحص دوري للمخزون (يُستدعى من background service)
  static Future<void> performPeriodicStockCheck(List<Product> products) async {
    await checkLowStockAndAlert(products);

    // حفظ آخر فحص
    await OfflineStorageService.saveSetting(
      'last_stock_check',
      DateTime.now().toIso8601String(),
    );
  }

  /// الحصول على توصيات إعادة الطلب
  static List<Map<String, dynamic>> getReorderRecommendations(
      List<Product> products) {
    final lowStockProducts = _getLowStockProducts(products);

    return lowStockProducts.map((product) {
      final recommendedQuantity = _calculateRecommendedQuantity(product);
      final estimatedCost = recommendedQuantity * product.purchasePriceSyp;

      return {
        'product': product,
        'current_quantity': product.quantity,
        'min_quantity': product.minQuantity,
        'recommended_quantity': recommendedQuantity,
        'estimated_cost_syp': estimatedCost,
        'estimated_cost_usd': estimatedCost / 13500, // افتراض سعر دولار
        'urgency_level': _getUrgencyLevel(product),
      };
    }).toList();
  }

  /// حساب الكمية الموصى بطلبها
  static int _calculateRecommendedQuantity(Product product) {
    // خوارزمية بسيطة: 3 أضعاف الحد الأدنى
    return (product.minQuantity * 3).round();
  }

  /// تحديد مستوى الإلحاح
  static String _getUrgencyLevel(Product product) {
    if (product.quantity <= 0) return 'عاجل جداً';
    if (product.quantity <= product.minQuantity * 0.5) return 'عاجل';
    if (product.quantity <= product.minQuantity) return 'متوسط';
    return 'منخفض';
  }

  /// تصدير تقرير المخزون المنخفض
  static Map<String, dynamic> exportLowStockReport(List<Product> products) {
    final statistics = getStockStatistics(products);
    final recommendations = getReorderRecommendations(products);

    return {
      'report_date': DateTime.now().toIso8601String(),
      'statistics': statistics,
      'recommendations': recommendations,
      'total_reorder_cost_syp': recommendations.fold(
          0.0, (sum, item) => sum + item['estimated_cost_syp']),
      'total_reorder_cost_usd': recommendations.fold(
          0.0, (sum, item) => sum + item['estimated_cost_usd']),
    };
  }
}
