import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/product.dart';
import '../models/customer.dart';

/// خدمة الإشعارات المتقدمة
class AdvancedNotificationService {
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات المتقدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    _localNotifications = FlutterLocalNotificationsPlugin();

    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications!.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
    debugPrint('✅ تم تهيئة خدمة الإشعارات المتقدمة');
  }

  /// جدولة إشعارات المخزون المنخفض
  static Future<void> scheduleStockAlerts(
      List<Product> lowStockProducts) async {
    if (!_isInitialized) await initialize();

    try {
      // إلغاء الإشعارات المجدولة السابقة للمخزون
      await _cancelNotificationsByType('low_stock');

      for (int i = 0; i < lowStockProducts.length && i < 5; i++) {
        final product = lowStockProducts[i];
        final urgencyLevel = _getStockUrgencyLevel(product);

        await _localNotifications!.show(
          1000 + i,
          '⚠️ تحذير: مخزون منخفض',
          'المنتج "${product.name}" ${_getStockMessage(product)}',
          _getNotificationDetails(urgencyLevel),
          payload: 'low_stock_${product.id}',
        );
      }

      debugPrint('✅ تم جدولة ${lowStockProducts.length} إشعار مخزون منخفض');
    } catch (e) {
      debugPrint('❌ فشل في جدولة إشعارات المخزون: $e');
    }
  }

  /// جدولة تذكيرات الديون المستحقة
  static Future<void> scheduleDebtReminders(
      List<Customer> customersWithDebts) async {
    if (!_isInitialized) await initialize();

    try {
      // إلغاء التذكيرات السابقة للديون
      await _cancelNotificationsByType('debt_reminder');

      for (int i = 0; i < customersWithDebts.length && i < 3; i++) {
        final customer = customersWithDebts[i];

        await _localNotifications!.show(
          2000 + i,
          '💰 تذكير: دين مستحق',
          'العميل "${customer.name}" لديه دين مستحق: ${customer.totalDebt.toStringAsFixed(0)} ل.س',
          _getNotificationDetails(NotificationPriority.high),
          payload: 'debt_reminder_${customer.id}',
        );
      }

      debugPrint('✅ تم جدولة ${customersWithDebts.length} تذكير دين');
    } catch (e) {
      debugPrint('❌ فشل في جدولة تذكيرات الديون: $e');
    }
  }

  /// إشعار تحديث سعر الدولار
  static Future<void> notifyDollarRateChange(
      double oldRate, double newRate) async {
    if (!_isInitialized) await initialize();

    try {
      final changePercent = ((newRate - oldRate) / oldRate * 100);
      final isIncrease = newRate > oldRate;
      final changeText = isIncrease ? 'ارتفع' : 'انخفض';
      final changeIcon = isIncrease ? '📈' : '📉';

      await _showNotification(
        id: 3000,
        title: '$changeIcon تحديث سعر الدولار',
        body:
            'السعر $changeText من ${oldRate.toStringAsFixed(0)} إلى ${newRate.toStringAsFixed(0)} ل.س (${changePercent.toStringAsFixed(1)}%)',
        payload: 'dollar_rate_change',
        priority: NotificationPriority.high,
      );

      debugPrint('✅ تم إرسال إشعار تحديث سعر الدولار');
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار تحديث سعر الدولار: $e');
    }
  }

  /// إشعار نسخة احتياطية تلقائية
  static Future<void> notifyAutoBackupCompleted(String backupPath) async {
    if (!_isInitialized) await initialize();

    try {
      await _showNotification(
        id: 4000,
        title: '💾 نسخة احتياطية تلقائية',
        body: 'تم إنشاء نسخة احتياطية تلقائية بنجاح',
        payload: 'auto_backup_completed',
        priority: NotificationPriority.low,
      );

      debugPrint('✅ تم إرسال إشعار النسخة الاحتياطية التلقائية');
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار النسخة الاحتياطية: $e');
    }
  }

  /// إشعار مزامنة البيانات
  static Future<void> notifyDataSyncCompleted(int syncedItems) async {
    if (!_isInitialized) await initialize();

    try {
      await _showNotification(
        id: 5000,
        title: '🔄 مزامنة البيانات',
        body: 'تم مزامنة $syncedItems عنصر بنجاح مع الخادم',
        payload: 'data_sync_completed',
        priority: NotificationPriority.low,
      );

      debugPrint('✅ تم إرسال إشعار مزامنة البيانات');
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار المزامنة: $e');
    }
  }

  /// إشعار فاتورة جديدة
  static Future<void> notifyNewInvoice(
      String invoiceNumber, double amount) async {
    if (!_isInitialized) await initialize();

    try {
      await _showNotification(
        id: 6000,
        title: '🧾 فاتورة جديدة',
        body:
            'تم إنشاء الفاتورة رقم $invoiceNumber بقيمة ${amount.toStringAsFixed(0)} ل.س',
        payload: 'new_invoice_$invoiceNumber',
        priority: NotificationPriority.normal,
      );

      debugPrint('✅ تم إرسال إشعار الفاتورة الجديدة');
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار الفاتورة: $e');
    }
  }

  /// إشعار تحديث المنتج
  static Future<void> notifyProductUpdate(
      String productName, String updateType) async {
    if (!_isInitialized) await initialize();

    try {
      String title;
      String body;

      switch (updateType) {
        case 'added':
          title = '📦 منتج جديد';
          body = 'تم إضافة المنتج "$productName" للمخزون';
          break;
        case 'updated':
          title = '✏️ تحديث منتج';
          body = 'تم تحديث بيانات المنتج "$productName"';
          break;
        case 'deleted':
          title = '🗑️ حذف منتج';
          body = 'تم حذف المنتج "$productName" من المخزون';
          break;
        default:
          title = '📦 تحديث منتج';
          body = 'تم تحديث المنتج "$productName"';
      }

      await _showNotification(
        id: 7000,
        title: title,
        body: body,
        payload: 'product_update_$updateType',
        priority: NotificationPriority.low,
      );

      debugPrint('✅ تم إرسال إشعار تحديث المنتج');
    } catch (e) {
      debugPrint('❌ فشل في إرسال إشعار تحديث المنتج: $e');
    }
  }

  /// عرض إشعار فوري
  static Future<void> _showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    await _localNotifications!.show(
      id,
      title,
      body,
      _getNotificationDetails(priority),
      payload: payload,
    );
  }

  /// الحصول على تفاصيل الإشعار حسب الأولوية
  static NotificationDetails _getNotificationDetails(
      NotificationPriority priority) {
    late Importance importance;
    late Priority androidPriority;
    late String channelId;
    late String channelName;
    late Color? ledColor;
    late bool enableVibration;

    switch (priority) {
      case NotificationPriority.low:
        importance = Importance.low;
        androidPriority = Priority.low;
        channelId = 'low_priority_channel';
        channelName = 'إشعارات منخفضة الأولوية';
        ledColor = null;
        enableVibration = false;
        break;
      case NotificationPriority.normal:
        importance = Importance.defaultImportance;
        androidPriority = Priority.defaultPriority;
        channelId = 'normal_priority_channel';
        channelName = 'إشعارات عادية';
        ledColor = null;
        enableVibration = true;
        break;
      case NotificationPriority.high:
        importance = Importance.high;
        androidPriority = Priority.high;
        channelId = 'high_priority_channel';
        channelName = 'إشعارات عالية الأولوية';
        ledColor = const Color(0xFFFF5722);
        enableVibration = true;
        break;
      case NotificationPriority.urgent:
        importance = Importance.max;
        androidPriority = Priority.max;
        channelId = 'urgent_priority_channel';
        channelName = 'إشعارات عاجلة';
        ledColor = const Color(0xFFFF0000);
        enableVibration = true;
        break;
    }

    return NotificationDetails(
      android: AndroidNotificationDetails(
        channelId,
        channelName,
        channelDescription: 'قناة $channelName للتطبيق',
        importance: importance,
        priority: androidPriority,
        showWhen: true,
        enableVibration: enableVibration,
        playSound: true,
        ledColor: ledColor,
        ledOnMs: 1000,
        ledOffMs: 500,
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );
  }

  /// تحديد مستوى إلحاح المخزون
  static NotificationPriority _getStockUrgencyLevel(Product product) {
    if (product.quantity <= 0) {
      return NotificationPriority.urgent;
    }
    if (product.quantity <= product.minQuantity * 0.3) {
      return NotificationPriority.high;
    }
    if (product.quantity <= product.minQuantity * 0.7) {
      return NotificationPriority.normal;
    }
    return NotificationPriority.low;
  }

  /// الحصول على رسالة المخزون
  static String _getStockMessage(Product product) {
    if (product.quantity <= 0) {
      return 'نفد من المخزون! 🚨';
    } else if (product.quantity <= product.minQuantity * 0.3) {
      return 'أوشك على النفاد (${product.quantity} متبقي) ⚠️';
    } else {
      return 'مخزون منخفض (${product.quantity} متبقي)';
    }
  }

  /// إلغاء الإشعارات حسب النوع
  static Future<void> _cancelNotificationsByType(String type) async {
    // هذه دالة مساعدة لإلغاء إشعارات معينة
    // يمكن تحسينها لاحقاً لتتبع الإشعارات حسب النوع
    switch (type) {
      case 'low_stock':
        for (int i = 1000; i < 1010; i++) {
          await _localNotifications!.cancel(i);
        }
        break;
      case 'debt_reminder':
        for (int i = 2000; i < 2010; i++) {
          await _localNotifications!.cancel(i);
        }
        break;
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');

    // هنا يمكن إضافة منطق التنقل حسب نوع الإشعار
    if (response.payload != null) {
      final payload = response.payload!;

      if (payload.startsWith('low_stock_')) {
        // التنقل لصفحة المنتجات
        debugPrint('التنقل لصفحة المنتجات');
      } else if (payload.startsWith('debt_reminder_')) {
        // التنقل لصفحة العملاء
        debugPrint('التنقل لصفحة العملاء');
      } else if (payload == 'dollar_rate_change') {
        // التنقل لصفحة تاريخ الدولار
        debugPrint('التنقل لصفحة تاريخ الدولار');
      }
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    if (!_isInitialized) return;
    await _localNotifications!.cancelAll();
    debugPrint('🗑️ تم إلغاء جميع الإشعارات');
  }
}

/// أولويات الإشعارات
enum NotificationPriority { low, normal, high, urgent }
