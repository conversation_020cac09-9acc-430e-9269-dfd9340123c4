import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/invoice.dart';
import '../models/invoice_item.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../providers/invoice_provider.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';

class CreateInvoiceScreen extends StatefulWidget {
  final Invoice? invoice;

  const CreateInvoiceScreen({super.key, this.invoice});

  @override
  State<CreateInvoiceScreen> createState() => _CreateInvoiceScreenState();
}

class _CreateInvoiceScreenState extends State<CreateInvoiceScreen> {
  Customer? _selectedCustomer;
  PaymentType _paymentType = PaymentType.cash;
  final List<InvoiceItem> _items = [];
  double _paidAmountSyp = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<ProductProvider>().fetchProducts();
        context.read<CustomerProvider>().fetchCustomers();
        if (widget.invoice != null) {
          setState(() {
            final customers = context.read<CustomerProvider>().customers;
            _selectedCustomer = customers.isNotEmpty
                ? customers.firstWhere(
                    (c) => c.id == widget.invoice!.customerId,
                    orElse: () => customers.first)
                : null;
            _paymentType = widget.invoice!.paymentType;
            _items.addAll(widget.invoice!.items);
            _paidAmountSyp = widget.invoice!.paidAmountSyp;
          });
        }
      }
    });
  }

  double get _totalAmountSyp =>
      _items.fold(0, (sum, item) => sum + item.totalSyp);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.invoice == null ? 'فاتورة جديدة' : 'تعديل الفاتورة'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCustomerSection(),
            const SizedBox(height: 16),
            _buildPaymentTypeSection(),
            const SizedBox(height: 16),
            _buildItemsSection(),
            const SizedBox(height: 16),
            _buildTotalsSection(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _saveInvoice,
        child: const Icon(Icons.save),
      ),
    );
  }

  Widget _buildCustomerSection() {
    final customers = context.watch<CustomerProvider>().customers;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العميل', style: Theme.of(context).textTheme.titleMedium),
            DropdownButton<Customer>(
              isExpanded: true,
              value: _selectedCustomer,
              hint: const Text('اختر العميل'),
              items: customers.map((customer) {
                return DropdownMenuItem(
                  value: customer,
                  child: Text(customer.name),
                );
              }).toList(),
              onChanged: (Customer? customer) {
                setState(() => _selectedCustomer = customer);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTypeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('طريقة الدفع', style: Theme.of(context).textTheme.titleMedium),
            DropdownButton<PaymentType>(
              isExpanded: true,
              value: _paymentType,
              items: const [
                DropdownMenuItem(value: PaymentType.cash, child: Text('نقدي')),
                DropdownMenuItem(value: PaymentType.debt, child: Text('دين')),
                DropdownMenuItem(
                    value: PaymentType.partial, child: Text('دفع جزئي')),
              ],
              onChanged: (PaymentType? value) {
                if (value != null) {
                  setState(() {
                    _paymentType = value;
                    if (value == PaymentType.cash) {
                      _paidAmountSyp = _totalAmountSyp;
                    } else if (value == PaymentType.debt) {
                      _paidAmountSyp = 0;
                    }
                  });
                }
              },
            ),
            if (_paymentType == PaymentType.partial) ...[
              const SizedBox(height: 8),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'المبلغ المدفوع (ل.س)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _paidAmountSyp = double.tryParse(value) ?? 0;
                  });
                },
                controller: TextEditingController(
                  text: _paidAmountSyp.toString(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('المنتجات',
                    style: Theme.of(context).textTheme.titleMedium),
                TextButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                  onPressed: _showAddItemDialog,
                ),
              ],
            ),
            if (_items.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Center(child: Text('لا توجد منتجات')),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return ListTile(
                    title: Text(item.productName),
                    subtitle: Text(
                      '${item.quantity} × ${item.unitPriceSyp.toStringAsFixed(2)} = ${item.totalSyp.toStringAsFixed(2)} ل.س',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      color: Colors.red,
                      onPressed: () {
                        setState(() => _items.removeAt(index));
                      },
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المجموع: ${_totalAmountSyp.toStringAsFixed(2)} ل.س',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              'المبلغ المدفوع: ${_paidAmountSyp.toStringAsFixed(2)} ل.س',
            ),
            Text(
              'المبلغ المتبقي: ${(_totalAmountSyp - _paidAmountSyp).toStringAsFixed(2)} ل.س',
              style: TextStyle(
                color: _totalAmountSyp - _paidAmountSyp > 0
                    ? Colors.red
                    : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAddItemDialog() async {
    final products = context.read<ProductProvider>().products;
    Product? selectedProduct;
    int quantity = 1;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إضافة منتج'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<Product>(
                isExpanded: true,
                value: selectedProduct,
                hint: const Text('اختر المنتج'),
                items: products.map((product) {
                  return DropdownMenuItem(
                    value: product,
                    child: Text(product.name),
                  );
                }).toList(),
                onChanged: (Product? product) {
                  setState(() => selectedProduct = product);
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('الكمية: '),
                  IconButton(
                    icon: const Icon(Icons.remove),
                    onPressed:
                        quantity > 1 ? () => setState(() => quantity--) : null,
                  ),
                  Text(quantity.toString()),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: selectedProduct != null &&
                            quantity < selectedProduct!.quantity
                        ? () => setState(() => quantity++)
                        : null,
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: selectedProduct == null
                  ? null
                  : () {
                      Navigator.of(context).pop({
                        'product': selectedProduct,
                        'quantity': quantity,
                      });
                    },
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      final product = result['product'] as Product;
      final quantity = result['quantity'] as int;
      setState(() {
        _items.add(InvoiceItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          productId: product.id.toString(),
          productName: product.name,
          productBarcode: product.barcode ?? '',
          unitPriceSyp: product.sellingPriceSyp,
          unitPriceUsd: product.sellingPriceUsd,
          quantity: quantity,
          totalSyp: product.sellingPriceSyp * quantity,
          totalUsd: product.sellingPriceUsd * quantity,
        ));
      });
    }
  }

  Future<void> _saveInvoice() async {
    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة منتج واحد على الأقل')),
      );
      return;
    }

    if (_paymentType == PaymentType.partial &&
        _paidAmountSyp > _totalAmountSyp) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('المبلغ المدفوع لا يمكن أن يتجاوز المبلغ الكلي')),
      );
      return;
    }

    try {
      final invoice = Invoice(
        id: widget.invoice?.id ?? DateTime.now().millisecondsSinceEpoch,
        customerId: _selectedCustomer?.id ?? 0,
        customerName: _selectedCustomer?.name ?? 'عميل نقدي',
        paymentType: _paymentType,
        totalAmountSyp: _totalAmountSyp,
        totalAmountUsd: 0, // سيتم حسابه في الخادم
        paidAmountSyp: _paidAmountSyp,
        remainingAmountSyp: _totalAmountSyp - _paidAmountSyp,
        status: _paymentType == PaymentType.cash
            ? InvoiceStatus.paid
            : _paymentType == PaymentType.debt
                ? InvoiceStatus.pending
                : InvoiceStatus.partial,
        date: DateTime.now(),
        items: _items,
      );

      final provider = context.read<InvoiceProvider>();
      if (widget.invoice == null) {
        await provider.createInvoice(invoice);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء الفاتورة بنجاح')),
          );
        }
      } else {
        await provider.updateInvoice(invoice);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث الفاتورة بنجاح')),
          );
        }
      }
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء حفظ الفاتورة')),
        );
      }
    }
  }
}
