@echo off
echo ========================================
echo   إصلاح مشكلة الأرقام العربية في Gradle
echo ========================================

echo.
echo 1. تعيين متغيرات البيئة...
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Duser.language=en -Duser.country=US -Duser.variant=
set GRADLE_OPTS=-Dfile.encoding=UTF-8 -Duser.language=en -Duser.country=US -Duser.variant=
set LC_ALL=en_US.UTF-8
set LANG=en_US.UTF-8

echo.
echo 2. تنظيف مجلدات Gradle...
if exist "%USERPROFILE%\.gradle" (
    echo تنظيف مجلد Gradle cache...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    rmdir /s /q "%USERPROFILE%\.gradle\wrapper"
)

echo.
echo 3. تنظيف مشروع Flutter...
call flutter clean

echo.
echo 4. إعادة تحميل التبعيات...
call flutter pub get

echo.
echo 5. محاولة بناء المشروع...
call flutter build apk --debug --verbose

echo.
echo ========================================
echo   انتهى الإصلاح
echo ========================================
pause
