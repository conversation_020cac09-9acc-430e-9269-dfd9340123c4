/// نموذج الشركة
class Company {
  final int id;
  final String name; // اسم الشركة
  final String nameEn; // الاسم بالإنجليزية
  final String taxId; // الرقم الضريبي
  final String commercialRegister; // السجل التجاري
  final String address; // العنوان
  final String city; // المدينة
  final String country; // البلد
  final String phone; // الهاتف
  final String email; // البريد الإلكتروني
  final String website; // الموقع الإلكتروني
  final String baseCurrency; // العملة الأساسية
  final String logo; // شعار الشركة
  final CompanyType type; // نوع الشركة
  final CompanyStatus status; // حالة الشركة
  final DateTime fiscalYearStart; // بداية السنة المالية
  final DateTime fiscalYearEnd; // نهاية السنة المالية
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime? updatedAt; // تاريخ آخر تحديث
  final CompanySettings settings; // إعدادات الشركة
  final List<BankAccount> bankAccounts; // الحسابات البنكية

  const Company({
    required this.id,
    required this.name,
    required this.nameEn,
    this.taxId = '',
    this.commercialRegister = '',
    required this.address,
    required this.city,
    required this.country,
    this.phone = '',
    this.email = '',
    this.website = '',
    this.baseCurrency = 'SYP',
    this.logo = '',
    this.type = CompanyType.limited,
    this.status = CompanyStatus.active,
    required this.fiscalYearStart,
    required this.fiscalYearEnd,
    required this.createdAt,
    this.updatedAt,
    required this.settings,
    this.bankAccounts = const [],
  });

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  Company copyWith({
    int? id,
    String? name,
    String? nameEn,
    String? taxId,
    String? commercialRegister,
    String? address,
    String? city,
    String? country,
    String? phone,
    String? email,
    String? website,
    String? baseCurrency,
    String? logo,
    CompanyType? type,
    CompanyStatus? status,
    DateTime? fiscalYearStart,
    DateTime? fiscalYearEnd,
    DateTime? createdAt,
    DateTime? updatedAt,
    CompanySettings? settings,
    List<BankAccount>? bankAccounts,
  }) {
    return Company(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      taxId: taxId ?? this.taxId,
      commercialRegister: commercialRegister ?? this.commercialRegister,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      baseCurrency: baseCurrency ?? this.baseCurrency,
      logo: logo ?? this.logo,
      type: type ?? this.type,
      status: status ?? this.status,
      fiscalYearStart: fiscalYearStart ?? this.fiscalYearStart,
      fiscalYearEnd: fiscalYearEnd ?? this.fiscalYearEnd,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
      bankAccounts: bankAccounts ?? this.bankAccounts,
    );
  }

  /// تحويل من JSON
  factory Company.fromJson(Map<String, dynamic> json) {
    return Company(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameEn: json['name_en'] ?? '',
      taxId: json['tax_id'] ?? '',
      commercialRegister: json['commercial_register'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      website: json['website'] ?? '',
      baseCurrency: json['base_currency'] ?? 'SYP',
      logo: json['logo'] ?? '',
      type: CompanyType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => CompanyType.limited,
      ),
      status: CompanyStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => CompanyStatus.active,
      ),
      fiscalYearStart: DateTime.parse(
          json['fiscal_year_start'] ?? DateTime.now().toIso8601String()),
      fiscalYearEnd: DateTime.parse(
          json['fiscal_year_end'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      settings: CompanySettings.fromJson(json['settings'] ?? {}),
      bankAccounts: (json['bank_accounts'] as List<dynamic>?)
              ?.map((account) => BankAccount.fromJson(account))
              .toList() ??
          [],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_en': nameEn,
      'tax_id': taxId,
      'commercial_register': commercialRegister,
      'address': address,
      'city': city,
      'country': country,
      'phone': phone,
      'email': email,
      'website': website,
      'base_currency': baseCurrency,
      'logo': logo,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'fiscal_year_start': fiscalYearStart.toIso8601String(),
      'fiscal_year_end': fiscalYearEnd.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'settings': settings.toJson(),
      'bank_accounts': bankAccounts.map((account) => account.toJson()).toList(),
    };
  }

  /// الحصول على اسم نوع الشركة
  String get typeDisplayName {
    switch (type) {
      case CompanyType.individual:
        return 'فردية';
      case CompanyType.partnership:
        return 'شراكة';
      case CompanyType.limited:
        return 'محدودة المسؤولية';
      case CompanyType.corporation:
        return 'مساهمة';
      case CompanyType.nonprofit:
        return 'غير ربحية';
    }
  }

  /// الحصول على اسم حالة الشركة
  String get statusDisplayName {
    switch (status) {
      case CompanyStatus.active:
        return 'نشطة';
      case CompanyStatus.inactive:
        return 'غير نشطة';
      case CompanyStatus.suspended:
        return 'معلقة';
      case CompanyStatus.closed:
        return 'مغلقة';
    }
  }

  /// فحص إذا كانت الشركة نشطة
  bool get isActive => status == CompanyStatus.active;

  /// الحصول على العنوان الكامل
  String get fullAddress => '$address, $city, $country';

  @override
  String toString() {
    return 'Company(id: $id, name: $name, type: $type, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Company && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أنواع الشركات
enum CompanyType {
  individual, // فردية
  partnership, // شراكة
  limited, // محدودة المسؤولية
  corporation, // مساهمة
  nonprofit, // غير ربحية
}

/// حالات الشركة
enum CompanyStatus {
  active, // نشطة
  inactive, // غير نشطة
  suspended, // معلقة
  closed, // مغلقة
}

/// إعدادات الشركة
class CompanySettings {
  final bool enableMultiCurrency; // تفعيل العملات المتعددة
  final bool enableTax; // تفعيل الضرائب
  final double taxRate; // معدل الضريبة
  final bool enableDiscount; // تفعيل الخصومات
  final bool enableInventoryTracking; // تفعيل تتبع المخزون
  final bool enableCustomerCredit; // تفعيل ائتمان العملاء
  final int invoiceNumberFormat; // تنسيق رقم الفاتورة
  final String invoicePrefix; // بادئة الفاتورة
  final bool autoBackup; // النسخ الاحتياطي التلقائي
  final int backupFrequency; // تكرار النسخ الاحتياطي (بالأيام)
  final Map<String, dynamic> customSettings; // إعدادات مخصصة

  const CompanySettings({
    this.enableMultiCurrency = true,
    this.enableTax = false,
    this.taxRate = 0.0,
    this.enableDiscount = true,
    this.enableInventoryTracking = true,
    this.enableCustomerCredit = true,
    this.invoiceNumberFormat = 1,
    this.invoicePrefix = 'INV',
    this.autoBackup = true,
    this.backupFrequency = 7,
    this.customSettings = const {},
  });

  factory CompanySettings.fromJson(Map<String, dynamic> json) {
    return CompanySettings(
      enableMultiCurrency: json['enable_multi_currency'] ?? true,
      enableTax: json['enable_tax'] ?? false,
      taxRate: (json['tax_rate'] ?? 0.0).toDouble(),
      enableDiscount: json['enable_discount'] ?? true,
      enableInventoryTracking: json['enable_inventory_tracking'] ?? true,
      enableCustomerCredit: json['enable_customer_credit'] ?? true,
      invoiceNumberFormat: json['invoice_number_format'] ?? 1,
      invoicePrefix: json['invoice_prefix'] ?? 'INV',
      autoBackup: json['auto_backup'] ?? true,
      backupFrequency: json['backup_frequency'] ?? 7,
      customSettings: Map<String, dynamic>.from(json['custom_settings'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enable_multi_currency': enableMultiCurrency,
      'enable_tax': enableTax,
      'tax_rate': taxRate,
      'enable_discount': enableDiscount,
      'enable_inventory_tracking': enableInventoryTracking,
      'enable_customer_credit': enableCustomerCredit,
      'invoice_number_format': invoiceNumberFormat,
      'invoice_prefix': invoicePrefix,
      'auto_backup': autoBackup,
      'backup_frequency': backupFrequency,
      'custom_settings': customSettings,
    };
  }
}

/// الحساب البنكي
class BankAccount {
  final int id;
  final String bankName; // اسم البنك
  final String accountNumber; // رقم الحساب
  final String accountName; // اسم الحساب
  final String iban; // رقم الآيبان
  final String swiftCode; // رمز السويفت
  final String currency; // العملة
  final double balance; // الرصيد
  final bool isActive; // نشط أم لا
  final bool isDefault; // افتراضي أم لا
  final DateTime createdAt; // تاريخ الإنشاء

  const BankAccount({
    required this.id,
    required this.bankName,
    required this.accountNumber,
    required this.accountName,
    this.iban = '',
    this.swiftCode = '',
    this.currency = 'SYP',
    this.balance = 0.0,
    this.isActive = true,
    this.isDefault = false,
    required this.createdAt,
  });

  factory BankAccount.fromJson(Map<String, dynamic> json) {
    return BankAccount(
      id: json['id'] ?? 0,
      bankName: json['bank_name'] ?? '',
      accountNumber: json['account_number'] ?? '',
      accountName: json['account_name'] ?? '',
      iban: json['iban'] ?? '',
      swiftCode: json['swift_code'] ?? '',
      currency: json['currency'] ?? 'SYP',
      balance: (json['balance'] ?? 0.0).toDouble(),
      isActive: json['is_active'] ?? true,
      isDefault: json['is_default'] ?? false,
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bank_name': bankName,
      'account_number': accountNumber,
      'account_name': accountName,
      'iban': iban,
      'swift_code': swiftCode,
      'currency': currency,
      'balance': balance,
      'is_active': isActive,
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// الحصول على الرصيد المنسق
  String get formattedBalance => '${balance.toStringAsFixed(2)} $currency';

  @override
  String toString() {
    return 'BankAccount(id: $id, bankName: $bankName, accountNumber: $accountNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BankAccount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
