import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/product_provider.dart';
import '../providers/invoice_provider.dart';
import '../providers/currency_provider.dart';

/// لوحة تحكم مبسطة للشاشة الرئيسية
class SimplifiedDashboard extends StatelessWidget {
  const SimplifiedDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // الإحصائيات المبسطة
        _buildSimpleStats(context),
        const SizedBox(height: 20),

        // الإجراءات السريعة المبسطة
        _buildQuickActions(context),
        const SizedBox(height: 20),

        // التنبيهات المهمة فقط
        _buildImportantAlerts(context),
      ],
    );
  }

  /// إحصائيات مبسطة - 4 بطاقات فقط
  Widget _buildSimpleStats(BuildContext context) {
    return Consumer3<ProductProvider, InvoiceProvider, CurrencyProvider>(
      builder:
          (context, productProvider, invoiceProvider, currencyProvider, child) {
        return Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نظرة سريعة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 16),

                // صف واحد مع 4 إحصائيات
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'المنتجات',
                        '${productProvider.products.length}',
                        Icons.inventory_2,
                        Colors.blue,
                        context,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'الفواتير',
                        '${invoiceProvider.invoices.length}',
                        Icons.receipt_long,
                        Colors.green,
                        context,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'المبيعات',
                        '${(invoiceProvider.invoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp) / 1000).toStringAsFixed(0)}K',
                        Icons.trending_up,
                        Colors.orange,
                        context,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'الدولار',
                        '${(currencyProvider.dollarRate / 1000).toStringAsFixed(0)}K',
                        Icons.attach_money,
                        Colors.purple,
                        context,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بطاقة إحصائية مبسطة
  Widget _buildSimpleStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    BuildContext context,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// إجراءات سريعة مبسطة - 4 أزرار كبيرة
  Widget _buildQuickActions(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // صفين من الأزرار
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    'فاتورة جديدة',
                    Icons.add_shopping_cart,
                    Colors.green,
                    '/create-invoice',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    'إضافة منتج',
                    Icons.add_box,
                    Colors.blue,
                    '/products',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    'التقارير',
                    Icons.analytics,
                    Colors.purple,
                    '/financial-reports',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    context,
                    'تحديث الدولار',
                    Icons.currency_exchange,
                    Colors.orange,
                    null,
                    onTap: () => _showDollarUpdateDialog(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// زر إجراء مبسط
  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String? route, {
    VoidCallback? onTap,
  }) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap ??
            () {
              if (route != null) {
                Navigator.pushNamed(context, route);
              }
            },
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنبيهات المهمة فقط
  Widget _buildImportantAlerts(BuildContext context) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final lowStockProducts = productProvider.products
            .where((p) => p.quantity <= p.minQuantity)
            .toList();

        if (lowStockProducts.isEmpty) {
          return const SizedBox(); // لا نعرض شيء إذا لم تكن هناك تنبيهات
        }

        return Card(
          elevation: 2,
          color: Colors.orange.withValues(alpha: 0.1),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.warning_amber, color: Colors.orange[700], size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تنبيه مخزون منخفض',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[700],
                        ),
                      ),
                      Text(
                        '${lowStockProducts.length} منتج يحتاج إعادة تموين',
                        style: TextStyle(color: Colors.orange[600]),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.pushNamed(context, '/products'),
                  child: const Text('عرض'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// نافذة تحديث الدولار السريعة
  void _showDollarUpdateDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث سعر الدولار'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Consumer<CurrencyProvider>(
              builder: (context, provider, child) => Text(
                'السعر الحالي: ${provider.formatSyp(provider.dollarRate)}',
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'السعر الجديد',
                hintText: '15000',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newRate = double.tryParse(controller.text);
              if (newRate != null && newRate > 0) {
                final provider = context.read<CurrencyProvider>();
                await provider.updateDollarRate(newRate);
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم تحديث سعر الدولار بنجاح')),
                  );
                }
              }
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }
}
