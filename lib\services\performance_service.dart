import 'package:flutter/material.dart';

/// خدمة تحسين الأداء
class PerformanceService {
  static final Map<String, dynamic> _cache = {};
  static const int _maxCacheSize = 100;
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// تخزين مؤقت ذكي مع انتهاء صلاحية
  static Future<T> getCachedData<T>({
    required String key,
    required Future<T> Function() fetcher,
    Duration? customExpiry,
  }) async {
    final cacheEntry = _cache[key];
    final expiry = customExpiry ?? _cacheExpiry;

    if (cacheEntry != null) {
      final cachedTime = cacheEntry['timestamp'] as DateTime;
      final data = cacheEntry['data'] as T;

      if (DateTime.now().difference(cachedTime) < expiry) {
        debugPrint('📦 Cache hit for: $key');
        return data;
      }
    }

    debugPrint('🔄 Cache miss for: $key - fetching data');
    final data = await fetcher();

    _cache[key] = {
      'data': data,
      'timestamp': DateTime.now(),
    };

    // تنظيف الكاش إذا تجاوز الحد الأقصى
    if (_cache.length > _maxCacheSize) {
      _cleanupCache();
    }

    return data;
  }

  /// تنظيف الكاش القديم
  static void _cleanupCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    _cache.forEach((key, value) {
      final timestamp = value['timestamp'] as DateTime;
      if (now.difference(timestamp) > _cacheExpiry) {
        keysToRemove.add(key);
      }
    });

    for (var key in keysToRemove) {
      _cache.remove(key);
    }

    debugPrint('🧹 Cleaned ${keysToRemove.length} expired cache entries');
  }

  /// مسح الكاش
  static void clearCache([String? key]) {
    if (key != null) {
      _cache.remove(key);
      debugPrint('🗑️ Cleared cache for: $key');
    } else {
      _cache.clear();
      debugPrint('🗑️ Cleared all cache');
    }
  }

  /// تحميل البيانات مسبقاً
  static Future<void> preloadCriticalData() async {
    debugPrint('🚀 Preloading critical data...');

    try {
      await Future.wait([
        _preloadProducts(),
        _preloadRecentInvoices(),
        _preloadCustomers(),
      ]);

      debugPrint('✅ Critical data preloaded successfully');
    } catch (e) {
      debugPrint('❌ Failed to preload data: $e');
    }
  }

  static Future<void> _preloadProducts() async {
    // تحميل المنتجات الأكثر استخداماً
    await getCachedData(
      key: 'popular_products',
      fetcher: () async {
        // محاكاة تحميل البيانات
        await Future.delayed(const Duration(milliseconds: 100));
        return <String>['product1', 'product2', 'product3'];
      },
    );
  }

  static Future<void> _preloadRecentInvoices() async {
    // تحميل الفواتير الحديثة
    await getCachedData(
      key: 'recent_invoices',
      fetcher: () async {
        await Future.delayed(const Duration(milliseconds: 100));
        return <String>['invoice1', 'invoice2', 'invoice3'];
      },
    );
  }

  static Future<void> _preloadCustomers() async {
    // تحميل العملاء النشطين
    await getCachedData(
      key: 'active_customers',
      fetcher: () async {
        await Future.delayed(const Duration(milliseconds: 100));
        return <String>['customer1', 'customer2', 'customer3'];
      },
    );
  }
}

/// مكون قائمة محسن مع Lazy Loading
class OptimizedListView<T> extends StatefulWidget {
  final Future<List<T>> Function(int page, int pageSize) dataFetcher;
  final Widget Function(BuildContext context, T item) itemBuilder;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final int pageSize;
  final String? cacheKey;

  const OptimizedListView({
    super.key,
    required this.dataFetcher,
    required this.itemBuilder,
    this.loadingWidget,
    this.emptyWidget,
    this.pageSize = 20,
    this.cacheKey,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  final List<T> _items = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final cacheKey =
          widget.cacheKey != null ? '${widget.cacheKey}_page_0' : null;

      List<T> newItems;

      if (cacheKey != null) {
        newItems = await PerformanceService.getCachedData(
          key: cacheKey,
          fetcher: () => widget.dataFetcher(0, widget.pageSize),
        );
      } else {
        newItems = await widget.dataFetcher(0, widget.pageSize);
      }

      setState(() {
        _items.clear();
        _items.addAll(newItems);
        _hasMore = newItems.length == widget.pageSize;
        _currentPage = 0;
      });
    } catch (e) {
      debugPrint('Error loading initial data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final newItems = await widget.dataFetcher(nextPage, widget.pageSize);

      setState(() {
        _items.addAll(newItems);
        _hasMore = newItems.length == widget.pageSize;
        _currentPage = nextPage;
      });
    } catch (e) {
      debugPrint('Error loading more data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_items.isEmpty && _isLoading) {
      return widget.loadingWidget ??
          const Center(child: CircularProgressIndicator());
    }

    if (_items.isEmpty && !_isLoading) {
      return widget.emptyWidget ?? const Center(child: Text('لا توجد بيانات'));
    }

    return RefreshIndicator(
      onRefresh: _loadInitialData,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _items.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _items.length) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return widget.itemBuilder(context, _items[index]);
        },
      ),
    );
  }
}

/// مكون تحميل محسن للصور
class OptimizedImageLoader extends StatelessWidget {
  final String? imageUrl;
  final String? assetPath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImageLoader({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl != null) {
      return Image.network(
        imageUrl!,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;

          return placeholder ??
              SizedBox(
                width: width,
                height: height,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
        },
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ??
              Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const Icon(Icons.error),
              );
        },
      );
    }

    if (assetPath != null) {
      return Image.asset(
        assetPath!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ??
              Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const Icon(Icons.error),
              );
        },
      );
    }

    return errorWidget ??
        Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: const Icon(Icons.image),
        );
  }
}
