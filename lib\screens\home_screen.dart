import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/product_provider.dart';
import '../providers/invoice_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/currency_provider.dart';
import '../widgets/brand_footer.dart';
import '../widgets/simplified_dashboard.dart';
import '../widgets/quick_actions_widget.dart';
import '../services/keyboard_shortcuts_service.dart';
import '../widgets/update_dollar_rate_dialog.dart';
import '../services/offline_notification_service.dart';
import '../models/invoice.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _loadDataSafely();
    // تهيئة اختصارات لوحة المفاتيح
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        KeyboardShortcutsService.initialize(context);
      } catch (e) {
        debugPrint('خطأ في تهيئة اختصارات لوحة المفاتيح: $e');
      }
    });
  }

  void _loadDataSafely() async {
    try {
      if (!mounted) return;

      debugPrint('بدء تحميل البيانات...');

      // تحميل البيانات واحدة تلو الأخرى مع معالجة الأخطاء
      await _loadProviderDataSafely('ProductProvider', () async {
        final productProvider = context.read<ProductProvider>();
        await productProvider.fetchProducts();
      });

      await _loadProviderDataSafely('InvoiceProvider', () async {
        final invoiceProvider = context.read<InvoiceProvider>();
        await invoiceProvider.fetchInvoices();
      });

      await _loadProviderDataSafely('CurrencyProvider', () async {
        final currencyProvider = context.read<CurrencyProvider>();
        await currencyProvider.loadDollarRate();
      });

      if (!mounted) return;

      // فحص المخزون المنخفض بعد تحميل البيانات
      _checkLowStockSafely();

      // تحديث الإحصائيات
      _updateStatistics();

      debugPrint('تم الانتهاء من تحميل البيانات');
    } catch (e) {
      debugPrint('خطأ عام في تحميل البيانات: $e');
      // في حالة الخطأ، لا نعرض رسالة للمستخدم لتجنب الإزعاج
    }
  }

  Future<void> _loadProviderDataSafely(
      String providerName, Future<void> Function() loadFunction) async {
    try {
      debugPrint('تحميل $providerName...');
      await loadFunction();
      debugPrint('تم تحميل $providerName بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحميل $providerName: $e');
      // نتجاهل الخطأ ونكمل
    }
  }

  void _checkLowStockSafely() async {
    try {
      final products = context.read<ProductProvider>().products;
      for (var product in products) {
        if (product.quantity <= product.minQuantity) {
          await OfflineNotificationService.showLowStockNotification(
            product.name,
            product.quantity,
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في فحص المخزون المنخفض: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام إدارة الفواتير'),
        actions: [
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return IconButton(
                icon: Icon(
                  themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                ),
                onPressed: () => themeProvider.toggleTheme(),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.currency_exchange),
            tooltip: 'تحديث سعر الصرف',
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => const UpdateDollarRateDialog(),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.offline_bolt),
            tooltip: 'وضع offline',
            onPressed: () async {
              if (mounted) {
                final messenger = ScaffoldMessenger.of(context);
                messenger.showSnackBar(
                  const SnackBar(
                      content: Text('التطبيق يعمل بوضع offline كامل')),
                );
              }
            },
          ),
        ],
      ),
      drawer: _buildDrawer(context),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // الإحصائيات المبسطة
                  const SimplifiedDashboard(),
                  const SizedBox(height: 20),

                  // الإجراءات السريعة
                  const QuickActionsWidget(),
                  const SizedBox(height: 20),

                  // الإحصائيات التفصيلية
                  _buildQuickStats(),
                  const SizedBox(height: 20),
                  _buildSalesTrend(),
                  const SizedBox(height: 20),
                  _buildTopProducts(),
                  const SizedBox(height: 20),
                  _buildLowStockAlert(),
                ],
              ),
            ),
          ),
          const BottomBrandFooter(),
        ],
      ),
    );
  }

  /// بناء الدرج الجانبي
  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, size: 48, color: Colors.white),
                SizedBox(height: 8),
                Text(
                  'نظام إدارة الفواتير',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              children: [
                _buildDrawerItem(Icons.home, 'الرئيسية', '/home'),
                _buildDrawerItem(
                    Icons.point_of_sale, 'فاتورة جديدة', '/create-invoice'),
                _buildDrawerItem(Icons.inventory, 'المخزون', '/products'),
                _buildDrawerItem(Icons.receipt_long, 'الفواتير', '/invoices'),
                _buildDrawerItem(Icons.people, 'العملاء', '/customers'),
                _buildDrawerItem(Icons.analytics, 'التقارير', '/reports'),
                _buildDrawerItem(
                    Icons.account_balance, 'دليل الحسابات', '/accounts'),
                _buildDrawerItem(
                    Icons.assessment, 'التقارير المالية', '/financial-reports'),
                _buildDrawerItem(Icons.business, 'إدارة الشركات', '/companies'),
                _buildDrawerItem(
                    Icons.psychology, 'الرؤى الذكية', '/ai-insights'),
                _buildDrawerItem(Icons.currency_exchange, 'أرشيف الدولار',
                    '/dollar-history'),
                _buildDrawerItem(Icons.settings, 'الإعدادات', '/settings'),
              ],
            ),
          ),
          const DrawerBrandFooter(),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, String route) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: () {
        Navigator.pop(context);
        Navigator.pushNamed(context, route);
      },
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Consumer3<ProductProvider, InvoiceProvider, CurrencyProvider>(
      builder:
          (context, productProvider, invoiceProvider, currencyProvider, child) {
        final totalProducts = productProvider.products.length;
        final lowStockProducts = productProvider.products
            .where((p) => p.quantity <= p.minQuantity)
            .length;
        final totalInvoices = invoiceProvider.invoices.length;
        final totalSales = invoiceProvider.invoices
            .fold(0.0, (sum, invoice) => sum + invoice.totalAmountSyp);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائيات سريعة',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي المنتجات',
                        totalProducts.toString(),
                        Icons.inventory,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'مخزون منخفض',
                        lowStockProducts.toString(),
                        Icons.warning,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي الفواتير',
                        totalInvoices.toString(),
                        Icons.receipt,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي المبيعات',
                        '${totalSales.toStringAsFixed(0)} ل.س',
                        Icons.monetization_on,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'سعر الدولار',
                        currencyProvider.formatSyp(currencyProvider.dollarRate),
                        Icons.currency_exchange,
                        Colors.teal,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'آخر تحديث',
                        'اليوم',
                        Icons.update,
                        Colors.indigo,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء اتجاهات المبيعات
  Widget _buildSalesTrend() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اتجاهات المبيعات (آخر 7 أيام)',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Consumer<InvoiceProvider>(
                builder: (context, invoiceProvider, child) {
                  final salesData = _getSalesData(invoiceProvider.invoices);
                  return LineChart(
                    LineChartData(
                      gridData: const FlGridData(show: true),
                      titlesData: const FlTitlesData(show: true),
                      borderData: FlBorderData(show: true),
                      lineBarsData: [
                        LineChartBarData(
                          spots: salesData,
                          isCurved: true,
                          color: Theme.of(context).primaryColor,
                          barWidth: 3,
                          dotData: const FlDotData(show: true),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<FlSpot> _getSalesData(List<Invoice> invoices) {
    final now = DateTime.now();
    final salesByDay = <int, double>{};

    for (int i = 6; i >= 0; i--) {
      final day = now.subtract(Duration(days: i));
      final dayKey = day.day;
      salesByDay[dayKey] = 0.0;
    }

    for (var invoice in invoices) {
      final daysDiff = now.difference(invoice.date).inDays;
      if (daysDiff <= 6) {
        final dayKey = invoice.date.day;
        salesByDay[dayKey] = (salesByDay[dayKey] ?? 0) + invoice.totalAmountSyp;
      }
    }

    return salesByDay.entries
        .map((entry) => FlSpot(entry.key.toDouble(), entry.value))
        .toList();
  }

  /// بناء أفضل المنتجات مبيعاً
  Widget _buildTopProducts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أفضل المنتجات مبيعاً',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Consumer<InvoiceProvider>(
              builder: (context, invoiceProvider, child) {
                final topProducts = _getTopProducts(invoiceProvider.invoices);
                return Column(
                  children: topProducts.take(5).map((product) {
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor,
                        child: Text(
                          '${product['quantity']}',
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                      ),
                      title: Text(product['name']),
                      subtitle: Text('تم بيع ${product['quantity']} قطعة'),
                      trailing: Text(
                        '${product['total'].toStringAsFixed(0)} ل.س',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getTopProducts(List<Invoice> invoices) {
    final productSales = <String, Map<String, dynamic>>{};

    for (var invoice in invoices) {
      for (var item in invoice.items) {
        if (productSales.containsKey(item.productName)) {
          productSales[item.productName]!['quantity'] += item.quantity;
          productSales[item.productName]!['total'] += item.totalSyp;
        } else {
          productSales[item.productName] = {
            'name': item.productName,
            'quantity': item.quantity,
            'total': item.totalSyp,
          };
        }
      }
    }

    final sortedProducts = productSales.values.toList();
    sortedProducts.sort((a, b) => b['quantity'].compareTo(a['quantity']));
    return sortedProducts;
  }

  /// بناء تنبيه المخزون المنخفض
  Widget _buildLowStockAlert() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final lowStockProducts = productProvider.products
            .where((p) => p.quantity <= p.minQuantity)
            .toList();

        if (lowStockProducts.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المخزون في حالة جيدة',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Text('جميع المنتجات متوفرة بكميات كافية'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      'تحذير: مخزون منخفض',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.orange,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...lowStockProducts.take(3).map((product) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Expanded(child: Text(product.name)),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${product.quantity} متبقي',
                            style: TextStyle(
                              color: Colors.orange[800],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (lowStockProducts.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'و ${lowStockProducts.length - 3} منتجات أخرى...',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _updateStatistics() {
    try {
      if (!mounted) return;

      final invoiceProvider = context.read<InvoiceProvider>();
      final productProvider = context.read<ProductProvider>();

      // تحديث إحصائيات المبيعات
      final salesStats = invoiceProvider.getSalesStatistics();

      // تحديث إحصائيات المخزون
      final products = productProvider.products;
      final lowStockCount = products.where((p) => p.isLowStock()).length;
      final outOfStockCount = products.where((p) => p.isOutOfStock()).length;

      debugPrint('الإحصائيات محدثة:');
      debugPrint('- مبيعات اليوم: ${salesStats['today_sales']}');
      debugPrint('- إجمالي الديون: ${salesStats['total_debt']}');
      debugPrint('- منتجات منخفضة المخزون: $lowStockCount');
      debugPrint('- منتجات نفدت: $outOfStockCount');
    } catch (e) {
      debugPrint('خطأ في تحديث الإحصائيات: $e');
    }
  }
}
