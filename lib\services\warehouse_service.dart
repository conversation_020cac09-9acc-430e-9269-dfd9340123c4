import 'package:flutter/foundation.dart';
import '../models/warehouse.dart';
import '../models/stock_movement.dart';

import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

/// خدمة إدارة المستودعات
class WarehouseService {
  static const String _warehousesBox = 'warehouses';
  static const String _warehouseStockBox = 'warehouse_stock';
  static const String _stockMovementsBox = 'stock_movements';

  /// تهيئة خدمة المستودعات
  static Future<void> initialize() async {
    try {
      // فحص إذا كان هناك مستودع افتراضي
      final warehouses = await getAllWarehouses();

      if (warehouses.isEmpty) {
        // إنشاء المستودع الرئيسي الافتراضي
        await _createDefaultWarehouse();
        debugPrint('✅ تم إنشاء المستودع الافتراضي');
      }

      debugPrint('✅ تم تهيئة خدمة المستودعات');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة المستودعات: $e');
      rethrow;
    }
  }

  // ==================== إدارة المستودعات ====================

  /// الحصول على جميع المستودعات
  static Future<List<Warehouse>> getAllWarehouses() async {
    try {
      final warehousesData =
          await OfflineDatabaseService.getBoxData(_warehousesBox);
      final warehouses = <Warehouse>[];

      for (var warehouseMap in warehousesData.values) {
        warehouses
            .add(Warehouse.fromJson(Map<String, dynamic>.from(warehouseMap)));
      }

      // ترتيب حسب الاسم
      warehouses.sort((a, b) => a.name.compareTo(b.name));

      return warehouses;
    } catch (e) {
      debugPrint('❌ فشل في جلب المستودعات: $e');
      return [];
    }
  }

  /// الحصول على مستودع بالمعرف
  static Future<Warehouse?> getWarehouseById(int id) async {
    try {
      final warehouseData = await OfflineDatabaseService.getBoxItem(
          _warehousesBox, id.toString());
      if (warehouseData != null) {
        return Warehouse.fromJson(Map<String, dynamic>.from(warehouseData));
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب المستودع: $e');
      return null;
    }
  }

  /// الحصول على المستودع الرئيسي
  static Future<Warehouse?> getMainWarehouse() async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.firstWhere(
        (warehouse) => warehouse.isMainWarehouse,
        orElse: () => warehouses.isNotEmpty
            ? warehouses.first
            : throw Exception('لا يوجد مستودع'),
      );
    } catch (e) {
      debugPrint('❌ فشل في جلب المستودع الرئيسي: $e');
      return null;
    }
  }

  /// إضافة مستودع جديد
  static Future<Warehouse> addWarehouse(Warehouse warehouse) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingWarehouses = await getAllWarehouses();
      final codeExists =
          existingWarehouses.any((w) => w.code == warehouse.code);
      if (codeExists) {
        throw Exception('رمز المستودع موجود مسبقاً');
      }

      // إنشاء معرف جديد
      final newId = await _getNextWarehouseId();
      final newWarehouse = warehouse.copyWith(
        id: newId,
        createdAt: DateTime.now(),
      );

      // حفظ المستودع
      await OfflineDatabaseService.saveBoxItem(
        _warehousesBox,
        newId.toString(),
        newWarehouse.toJson(),
      );

      // إرسال إشعار
      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة المستودع "${newWarehouse.name}" بنجاح',
      );

      debugPrint('✅ تم إضافة المستودع: ${newWarehouse.name}');
      return newWarehouse;
    } catch (e) {
      debugPrint('❌ فشل في إضافة المستودع: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة المستودع: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث مستودع
  static Future<Warehouse> updateWarehouse(Warehouse warehouse) async {
    try {
      final updatedWarehouse = warehouse.copyWith(updatedAt: DateTime.now());

      await OfflineDatabaseService.saveBoxItem(
        _warehousesBox,
        warehouse.id.toString(),
        updatedWarehouse.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث المستودع "${updatedWarehouse.name}" بنجاح',
      );

      debugPrint('✅ تم تحديث المستودع: ${updatedWarehouse.name}');
      return updatedWarehouse;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المستودع: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث المستودع: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// حذف مستودع
  static Future<void> deleteWarehouse(int warehouseId) async {
    try {
      final warehouse = await getWarehouseById(warehouseId);
      if (warehouse == null) {
        throw Exception('المستودع غير موجود');
      }

      if (warehouse.isMainWarehouse) {
        throw Exception('لا يمكن حذف المستودع الرئيسي');
      }

      // فحص إذا كان المستودع يحتوي على مخزون
      final hasStock = await _warehouseHasStock(warehouseId);
      if (hasStock) {
        throw Exception('لا يمكن حذف المستودع لوجود مخزون به');
      }

      await OfflineDatabaseService.deleteBoxItem(
          _warehousesBox, warehouseId.toString());

      await OfflineNotificationService.showSuccessNotification(
        'تم حذف المستودع "${warehouse.name}" بنجاح',
      );

      debugPrint('✅ تم حذف المستودع: ${warehouse.name}');
    } catch (e) {
      debugPrint('❌ فشل في حذف المستودع: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في حذف المستودع: ${e.toString()}',
      );
      rethrow;
    }
  }

  // ==================== إدارة مخزون المستودعات ====================

  /// الحصول على مخزون منتج في مستودع
  static Future<ProductWarehouseStock?> getProductStock(
      int productId, int warehouseId) async {
    try {
      final stockKey = '${productId}_$warehouseId';
      final stockData =
          await OfflineDatabaseService.getBoxItem(_warehouseStockBox, stockKey);

      if (stockData != null) {
        return ProductWarehouseStock.fromJson(
            Map<String, dynamic>.from(stockData));
      }

      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب مخزون المنتج: $e');
      return null;
    }
  }

  /// الحصول على إجمالي مخزون منتج في جميع المستودعات
  static Future<int> getTotalProductStock(int productId) async {
    try {
      final stockData =
          await OfflineDatabaseService.getBoxData(_warehouseStockBox);
      int totalStock = 0;

      for (var stockMap in stockData.values) {
        final stock =
            ProductWarehouseStock.fromJson(Map<String, dynamic>.from(stockMap));
        if (stock.productId == productId) {
          totalStock += stock.availableQuantity;
        }
      }

      return totalStock;
    } catch (e) {
      debugPrint('❌ فشل في حساب إجمالي المخزون: $e');
      return 0;
    }
  }

  /// الحصول على مخزون مستودع
  static Future<List<ProductWarehouseStock>> getWarehouseStock(
      int warehouseId) async {
    try {
      final stockData =
          await OfflineDatabaseService.getBoxData(_warehouseStockBox);
      final warehouseStock = <ProductWarehouseStock>[];

      for (var stockMap in stockData.values) {
        final stock =
            ProductWarehouseStock.fromJson(Map<String, dynamic>.from(stockMap));
        if (stock.warehouseId == warehouseId) {
          warehouseStock.add(stock);
        }
      }

      return warehouseStock;
    } catch (e) {
      debugPrint('❌ فشل في جلب مخزون المستودع: $e');
      return [];
    }
  }

  /// تحديث مخزون منتج في مستودع
  static Future<ProductWarehouseStock> updateProductStock({
    required int productId,
    required int warehouseId,
    required int newQuantity,
    String? location,
    String? reference,
    String? notes,
  }) async {
    try {
      final stockKey = '${productId}_$warehouseId';
      final existingStock = await getProductStock(productId, warehouseId);

      final updatedStock = existingStock?.copyWith(
            quantity: newQuantity,
            availableQuantity: newQuantity - (existingStock.reservedQuantity),
            location: location ?? existingStock.location,
            lastUpdated: DateTime.now(),
          ) ??
          ProductWarehouseStock(
            id: await _getNextStockId(),
            productId: productId,
            warehouseId: warehouseId,
            quantity: newQuantity,
            availableQuantity: newQuantity,
            location: location,
            lastUpdated: DateTime.now(),
          );

      // حفظ المخزون المحدث
      await OfflineDatabaseService.saveBoxItem(
        _warehouseStockBox,
        stockKey,
        updatedStock.toJson(),
      );

      // تسجيل حركة المخزون
      await _recordStockMovement(
        productId: productId,
        warehouseId: warehouseId,
        type: StockMovementType.adjustment,
        quantity: newQuantity - (existingStock?.quantity ?? 0),
        quantityBefore: existingStock?.quantity ?? 0,
        quantityAfter: newQuantity,
        reference: reference ?? 'تحديث مخزون',
        notes: notes,
      );

      debugPrint('✅ تم تحديث مخزون المنتج $productId في المستودع $warehouseId');
      return updatedStock;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المخزون: $e');
      rethrow;
    }
  }

  // ==================== وظائف مساعدة ====================

  /// إنشاء المستودع الافتراضي
  static Future<void> _createDefaultWarehouse() async {
    final defaultWarehouse = Warehouse(
      id: 1,
      name: 'المستودع الرئيسي',
      code: 'MAIN',
      location: 'المقر الرئيسي',
      description: 'المستودع الرئيسي الافتراضي',
      manager: 'مدير المستودع',
      isActive: true,
      isMainWarehouse: true,
      createdAt: DateTime.now(),
    );

    await OfflineDatabaseService.saveBoxItem(
      _warehousesBox,
      '1',
      defaultWarehouse.toJson(),
    );
  }

  /// الحصول على معرف مستودع جديد
  static Future<int> _getNextWarehouseId() async {
    final warehouses = await getAllWarehouses();
    if (warehouses.isEmpty) return 1;

    final maxId = warehouses.map((w) => w.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// الحصول على معرف مخزون جديد
  static Future<int> _getNextStockId() async {
    final stockData =
        await OfflineDatabaseService.getBoxData(_warehouseStockBox);
    if (stockData.isEmpty) return 1;

    int maxId = 0;
    for (var stockMap in stockData.values) {
      final stock =
          ProductWarehouseStock.fromJson(Map<String, dynamic>.from(stockMap));
      if (stock.id > maxId) maxId = stock.id;
    }

    return maxId + 1;
  }

  /// فحص إذا كان المستودع يحتوي على مخزون
  static Future<bool> _warehouseHasStock(int warehouseId) async {
    final warehouseStock = await getWarehouseStock(warehouseId);
    return warehouseStock.any((stock) => stock.quantity > 0);
  }

  /// تسجيل حركة مخزون
  static Future<void> _recordStockMovement({
    required int productId,
    required int warehouseId,
    required StockMovementType type,
    required int quantity,
    required int quantityBefore,
    required int quantityAfter,
    required String reference,
    String? notes,
    String? batchNumber,
    DateTime? expiryDate,
    int? fromWarehouseId,
    int? toWarehouseId,
  }) async {
    try {
      final movementId = await _getNextMovementId();

      final movement = StockMovement(
        id: movementId,
        productId: productId,
        warehouseId: warehouseId,
        type: type,
        quantity: quantity,
        quantityBefore: quantityBefore,
        quantityAfter: quantityAfter,
        reference: reference,
        notes: notes,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        userId: 'system', // يمكن تحديثه لاحقاً لدعم المستخدمين
        createdAt: DateTime.now(),
      );

      await OfflineDatabaseService.saveBoxItem(
        _stockMovementsBox,
        movementId.toString(),
        movement.toJson(),
      );

      debugPrint('✅ تم تسجيل حركة المخزون: ${movement.typeDescription}');
    } catch (e) {
      debugPrint('❌ فشل في تسجيل حركة المخزون: $e');
    }
  }

  /// الحصول على معرف حركة جديد
  static Future<int> _getNextMovementId() async {
    final movementsData =
        await OfflineDatabaseService.getBoxData(_stockMovementsBox);
    if (movementsData.isEmpty) return 1;

    int maxId = 0;
    for (var movementMap in movementsData.values) {
      final movement =
          StockMovement.fromJson(Map<String, dynamic>.from(movementMap));
      if (movement.id > maxId) maxId = movement.id;
    }

    return maxId + 1;
  }

  // ==================== نقل المخزون بين المستودعات ====================

  /// نقل مخزون بين المستودعات
  static Future<void> transferStock({
    required int productId,
    required int fromWarehouseId,
    required int toWarehouseId,
    required int quantity,
    String? notes,
    String? batchNumber,
    DateTime? expiryDate,
  }) async {
    try {
      // التحقق من وجود المستودعات
      final fromWarehouse = await getWarehouseById(fromWarehouseId);
      final toWarehouse = await getWarehouseById(toWarehouseId);

      if (fromWarehouse == null || toWarehouse == null) {
        throw Exception('المستودع غير موجود');
      }

      // التحقق من توفر الكمية في المستودع المصدر
      final sourceStock = await getProductStock(productId, fromWarehouseId);
      if (sourceStock == null || sourceStock.availableQuantity < quantity) {
        throw Exception('الكمية المطلوبة غير متوفرة في المستودع المصدر');
      }

      // تقليل المخزون من المستودع المصدر
      final newSourceQuantity = sourceStock.quantity - quantity;
      await updateProductStock(
        productId: productId,
        warehouseId: fromWarehouseId,
        newQuantity: newSourceQuantity,
        reference: 'نقل إلى ${toWarehouse.name}',
        notes: notes,
      );

      // زيادة المخزون في المستودع الهدف
      final targetStock = await getProductStock(productId, toWarehouseId);
      final newTargetQuantity = (targetStock?.quantity ?? 0) + quantity;
      await updateProductStock(
        productId: productId,
        warehouseId: toWarehouseId,
        newQuantity: newTargetQuantity,
        reference: 'نقل من ${fromWarehouse.name}',
        notes: notes,
      );

      // تسجيل حركة النقل للمستودع المصدر
      await _recordStockMovement(
        productId: productId,
        warehouseId: fromWarehouseId,
        type: StockMovementType.transfer,
        quantity: -quantity,
        quantityBefore: sourceStock.quantity,
        quantityAfter: newSourceQuantity,
        reference: 'نقل إلى ${toWarehouse.name}',
        notes: notes,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
      );

      // تسجيل حركة النقل للمستودع الهدف
      await _recordStockMovement(
        productId: productId,
        warehouseId: toWarehouseId,
        type: StockMovementType.transfer,
        quantity: quantity,
        quantityBefore: targetStock?.quantity ?? 0,
        quantityAfter: newTargetQuantity,
        reference: 'نقل من ${fromWarehouse.name}',
        notes: notes,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم نقل $quantity قطعة من ${fromWarehouse.name} إلى ${toWarehouse.name}',
      );

      debugPrint('✅ تم نقل المخزون بنجاح');
    } catch (e) {
      debugPrint('❌ فشل في نقل المخزون: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في نقل المخزون: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// الحصول على حركات المخزون
  static Future<List<StockMovement>> getStockMovements({
    int? productId,
    int? warehouseId,
    StockMovementType? type,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final movementsData =
          await OfflineDatabaseService.getBoxData(_stockMovementsBox);
      final movements = <StockMovement>[];

      for (var movementMap in movementsData.values) {
        final movement =
            StockMovement.fromJson(Map<String, dynamic>.from(movementMap));

        // تطبيق الفلاتر
        if (productId != null && movement.productId != productId) continue;
        if (warehouseId != null && movement.warehouseId != warehouseId) {
          continue;
        }
        if (type != null && movement.type != type) continue;
        if (fromDate != null && movement.createdAt.isBefore(fromDate)) continue;
        if (toDate != null && movement.createdAt.isAfter(toDate)) continue;

        movements.add(movement);
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      movements.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return movements;
    } catch (e) {
      debugPrint('❌ فشل في جلب حركات المخزون: $e');
      return [];
    }
  }

  /// تقرير مخزون المستودعات
  static Future<Map<String, dynamic>> getWarehousesStockReport() async {
    try {
      final warehouses = await getAllWarehouses();
      final warehousesReport = <Map<String, dynamic>>[];

      double totalInventoryValue = 0;
      int totalProducts = 0;
      int lowStockCount = 0;
      int outOfStockCount = 0;

      for (var warehouse in warehouses) {
        final warehouseStock = await getWarehouseStock(warehouse.id);
        double warehouseValue = 0;
        int warehouseLowStock = 0;
        int warehouseOutOfStock = 0;

        for (var stock in warehouseStock) {
          // يمكن إضافة حساب القيمة هنا إذا كانت متوفرة
          // warehouseValue += stock.quantity * product.cost;

          if (stock.isOutOfStock()) {
            warehouseOutOfStock++;
            outOfStockCount++;
          } else if (stock.isLowStock()) {
            warehouseLowStock++;
            lowStockCount++;
          }
        }

        warehousesReport.add({
          'warehouse': warehouse.toJson(),
          'total_products': warehouseStock.length,
          'total_quantity':
              warehouseStock.fold(0, (sum, stock) => sum + stock.quantity),
          'available_quantity': warehouseStock.fold(
              0, (sum, stock) => sum + stock.availableQuantity),
          'reserved_quantity': warehouseStock.fold(
              0, (sum, stock) => sum + stock.reservedQuantity),
          'low_stock_count': warehouseLowStock,
          'out_of_stock_count': warehouseOutOfStock,
          'inventory_value': warehouseValue,
        });

        totalProducts += warehouseStock.length;
        totalInventoryValue += warehouseValue;
      }

      return {
        'total_warehouses': warehouses.length,
        'active_warehouses': warehouses.where((w) => w.isActive).length,
        'total_products': totalProducts,
        'total_inventory_value': totalInventoryValue,
        'low_stock_count': lowStockCount,
        'out_of_stock_count': outOfStockCount,
        'warehouses': warehousesReport,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء تقرير المستودعات: $e');
      return {};
    }
  }
}
