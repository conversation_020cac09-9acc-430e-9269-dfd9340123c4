import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/security_service.dart';
import '../widgets/brand_footer.dart';

class ActivationScreen extends StatefulWidget {
  final bool isReset;

  const ActivationScreen({super.key, this.isReset = false});

  @override
  State<ActivationScreen> createState() => _ActivationScreenState();
}

class _ActivationScreenState extends State<ActivationScreen> {
  final _codeController = TextEditingController();
  final _newPinController = TextEditingController();
  final _confirmPinController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _codeVerified = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Stack(
            children: [
              Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // شعار التطبيق
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.security,
                                size: 64,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 24),

                            // العنوان
                            Text(
                              widget.isReset
                                  ? 'إعادة تعيين رمز PIN'
                                  : 'تفعيل التطبيق',
                              style: GoogleFonts.cairo(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),

                            Text(
                              widget.isReset
                                  ? 'أدخل رمز التفعيل لإعادة تعيين رمز PIN'
                                  : 'أدخل رمز التفعيل لبدء استخدام التطبيق',
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 32),

                            // حقل رمز التفعيل
                            if (!_codeVerified || !widget.isReset) ...[
                              TextFormField(
                                controller: _codeController,
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 2,
                                ),
                                decoration: InputDecoration(
                                  labelText: 'رمز التفعيل',
                                  hintText: '125874369',
                                  prefixIcon: const Icon(Icons.vpn_key),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال رمز التفعيل';
                                  }
                                  if (value.length != 9) {
                                    return 'رمز التفعيل يجب أن يكون 9 أرقام';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                            ],

                            // حقول PIN الجديد (للإعادة تعيين)
                            if (_codeVerified && widget.isReset) ...[
                              TextFormField(
                                controller: _newPinController,
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                obscureText: true,
                                maxLength: 4,
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 4,
                                ),
                                decoration: InputDecoration(
                                  labelText: 'رمز PIN الجديد',
                                  hintText: '••••',
                                  prefixIcon: const Icon(Icons.lock),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  counterText: '',
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال رمز PIN';
                                  }
                                  if (value.length != 4) {
                                    return 'رمز PIN يجب أن يكون 4 أرقام';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _confirmPinController,
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                obscureText: true,
                                maxLength: 4,
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 4,
                                ),
                                decoration: InputDecoration(
                                  labelText: 'تأكيد رمز PIN',
                                  hintText: '••••',
                                  prefixIcon: const Icon(Icons.lock_outline),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  counterText: '',
                                ),
                                validator: (value) {
                                  if (value != _newPinController.text) {
                                    return 'رمز PIN غير متطابق';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 24),
                            ],

                            // زر التفعيل
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _handleSubmit,
                                style: ElevatedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : Text(
                                        _getButtonText(),
                                        style: GoogleFonts.cairo(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // العلامة التجارية
              const LoginBrandFooter(),
            ],
          ),
        ),
      ),
    );
  }

  String _getButtonText() {
    if (widget.isReset) {
      return _codeVerified ? 'تعيين رمز PIN الجديد' : 'التحقق من رمز التفعيل';
    }
    return 'تفعيل التطبيق';
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.isReset) {
        if (!_codeVerified) {
          // التحقق من رمز التفعيل
          final isValid =
              await SecurityService.activateApp(_codeController.text);
          if (isValid) {
            setState(() {
              _codeVerified = true;
            });
          } else {
            _showError('رمز التفعيل غير صحيح');
          }
        } else {
          // إعادة تعيين PIN
          final success = await SecurityService.resetPin(
            '125874369', // رمز التفعيل الثابت
            _newPinController.text,
          );

          if (success) {
            _showSuccess('تم إعادة تعيين رمز PIN بنجاح');
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/pin-setup');
            }
          } else {
            _showError('فشل في إعادة تعيين رمز PIN');
          }
        }
      } else {
        // تفعيل التطبيق
        final success = await SecurityService.activateApp(_codeController.text);
        if (success) {
          _showSuccess('تم تفعيل التطبيق بنجاح');
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/pin-setup');
          }
        } else {
          _showError('رمز التفعيل غير صحيح');
        }
      }
    } catch (e) {
      _showError('حدث خطأ: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    _newPinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }
}
