# نظام إدارة الفواتير والمخزون

## وصف التطبيق
تطبيق شامل لإدارة الفواتير والمخزون مبني بـ Flutter للواجهة الأمامية و Django للخادم الخلفي.

## المميزات الرئيسية

### 📱 **التطبيق المحمول (Flutter)**
- **إدارة المنتجات**: إضافة، تعديل، حذف المنتجات
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **إنشاء الفواتير**: فواتير مفصلة مع أنواع دفع متعددة
- **التقارير**: تقارير مبيعات وتحليلات
- **إدارة المخزون**: تتبع الكميات والتنبيهات
- **أسعار الصرف**: دعم العملات المتعددة (ليرة سورية/دولار)

### 🖥️ **الخادم الخلفي (Django)**
- **API RESTful**: واجهة برمجة تطبيقات شاملة
- **المصادقة**: نظام JWT للأمان
- **قاعدة البيانات**: SQLite للتطوير
- **CORS**: دعم الطلبات من التطبيق المحمول

## هيكل المشروع

```
├── lib/                    # كود Flutter
│   ├── main.dart          # نقطة البداية
│   ├── models/            # نماذج البيانات
│   ├── providers/         # إدارة الحالة
│   ├── screens/           # شاشات التطبيق
│   ├── services/          # خدمات API
│   └── widgets/           # مكونات قابلة للإعادة
├── backend/               # كود Django
│   ├── core/             # إعدادات المشروع
│   ├── inventory/        # تطبيق المخزون
│   └── accounts/         # تطبيق المستخدمين
└── android/              # إعدادات Android
```

## التقنيات المستخدمة

### Frontend (Flutter)
- **Flutter 3.29.3**
- **Provider** - إدارة الحالة
- **HTTP** - طلبات الشبكة
- **SharedPreferences** - التخزين المحلي
- **FL Chart** - الرسوم البيانية
- **Intl** - التدويل

### Backend (Django)
- **Django** - إطار العمل الرئيسي
- **Django REST Framework** - API
- **SimpleJWT** - المصادقة
- **CORS Headers** - دعم CORS
- **SQLite** - قاعدة البيانات

## كيفية التشغيل

### 1. تشغيل الخادم الخلفي
```bash
cd backend
pip install django djangorestframework django-cors-headers djangorestframework-simplejwt
python manage.py migrate
python manage.py runserver
```

### 2. تشغيل التطبيق المحمول
```bash
flutter pub get
flutter run
```

### 3. بناء APK
```bash
flutter build apk --release
```

## الشاشات الرئيسية

1. **شاشة تسجيل الدخول**: مصادقة المستخدم
2. **الشاشة الرئيسية**: لوحة التحكم
3. **إدارة المنتجات**: عرض وإدارة المنتجات
4. **إدارة العملاء**: قاعدة بيانات العملاء
5. **الفواتير**: إنشاء وإدارة الفواتير
6. **التقارير**: تحليلات المبيعات
7. **الإعدادات**: إعدادات التطبيق

## الاتصال بالخادم
- **عنوان الخادم**: `http://********:8000/api` (للمحاكي)
- **المصادقة**: JWT Token
- **تنسيق البيانات**: JSON

## ملاحظات مهمة
- التطبيق يدعم اللغة العربية
- يمكن العمل مع عملات متعددة
- نظام تنبيهات للمخزون المنخفض
- حفظ البيانات محلياً للعمل بدون إنترنت

## المطور
تم تطوير هذا التطبيق باستخدام أحدث التقنيات لضمان الأداء والموثوقية.
