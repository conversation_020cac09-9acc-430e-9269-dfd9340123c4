import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/invoice.dart';

import '../services/accounting_service.dart';
import 'offline_database_service.dart';

/// خدمة التقارير المالية المتقدمة
class FinancialReportsService {
  /// إنشاء قائمة المركز المالي (الميزانية العمومية)
  static Future<Map<String, dynamic>> generateBalanceSheet({
    required DateTime asOfDate,
    String currency = 'SYP',
    String companyName = 'الشركة',
  }) async {
    try {
      final accounts = await AccountingService.getAllAccounts();

      // تصنيف الحسابات
      final assets = <Map<String, dynamic>>[];
      final liabilities = <Map<String, dynamic>>[];
      final equity = <Map<String, dynamic>>[];

      double totalAssets = 0;
      double totalLiabilities = 0;
      double totalEquity = 0;

      for (var account in accounts) {
        final balance = await AccountingService.getAccountBalance(account.id);

        if (balance != 0) {
          final accountData = {
            'account_code': account.code,
            'account_name': account.name,
            'amount': balance.abs(),
            'formatted_amount': '${balance.abs().toStringAsFixed(2)} $currency',
          };

          switch (account.type) {
            case AccountType.asset:
              assets.add(accountData);
              totalAssets += balance.abs();
              break;
            case AccountType.liability:
              liabilities.add(accountData);
              totalLiabilities += balance.abs();
              break;
            case AccountType.equity:
              equity.add(accountData);
              totalEquity += balance.abs();
              break;
            default:
              break;
          }
        }
      }

      // ترتيب الحسابات حسب الرمز
      assets.sort((a, b) => a['account_code'].compareTo(b['account_code']));
      liabilities
          .sort((a, b) => a['account_code'].compareTo(b['account_code']));
      equity.sort((a, b) => a['account_code'].compareTo(b['account_code']));

      return {
        'report_type': 'balance_sheet',
        'as_of_date': asOfDate.toIso8601String(),
        'company_name': companyName,
        'currency': currency,
        'assets': {
          'items': assets,
          'total': totalAssets,
          'formatted_total': '${totalAssets.toStringAsFixed(2)} $currency',
        },
        'liabilities': {
          'items': liabilities,
          'total': totalLiabilities,
          'formatted_total': '${totalLiabilities.toStringAsFixed(2)} $currency',
        },
        'equity': {
          'items': equity,
          'total': totalEquity,
          'formatted_total': '${totalEquity.toStringAsFixed(2)} $currency',
        },
        'total_liabilities_and_equity': totalLiabilities + totalEquity,
        'is_balanced':
            (totalAssets - (totalLiabilities + totalEquity)).abs() < 0.01,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قائمة المركز المالي: $e');
      rethrow;
    }
  }

  /// إنشاء قائمة الدخل
  static Future<Map<String, dynamic>> generateIncomeStatement({
    required DateTime startDate,
    required DateTime endDate,
    String currency = 'SYP',
    String companyName = 'الشركة',
  }) async {
    try {
      final accounts = await AccountingService.getAllAccounts();
      final entries = await AccountingService.getAllJournalEntries();

      // فلترة القيود حسب الفترة
      final periodEntries = entries
          .where((entry) =>
              entry.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
              entry.date.isBefore(endDate.add(const Duration(days: 1))))
          .toList();

      final revenues = <Map<String, dynamic>>[];
      final expenses = <Map<String, dynamic>>[];

      double totalRevenue = 0;
      double totalExpenses = 0;

      // حساب الإيرادات والمصروفات
      final accountTotals = <int, double>{};

      for (var entry in periodEntries) {
        for (var line in entry.lines) {
          accountTotals[line.accountId] = (accountTotals[line.accountId] ?? 0) +
              line.creditAmount -
              line.debitAmount;
        }
      }

      for (var account in accounts) {
        final amount = accountTotals[account.id] ?? 0;

        if (amount != 0) {
          final accountData = {
            'account_code': account.code,
            'account_name': account.name,
            'amount': amount.abs(),
            'formatted_amount': '${amount.abs().toStringAsFixed(2)} $currency',
          };

          if (account.type == AccountType.revenue) {
            revenues.add(accountData);
            totalRevenue += amount.abs();
          } else if (account.type == AccountType.expense) {
            expenses.add(accountData);
            totalExpenses += amount.abs();
          }
        }
      }

      final netIncome = totalRevenue - totalExpenses;
      final profitMargin =
          totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

      return {
        'report_type': 'income_statement',
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'company_name': companyName,
        'currency': currency,
        'revenues': {
          'items': revenues,
          'total': totalRevenue,
          'formatted_total': '${totalRevenue.toStringAsFixed(2)} $currency',
        },
        'expenses': {
          'items': expenses,
          'total': totalExpenses,
          'formatted_total': '${totalExpenses.toStringAsFixed(2)} $currency',
        },
        'net_income': netIncome,
        'formatted_net_income': '${netIncome.toStringAsFixed(2)} $currency',
        'profit_margin': profitMargin,
        'formatted_profit_margin': '${profitMargin.toStringAsFixed(2)}%',
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء قائمة الدخل: $e');
      rethrow;
    }
  }

  /// إنشاء ميزان المراجعة
  static Future<Map<String, dynamic>> generateTrialBalance({
    required DateTime asOfDate,
    String currency = 'SYP',
    String companyName = 'الشركة',
  }) async {
    try {
      final accounts = await AccountingService.getAllAccounts();
      final trialBalanceAccounts = <Map<String, dynamic>>[];

      double totalDebit = 0;
      double totalCredit = 0;

      for (var account in accounts) {
        final balance = await AccountingService.getAccountBalance(account.id);

        if (balance != 0) {
          double debitBalance = 0;
          double creditBalance = 0;

          if (account.isDebitAccount) {
            if (balance > 0) {
              debitBalance = balance;
            } else {
              creditBalance = balance.abs();
            }
          } else {
            if (balance > 0) {
              creditBalance = balance;
            } else {
              debitBalance = balance.abs();
            }
          }

          trialBalanceAccounts.add({
            'account_code': account.code,
            'account_name': account.name,
            'account_type': account.typeDisplayName,
            'debit_balance': debitBalance,
            'credit_balance': creditBalance,
            'formatted_debit': '${debitBalance.toStringAsFixed(2)} $currency',
            'formatted_credit': '${creditBalance.toStringAsFixed(2)} $currency',
          });

          totalDebit += debitBalance;
          totalCredit += creditBalance;
        }
      }

      // ترتيب حسب رمز الحساب
      trialBalanceAccounts
          .sort((a, b) => a['account_code'].compareTo(b['account_code']));

      return {
        'report_type': 'trial_balance',
        'as_of_date': asOfDate.toIso8601String(),
        'company_name': companyName,
        'currency': currency,
        'accounts': trialBalanceAccounts,
        'total_debit': totalDebit,
        'total_credit': totalCredit,
        'formatted_total_debit': '${totalDebit.toStringAsFixed(2)} $currency',
        'formatted_total_credit': '${totalCredit.toStringAsFixed(2)} $currency',
        'is_balanced': (totalDebit - totalCredit).abs() < 0.01,
        'difference': totalDebit - totalCredit,
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء ميزان المراجعة: $e');
      rethrow;
    }
  }

  /// إنشاء تقرير النسب المالية
  static Future<Map<String, dynamic>> generateFinancialRatios({
    required DateTime asOfDate,
    String currency = 'SYP',
    String companyName = 'الشركة',
  }) async {
    try {
      // الحصول على البيانات المطلوبة
      final balanceSheet = await generateBalanceSheet(
        asOfDate: asOfDate,
        currency: currency,
        companyName: companyName,
      );

      final incomeStatement = await generateIncomeStatement(
        startDate: DateTime(asOfDate.year, 1, 1),
        endDate: asOfDate,
        currency: currency,
        companyName: companyName,
      );

      final totalAssets = balanceSheet['assets']['total'] as double;
      final totalLiabilities = balanceSheet['liabilities']['total'] as double;
      final totalEquity = balanceSheet['equity']['total'] as double;
      final totalRevenue = incomeStatement['revenues']['total'] as double;
      final netIncome = incomeStatement['net_income'] as double;

      // حساب النسب المالية
      final currentRatio = totalAssets > 0
          ? totalAssets / (totalLiabilities > 0 ? totalLiabilities : 1)
          : 0;
      final debtToEquityRatio =
          totalEquity > 0 ? totalLiabilities / totalEquity : 0;
      final returnOnAssets =
          totalAssets > 0 ? (netIncome / totalAssets) * 100 : 0;
      final returnOnEquity =
          totalEquity > 0 ? (netIncome / totalEquity) * 100 : 0;
      final profitMargin =
          totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

      // تقييم الوضع المالي
      String healthAssessment = 'متوسط';
      int score = 0;

      if (currentRatio >= 2.0) {
        score += 2;
      } else if (currentRatio >= 1.5) {
        score += 1;
      }

      if (profitMargin >= 15) {
        score += 2;
      } else if (profitMargin >= 10) {
        score += 1;
      }

      if (returnOnEquity >= 20) {
        score += 2;
      } else if (returnOnEquity >= 15) {
        score += 1;
      }

      if (debtToEquityRatio <= 0.5) {
        score += 2;
      } else if (debtToEquityRatio <= 1.0) {
        score += 1;
      }

      if (score >= 7) {
        healthAssessment = 'ممتاز';
      } else if (score >= 5) {
        healthAssessment = 'جيد';
      } else if (score >= 3) {
        healthAssessment = 'متوسط';
      } else {
        healthAssessment = 'ضعيف';
      }

      return {
        'report_type': 'financial_ratios',
        'as_of_date': asOfDate.toIso8601String(),
        'company_name': companyName,
        'currency': currency,
        'ratios': {
          'current_ratio': {
            'value': currentRatio,
            'formatted': currentRatio.toStringAsFixed(2),
            'description': 'النسبة الجارية',
            'interpretation': currentRatio >= 2.0
                ? 'ممتاز'
                : currentRatio >= 1.5
                    ? 'جيد'
                    : 'ضعيف',
          },
          'debt_to_equity_ratio': {
            'value': debtToEquityRatio,
            'formatted': debtToEquityRatio.toStringAsFixed(2),
            'description': 'نسبة الدين إلى حقوق الملكية',
            'interpretation': debtToEquityRatio <= 0.5
                ? 'ممتاز'
                : debtToEquityRatio <= 1.0
                    ? 'جيد'
                    : 'ضعيف',
          },
          'return_on_assets': {
            'value': returnOnAssets,
            'formatted': '${returnOnAssets.toStringAsFixed(2)}%',
            'description': 'العائد على الأصول',
            'interpretation': returnOnAssets >= 15
                ? 'ممتاز'
                : returnOnAssets >= 10
                    ? 'جيد'
                    : 'ضعيف',
          },
          'return_on_equity': {
            'value': returnOnEquity,
            'formatted': '${returnOnEquity.toStringAsFixed(2)}%',
            'description': 'العائد على حقوق الملكية',
            'interpretation': returnOnEquity >= 20
                ? 'ممتاز'
                : returnOnEquity >= 15
                    ? 'جيد'
                    : 'ضعيف',
          },
          'profit_margin': {
            'value': profitMargin,
            'formatted': '${profitMargin.toStringAsFixed(2)}%',
            'description': 'هامش الربح الصافي',
            'interpretation': profitMargin >= 15
                ? 'ممتاز'
                : profitMargin >= 10
                    ? 'جيد'
                    : 'ضعيف',
          },
        },
        'overall_assessment': {
          'score': score,
          'max_score': 8,
          'rating': healthAssessment,
          'description': _getHealthDescription(healthAssessment),
        },
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء تقرير النسب المالية: $e');
      rethrow;
    }
  }

  /// إنشاء تقرير مقارن (سنوي)
  static Future<Map<String, dynamic>> generateComparativeReport({
    required DateTime currentYearEnd,
    required DateTime previousYearEnd,
    String currency = 'SYP',
    String companyName = 'الشركة',
  }) async {
    try {
      final currentYear = await generateIncomeStatement(
        startDate: DateTime(currentYearEnd.year, 1, 1),
        endDate: currentYearEnd,
        currency: currency,
        companyName: companyName,
      );

      final previousYear = await generateIncomeStatement(
        startDate: DateTime(previousYearEnd.year, 1, 1),
        endDate: previousYearEnd,
        currency: currency,
        companyName: companyName,
      );

      final currentRevenue = currentYear['revenues']['total'] as double;
      final previousRevenue = previousYear['revenues']['total'] as double;
      final currentNetIncome = currentYear['net_income'] as double;
      final previousNetIncome = previousYear['net_income'] as double;

      final revenueGrowth = previousRevenue > 0
          ? ((currentRevenue - previousRevenue) / previousRevenue) * 100
          : 0;

      final incomeGrowth = previousNetIncome > 0
          ? ((currentNetIncome - previousNetIncome) / previousNetIncome) * 100
          : 0;

      return {
        'report_type': 'comparative_report',
        'current_year': currentYearEnd.year,
        'previous_year': previousYearEnd.year,
        'company_name': companyName,
        'currency': currency,
        'comparison': {
          'revenue': {
            'current': currentRevenue,
            'previous': previousRevenue,
            'change': currentRevenue - previousRevenue,
            'growth_rate': revenueGrowth,
            'formatted_current':
                '${currentRevenue.toStringAsFixed(2)} $currency',
            'formatted_previous':
                '${previousRevenue.toStringAsFixed(2)} $currency',
            'formatted_growth': '${revenueGrowth.toStringAsFixed(2)}%',
          },
          'net_income': {
            'current': currentNetIncome,
            'previous': previousNetIncome,
            'change': currentNetIncome - previousNetIncome,
            'growth_rate': incomeGrowth,
            'formatted_current':
                '${currentNetIncome.toStringAsFixed(2)} $currency',
            'formatted_previous':
                '${previousNetIncome.toStringAsFixed(2)} $currency',
            'formatted_growth': '${incomeGrowth.toStringAsFixed(2)}%',
          },
        },
        'analysis': {
          'revenue_trend': revenueGrowth > 0 ? 'نمو' : 'انخفاض',
          'income_trend': incomeGrowth > 0 ? 'تحسن' : 'تراجع',
          'overall_performance': (revenueGrowth > 0 && incomeGrowth > 0)
              ? 'إيجابي'
              : 'يحتاج تحسين',
        },
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء التقرير المقارن: $e');
      rethrow;
    }
  }

  /// وصف الحالة المالية
  static String _getHealthDescription(String rating) {
    switch (rating) {
      case 'ممتاز':
        return 'الوضع المالي للشركة ممتاز مع نسب مالية قوية ومؤشرات إيجابية';
      case 'جيد':
        return 'الوضع المالي للشركة جيد مع إمكانية للتحسين في بعض المجالات';
      case 'متوسط':
        return 'الوضع المالي للشركة متوسط ويحتاج إلى مراجعة وتحسين';
      case 'ضعيف':
        return 'الوضع المالي للشركة ضعيف ويحتاج إلى إجراءات عاجلة للتحسين';
      default:
        return 'غير محدد';
    }
  }

  // ==================== التقارير التشغيلية ====================

  /// تقرير المبيعات اليومية
  static Future<Map<String, dynamic>> getDailySalesReport(DateTime date) async {
    try {
      final invoices = await OfflineDatabaseService.getAllInvoices();
      final dailyInvoices = invoices
          .where((invoice) =>
              invoice.date.year == date.year &&
              invoice.date.month == date.month &&
              invoice.date.day == date.day)
          .toList();

      double totalSales = 0;
      double totalCash = 0;
      double totalDebt = 0;
      int totalInvoices = dailyInvoices.length;

      for (var invoice in dailyInvoices) {
        totalSales += invoice.totalAmountSyp;
        if (invoice.paymentType == PaymentType.cash) {
          totalCash += invoice.totalAmountSyp;
        } else {
          totalDebt += invoice.remainingAmountSyp;
        }
      }

      return {
        'date': date.toIso8601String(),
        'total_sales': totalSales,
        'total_cash': totalCash,
        'total_debt': totalDebt,
        'total_invoices': totalInvoices,
        'average_invoice': totalInvoices > 0 ? totalSales / totalInvoices : 0,
        'invoices': dailyInvoices.map((i) => i.toJson()).toList(),
      };
    } catch (e) {
      debugPrint('خطأ في تقرير المبيعات اليومية: $e');
      return {};
    }
  }

  /// تقرير المبيعات الشهرية
  static Future<Map<String, dynamic>> getMonthlySalesReport(
      int year, int month) async {
    try {
      final invoices = await OfflineDatabaseService.getAllInvoices();
      final monthlyInvoices = invoices
          .where((invoice) =>
              invoice.date.year == year && invoice.date.month == month)
          .toList();

      double totalSales = 0;
      double totalCash = 0;
      double totalDebt = 0;
      Map<int, double> dailySales = {};

      for (var invoice in monthlyInvoices) {
        totalSales += invoice.totalAmountSyp;
        if (invoice.paymentType == PaymentType.cash) {
          totalCash += invoice.totalAmountSyp;
        } else {
          totalDebt += invoice.remainingAmountSyp;
        }

        // تجميع المبيعات اليومية
        int day = invoice.date.day;
        dailySales[day] = (dailySales[day] ?? 0) + invoice.totalAmountSyp;
      }

      return {
        'year': year,
        'month': month,
        'total_sales': totalSales,
        'total_cash': totalCash,
        'total_debt': totalDebt,
        'total_invoices': monthlyInvoices.length,
        'daily_sales': dailySales,
        'average_daily':
            dailySales.isNotEmpty ? totalSales / dailySales.length : 0,
      };
    } catch (e) {
      debugPrint('خطأ في تقرير المبيعات الشهرية: $e');
      return {};
    }
  }

  /// تقرير المخزون
  static Future<Map<String, dynamic>> getInventoryReport() async {
    try {
      final products = await OfflineDatabaseService.getAllProducts();

      double totalInventoryValue = 0;
      int lowStockCount = 0;
      int outOfStockCount = 0;
      List<Map<String, dynamic>> lowStockProducts = [];
      List<Map<String, dynamic>> outOfStockProducts = [];

      for (var product in products) {
        totalInventoryValue += product.purchasePrice * product.quantity;

        if (product.isOutOfStock()) {
          outOfStockCount++;
          outOfStockProducts.add({
            'id': product.id,
            'name': product.name,
            'quantity': product.quantity,
            'min_quantity': product.minQuantity,
          });
        } else if (product.isLowStock()) {
          lowStockCount++;
          lowStockProducts.add({
            'id': product.id,
            'name': product.name,
            'quantity': product.quantity,
            'min_quantity': product.minQuantity,
          });
        }
      }

      return {
        'total_products': products.length,
        'total_inventory_value': totalInventoryValue,
        'low_stock_count': lowStockCount,
        'out_of_stock_count': outOfStockCount,
        'low_stock_products': lowStockProducts,
        'out_of_stock_products': outOfStockProducts,
        'inventory_status': {
          'healthy': products.length - lowStockCount - outOfStockCount,
          'low_stock': lowStockCount,
          'out_of_stock': outOfStockCount,
        },
      };
    } catch (e) {
      debugPrint('خطأ في تقرير المخزون: $e');
      return {};
    }
  }

  /// تقرير العملاء والديون
  static Future<Map<String, dynamic>> getCustomerDebtReport() async {
    try {
      final customers = await OfflineDatabaseService.getAllCustomers();
      final invoices = await OfflineDatabaseService.getAllInvoices();

      double totalDebt = 0;
      List<Map<String, dynamic>> customerDebts = [];

      for (var customer in customers) {
        final customerInvoices =
            invoices.where((i) => i.customerId == customer.id).toList();
        double customerTotalDebt = 0;

        for (var invoice in customerInvoices) {
          customerTotalDebt += invoice.remainingAmountSyp;
        }

        if (customerTotalDebt > 0) {
          customerDebts.add({
            'customer_id': customer.id,
            'customer_name': customer.name,
            'phone': customer.phone,
            'total_debt': customerTotalDebt,
            'invoices_count': customerInvoices.length,
          });
        }

        totalDebt += customerTotalDebt;
      }

      // ترتيب العملاء حسب الدين (الأعلى أولاً)
      customerDebts.sort((a, b) => b['total_debt'].compareTo(a['total_debt']));

      return {
        'total_customers': customers.length,
        'customers_with_debt': customerDebts.length,
        'total_debt': totalDebt,
        'average_debt':
            customerDebts.isNotEmpty ? totalDebt / customerDebts.length : 0,
        'customer_debts': customerDebts,
      };
    } catch (e) {
      debugPrint('خطأ في تقرير العملاء والديون: $e');
      return {};
    }
  }
}
