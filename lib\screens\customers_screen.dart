import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/customer_provider.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<CustomerProvider>().fetchCustomers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملاء'),
      ),
      body: Consumer<CustomerProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error.isNotEmpty) {
            return Center(child: Text(provider.error));
          }

          if (provider.customers.isEmpty) {
            return const Center(child: Text('لا يوجد عملاء'));
          }

          return ListView.builder(
            itemCount: provider.customers.length,
            itemBuilder: (context, index) {
              final customer = provider.customers[index];
              return CustomerListItem(
                customer: customer,
                onEdit: () => _showCustomerDialog(context, customer),
                onDelete: () => _deleteCustomer(context, customer.id),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCustomerDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<void> _showCustomerDialog(BuildContext context,
      [Customer? customer]) async {
    final nameController = TextEditingController(text: customer?.name);
    final phoneController = TextEditingController(text: customer?.phone);
    final addressController = TextEditingController(text: customer?.address);

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(customer == null ? 'إضافة عميل' : 'تعديل بيانات العميل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'اسم العميل'),
            ),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(labelText: 'رقم الهاتف'),
              keyboardType: TextInputType.phone,
            ),
            TextField(
              controller: addressController,
              decoration: const InputDecoration(labelText: 'العنوان'),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop({
                'name': nameController.text,
                'phone': phoneController.text,
                'address': addressController.text,
              });
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _handleCustomerSave(result, customer);
    }
  }

  Future<void> _handleCustomerSave(
      Map<String, String> result, Customer? customer) async {
    if (!mounted) return;
    final provider = context.read<CustomerProvider>();
    try {
      if (customer == null) {
        await provider.addCustomer(Customer(
          id: 0,
          name: result['name']!,
          phone: result['phone']!,
          address: result['address']!,
          totalDebt: 0,
          createdAt: DateTime.now(),
        ));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة العميل بنجاح')),
          );
        }
      } else {
        await provider.updateCustomer(Customer(
          id: customer.id,
          name: result['name']!,
          phone: result['phone']!,
          address: result['address']!,
          totalDebt: customer.totalDebt,
          createdAt: customer.createdAt,
        ));
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث بيانات العميل بنجاح')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(provider.error)),
        );
      }
    }
  }

  Future<void> _deleteCustomer(BuildContext context, int id) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العميل'),
        content: const Text('هل أنت متأكد من حذف هذا العميل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      if (!mounted) return;

      // استخراج المراجع قبل العملية async
      final provider = context.read<CustomerProvider>();
      final messenger = ScaffoldMessenger.of(context);

      try {
        await provider.deleteCustomer(id);
        if (mounted) {
          messenger.showSnackBar(
            const SnackBar(content: Text('تم حذف العميل بنجاح')),
          );
        }
      } catch (e) {
        // تم معالجة الخطأ في مزود الحالة
      }
    }
  }
}

class CustomerListItem extends StatelessWidget {
  final Customer customer;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CustomerListItem({
    super.key,
    required this.customer,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(customer.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (customer.phone.isNotEmpty) Text('هاتف: ${customer.phone}'),
            if (customer.address.isNotEmpty)
              Text('العنوان: ${customer.address}'),
            Text(
              'الدين: ${customer.totalDebt.toStringAsFixed(2)} ل.س',
              style: TextStyle(
                color: customer.totalDebt > 0 ? Colors.red : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }
}
