// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profitability_analysis.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProfitabilityAnalysisAdapter extends TypeAdapter<ProfitabilityAnalysis> {
  @override
  final int typeId = 16;

  @override
  ProfitabilityAnalysis read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProfitabilityAnalysis(
      id: fields[0] as int,
      periodStart: fields[1] as DateTime,
      periodEnd: fields[2] as DateTime,
      totalRevenue: fields[3] as double,
      totalCost: fields[4] as double,
      grossProfit: fields[5] as double,
      operatingExpenses: fields[6] as double,
      netProfit: fields[7] as double,
      grossProfitMargin: fields[8] as double,
      netProfitMargin: fields[9] as double,
      revenueByCategory: (fields[10] as Map).cast<String, double>(),
      costByCategory: (fields[11] as Map).cast<String, double>(),
      profitByProduct: (fields[12] as Map).cast<String, double>(),
      profitByCustomer: (fields[13] as Map).cast<String, double>(),
      trends: (fields[14] as List).cast<ProfitTrend>(),
      createdAt: fields[15] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ProfitabilityAnalysis obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.periodStart)
      ..writeByte(2)
      ..write(obj.periodEnd)
      ..writeByte(3)
      ..write(obj.totalRevenue)
      ..writeByte(4)
      ..write(obj.totalCost)
      ..writeByte(5)
      ..write(obj.grossProfit)
      ..writeByte(6)
      ..write(obj.operatingExpenses)
      ..writeByte(7)
      ..write(obj.netProfit)
      ..writeByte(8)
      ..write(obj.grossProfitMargin)
      ..writeByte(9)
      ..write(obj.netProfitMargin)
      ..writeByte(10)
      ..write(obj.revenueByCategory)
      ..writeByte(11)
      ..write(obj.costByCategory)
      ..writeByte(12)
      ..write(obj.profitByProduct)
      ..writeByte(13)
      ..write(obj.profitByCustomer)
      ..writeByte(14)
      ..write(obj.trends)
      ..writeByte(15)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProfitabilityAnalysisAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProfitTrendAdapter extends TypeAdapter<ProfitTrend> {
  @override
  final int typeId = 17;

  @override
  ProfitTrend read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProfitTrend(
      date: fields[0] as DateTime,
      revenue: fields[1] as double,
      cost: fields[2] as double,
      profit: fields[3] as double,
      profitMargin: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, ProfitTrend obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.revenue)
      ..writeByte(2)
      ..write(obj.cost)
      ..writeByte(3)
      ..write(obj.profit)
      ..writeByte(4)
      ..write(obj.profitMargin);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProfitTrendAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductProfitabilityAdapter extends TypeAdapter<ProductProfitability> {
  @override
  final int typeId = 18;

  @override
  ProductProfitability read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductProfitability(
      productId: fields[0] as int,
      productName: fields[1] as String,
      quantitySold: fields[2] as int,
      totalRevenue: fields[3] as double,
      totalCost: fields[4] as double,
      grossProfit: fields[5] as double,
      profitMargin: fields[6] as double,
      averageSellingPrice: fields[7] as double,
      averageCost: fields[8] as double,
      periodStart: fields[9] as DateTime,
      periodEnd: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ProductProfitability obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.productId)
      ..writeByte(1)
      ..write(obj.productName)
      ..writeByte(2)
      ..write(obj.quantitySold)
      ..writeByte(3)
      ..write(obj.totalRevenue)
      ..writeByte(4)
      ..write(obj.totalCost)
      ..writeByte(5)
      ..write(obj.grossProfit)
      ..writeByte(6)
      ..write(obj.profitMargin)
      ..writeByte(7)
      ..write(obj.averageSellingPrice)
      ..writeByte(8)
      ..write(obj.averageCost)
      ..writeByte(9)
      ..write(obj.periodStart)
      ..writeByte(10)
      ..write(obj.periodEnd);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductProfitabilityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerProfitabilityAdapter extends TypeAdapter<CustomerProfitability> {
  @override
  final int typeId = 19;

  @override
  CustomerProfitability read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerProfitability(
      customerId: fields[0] as int,
      customerName: fields[1] as String,
      totalOrders: fields[2] as int,
      totalRevenue: fields[3] as double,
      totalCost: fields[4] as double,
      grossProfit: fields[5] as double,
      profitMargin: fields[6] as double,
      averageOrderValue: fields[7] as double,
      customerLifetimeValue: fields[8] as double,
      firstOrderDate: fields[9] as DateTime,
      lastOrderDate: fields[10] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, CustomerProfitability obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.customerId)
      ..writeByte(1)
      ..write(obj.customerName)
      ..writeByte(2)
      ..write(obj.totalOrders)
      ..writeByte(3)
      ..write(obj.totalRevenue)
      ..writeByte(4)
      ..write(obj.totalCost)
      ..writeByte(5)
      ..write(obj.grossProfit)
      ..writeByte(6)
      ..write(obj.profitMargin)
      ..writeByte(7)
      ..write(obj.averageOrderValue)
      ..writeByte(8)
      ..write(obj.customerLifetimeValue)
      ..writeByte(9)
      ..write(obj.firstOrderDate)
      ..writeByte(10)
      ..write(obj.lastOrderDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerProfitabilityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
