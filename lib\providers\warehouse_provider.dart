import 'package:flutter/material.dart';
import '../models/warehouse.dart';
import '../models/product_batch.dart';
import '../models/stock_movement.dart';
import '../services/warehouse_service.dart';
import '../services/batch_service.dart';
import '../services/offline_notification_service.dart';

class WarehouseProvider with ChangeNotifier {
  List<Warehouse> _warehouses = [];
  List<ProductWarehouseStock> _warehouseStock = [];
  List<ProductBatch> _batches = [];
  List<StockMovement> _stockMovements = [];
  bool _isLoading = false;
  String _error = '';

  WarehouseProvider();

  // Getters
  List<Warehouse> get warehouses => _warehouses;
  List<ProductWarehouseStock> get warehouseStock => _warehouseStock;
  List<ProductBatch> get batches => _batches;
  List<StockMovement> get stockMovements => _stockMovements;
  bool get isLoading => _isLoading;
  String get error => _error;

  /// تحميل جميع المستودعات
  Future<void> fetchWarehouses() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _warehouses = await WarehouseService.getAllWarehouses();
      debugPrint('✅ تم تحميل ${_warehouses.length} مستودع');
    } catch (e) {
      _error = 'فشل في تحميل المستودعات';
      debugPrint('❌ فشل في تحميل المستودعات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل مخزون مستودع
  Future<void> fetchWarehouseStock(int warehouseId) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _warehouseStock = await WarehouseService.getWarehouseStock(warehouseId);
      debugPrint('✅ تم تحميل مخزون المستودع $warehouseId');
    } catch (e) {
      _error = 'فشل في تحميل مخزون المستودع';
      debugPrint('❌ فشل في تحميل مخزون المستودع: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل جميع الدفعات
  Future<void> fetchBatches() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _batches = await BatchService.getAllBatches();
      debugPrint('✅ تم تحميل ${_batches.length} دفعة');
    } catch (e) {
      _error = 'فشل في تحميل الدفعات';
      debugPrint('❌ فشل في تحميل الدفعات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل حركات المخزون
  Future<void> fetchStockMovements({
    int? productId,
    int? warehouseId,
    StockMovementType? type,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _stockMovements = await WarehouseService.getStockMovements(
        productId: productId,
        warehouseId: warehouseId,
        type: type,
        fromDate: fromDate,
        toDate: toDate,
      );
      debugPrint('✅ تم تحميل ${_stockMovements.length} حركة مخزون');
    } catch (e) {
      _error = 'فشل في تحميل حركات المخزون';
      debugPrint('❌ فشل في تحميل حركات المخزون: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إضافة مستودع جديد
  Future<Warehouse> createWarehouse(Warehouse warehouse) async {
    try {
      final newWarehouse = await WarehouseService.addWarehouse(warehouse);
      _warehouses.add(newWarehouse);

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة المستودع "${newWarehouse.name}" بنجاح',
      );

      notifyListeners();
      debugPrint('✅ تم إنشاء المستودع: ${newWarehouse.name}');
      return newWarehouse;
    } catch (e) {
      _error = 'فشل في إنشاء المستودع';
      await OfflineNotificationService.showErrorNotification(
        'فشل في إنشاء المستودع: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// تحديث مستودع
  Future<void> updateWarehouse(Warehouse warehouse) async {
    try {
      final updatedWarehouse = await WarehouseService.updateWarehouse(warehouse);
      final index = _warehouses.indexWhere((w) => w.id == warehouse.id);
      if (index != -1) {
        _warehouses[index] = updatedWarehouse;
      }

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث المستودع "${updatedWarehouse.name}" بنجاح',
      );

      notifyListeners();
      debugPrint('✅ تم تحديث المستودع: ${updatedWarehouse.name}');
    } catch (e) {
      _error = 'فشل في تحديث المستودع';
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث المستودع: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// حذف مستودع
  Future<void> deleteWarehouse(int warehouseId) async {
    try {
      final warehouseToDelete = _warehouses.firstWhere((w) => w.id == warehouseId);
      
      await WarehouseService.deleteWarehouse(warehouseId);
      _warehouses.removeWhere((w) => w.id == warehouseId);

      await OfflineNotificationService.showSuccessNotification(
        'تم حذف المستودع "${warehouseToDelete.name}" بنجاح',
      );

      notifyListeners();
      debugPrint('✅ تم حذف المستودع: ${warehouseToDelete.name}');
    } catch (e) {
      _error = 'فشل في حذف المستودع';
      await OfflineNotificationService.showErrorNotification(
        'فشل في حذف المستودع: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// نقل مخزون بين المستودعات
  Future<void> transferStock({
    required int productId,
    required int fromWarehouseId,
    required int toWarehouseId,
    required int quantity,
    String? notes,
    String? batchNumber,
    DateTime? expiryDate,
  }) async {
    try {
      await WarehouseService.transferStock(
        productId: productId,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        quantity: quantity,
        notes: notes,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
      );

      // تحديث البيانات المحلية
      await fetchWarehouseStock(fromWarehouseId);
      await fetchWarehouseStock(toWarehouseId);
      await fetchStockMovements();

      debugPrint('✅ تم نقل المخزون بنجاح');
    } catch (e) {
      _error = 'فشل في نقل المخزون';
      notifyListeners();
      rethrow;
    }
  }

  /// إضافة دفعة جديدة
  Future<ProductBatch> createBatch(ProductBatch batch) async {
    try {
      final newBatch = await BatchService.addBatch(batch);
      _batches.add(newBatch);

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة الدفعة "${newBatch.batchNumber}" بنجاح',
      );

      notifyListeners();
      debugPrint('✅ تم إنشاء الدفعة: ${newBatch.batchNumber}');
      return newBatch;
    } catch (e) {
      _error = 'فشل في إنشاء الدفعة';
      await OfflineNotificationService.showErrorNotification(
        'فشل في إنشاء الدفعة: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// تحديث دفعة
  Future<void> updateBatch(ProductBatch batch) async {
    try {
      final updatedBatch = await BatchService.updateBatch(batch);
      final index = _batches.indexWhere((b) => b.id == batch.id);
      if (index != -1) {
        _batches[index] = updatedBatch;
      }

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث الدفعة "${updatedBatch.batchNumber}" بنجاح',
      );

      notifyListeners();
      debugPrint('✅ تم تحديث الدفعة: ${updatedBatch.batchNumber}');
    } catch (e) {
      _error = 'فشل في تحديث الدفعة';
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث الدفعة: ${e.toString()}',
      );
      notifyListeners();
      rethrow;
    }
  }

  /// بيع من دفعة
  Future<void> sellFromBatch(
    String batchNumber,
    int quantity, {
    String? reference,
    String? notes,
  }) async {
    try {
      final updatedBatch = await BatchService.sellFromBatch(
        batchNumber,
        quantity,
        reference: reference,
        notes: notes,
      );

      final index = _batches.indexWhere((b) => b.batchNumber == batchNumber);
      if (index != -1) {
        _batches[index] = updatedBatch;
      }

      notifyListeners();
      debugPrint('✅ تم البيع من الدفعة: $batchNumber');
    } catch (e) {
      _error = 'فشل في البيع من الدفعة';
      notifyListeners();
      rethrow;
    }
  }

  /// الحصول على المستودع الرئيسي
  Warehouse? get mainWarehouse {
    try {
      return _warehouses.firstWhere((w) => w.isMainWarehouse);
    } catch (e) {
      return _warehouses.isNotEmpty ? _warehouses.first : null;
    }
  }

  /// الحصول على المستودعات النشطة
  List<Warehouse> get activeWarehouses {
    return _warehouses.where((w) => w.isActive).toList();
  }

  /// الحصول على الدفعات المنتهية الصلاحية
  List<ProductBatch> get expiredBatches {
    return _batches.where((b) => b.isExpired).toList();
  }

  /// الحصول على الدفعات التي ستنتهي صلاحيتها قريباً
  List<ProductBatch> getExpiringBatches(int daysAhead) {
    return _batches.where((b) => b.isExpiringWithin(daysAhead)).toList();
  }

  /// الحصول على دفعات منتج
  List<ProductBatch> getProductBatches(int productId) {
    return _batches.where((b) => b.productId == productId).toList();
  }

  /// الحصول على مخزون منتج في مستودع
  ProductWarehouseStock? getProductStock(int productId, int warehouseId) {
    try {
      return _warehouseStock.firstWhere(
        (stock) => stock.productId == productId && stock.warehouseId == warehouseId,
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على إجمالي مخزون منتج
  int getTotalProductStock(int productId) {
    return _warehouseStock
        .where((stock) => stock.productId == productId)
        .fold(0, (total, stock) => total + stock.availableQuantity);
  }

  /// إحصائيات المستودعات
  Map<String, dynamic> getWarehousesStatistics() {
    final activeWarehouses = _warehouses.where((w) => w.isActive).length;
    final totalProducts = _warehouseStock.length;
    final totalQuantity = _warehouseStock.fold(0, (sum, stock) => sum + stock.quantity);
    final availableQuantity = _warehouseStock.fold(0, (sum, stock) => sum + stock.availableQuantity);
    final reservedQuantity = _warehouseStock.fold(0, (sum, stock) => sum + stock.reservedQuantity);
    
    final lowStockCount = _warehouseStock.where((stock) => stock.isLowStock()).length;
    final outOfStockCount = _warehouseStock.where((stock) => stock.isOutOfStock()).length;
    
    final activeBatches = _batches.where((b) => b.status == BatchStatus.active).length;
    final expiredBatchesCount = _batches.where((b) => b.isExpired).length;
    final expiringBatchesCount = _batches.where((b) => b.isExpiringWithin(30)).length;

    return {
      'total_warehouses': _warehouses.length,
      'active_warehouses': activeWarehouses,
      'total_products': totalProducts,
      'total_quantity': totalQuantity,
      'available_quantity': availableQuantity,
      'reserved_quantity': reservedQuantity,
      'low_stock_count': lowStockCount,
      'out_of_stock_count': outOfStockCount,
      'total_batches': _batches.length,
      'active_batches': activeBatches,
      'expired_batches': expiredBatchesCount,
      'expiring_batches': expiringBatchesCount,
    };
  }
}
