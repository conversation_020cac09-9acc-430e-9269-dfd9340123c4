class Customer {
  final int id;
  final String name;
  final String phone;
  final String email;
  final String address;
  final double totalDebt;
  final DateTime createdAt;

  /// الرصيد (balance) - يمكن أن يكون موجب أو سالب
  double get balance => -totalDebt;

  Customer({
    required this.id,
    required this.name,
    required this.phone,
    this.email = '',
    required this.address,
    required this.totalDebt,
    required this.createdAt,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'],
      name: json['name'],
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      address: json['address'] ?? '',
      totalDebt: json['total_debt'].toDouble(),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'total_debt': totalDebt,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  Customer copyWith({
    int? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    double? totalDebt,
    DateTime? createdAt,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      totalDebt: totalDebt ?? this.totalDebt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// تحديث الدين
  Customer updateDebt(double newDebt) {
    return copyWith(totalDebt: newDebt);
  }

  /// إضافة دين جديد
  Customer addDebt(double amount) {
    return copyWith(totalDebt: totalDebt + amount);
  }

  /// دفع دين
  Customer payDebt(double amount) {
    final newDebt = (totalDebt - amount).clamp(0.0, double.infinity);
    return copyWith(totalDebt: newDebt);
  }

  /// فحص إذا كان العميل لديه ديون
  bool hasDebt() {
    return totalDebt > 0;
  }

  /// الحصول على نص وصفي للدين
  String getDebtDescription() {
    if (totalDebt <= 0) {
      return 'لا يوجد ديون';
    }
    return '${totalDebt.toStringAsFixed(0)} ل.س';
  }
}
