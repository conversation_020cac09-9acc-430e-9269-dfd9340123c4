import 'package:flutter/foundation.dart';
import '../models/company.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';
import '../services/offline_storage_service.dart';

/// خدمة إدارة الشركات
class CompanyService {
  static const String _companiesBox = 'companies';
  static const String _activeCompanyKey = 'active_company_id';

  /// تهيئة خدمة الشركات
  static Future<void> initialize() async {
    try {
      // فحص إذا كانت هناك شركات موجودة
      final companies = await getAllCompanies();
      
      if (companies.isEmpty) {
        // إنشاء شركة افتراضية
        await _createDefaultCompany();
        debugPrint('✅ تم إنشاء الشركة الافتراضية');
      }
      
      // التأكد من وجود شركة نشطة
      final activeCompany = await getActiveCompany();
      if (activeCompany == null && companies.isNotEmpty) {
        await setActiveCompany(companies.first.id);
      }
      
      debugPrint('✅ تم تهيئة خدمة الشركات');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة الشركات: $e');
      rethrow;
    }
  }

  /// الحصول على جميع الشركات
  static Future<List<Company>> getAllCompanies() async {
    try {
      final companiesData = await OfflineDatabaseService.getBoxData(_companiesBox);
      final companies = <Company>[];
      
      for (var companyMap in companiesData.values) {
        companies.add(Company.fromJson(Map<String, dynamic>.from(companyMap)));
      }
      
      // ترتيب حسب الاسم
      companies.sort((a, b) => a.name.compareTo(b.name));
      
      return companies;
    } catch (e) {
      debugPrint('❌ فشل في جلب الشركات: $e');
      return [];
    }
  }

  /// الحصول على شركة بالمعرف
  static Future<Company?> getCompanyById(int id) async {
    try {
      final companyData = await OfflineDatabaseService.getBoxItem(_companiesBox, id.toString());
      if (companyData != null) {
        return Company.fromJson(Map<String, dynamic>.from(companyData));
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب الشركة: $e');
      return null;
    }
  }

  /// الحصول على الشركة النشطة
  static Future<Company?> getActiveCompany() async {
    try {
      final activeCompanyId = OfflineStorageService.getSetting<int>(_activeCompanyKey, 0);
      if (activeCompanyId != null && activeCompanyId > 0) {
        return await getCompanyById(activeCompanyId);
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب الشركة النشطة: $e');
      return null;
    }
  }

  /// تعيين الشركة النشطة
  static Future<void> setActiveCompany(int companyId) async {
    try {
      final company = await getCompanyById(companyId);
      if (company == null) {
        throw Exception('الشركة غير موجودة');
      }

      if (!company.isActive) {
        throw Exception('لا يمكن تفعيل شركة غير نشطة');
      }

      await OfflineStorageService.saveSetting(_activeCompanyKey, companyId);

      await OfflineNotificationService.showSuccessNotification(
        'تم تبديل الشركة النشطة إلى "${company.name}"',
      );

      debugPrint('✅ تم تعيين الشركة النشطة: ${company.name}');
    } catch (e) {
      debugPrint('❌ فشل في تعيين الشركة النشطة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تبديل الشركة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// إضافة شركة جديدة
  static Future<Company> addCompany(Company company) async {
    try {
      // إنشاء معرف جديد
      final newId = await _getNextCompanyId();
      final newCompany = company.copyWith(
        id: newId,
        createdAt: DateTime.now(),
      );

      // حفظ الشركة
      await OfflineDatabaseService.saveBoxItem(
        _companiesBox,
        newId.toString(),
        newCompany.toJson(),
      );

      // إذا كانت هذه الشركة الأولى، اجعلها نشطة
      final companies = await getAllCompanies();
      if (companies.length == 1) {
        await setActiveCompany(newId);
      }

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة الشركة "${newCompany.name}" بنجاح',
      );

      debugPrint('✅ تم إضافة الشركة: ${newCompany.name}');
      return newCompany;
    } catch (e) {
      debugPrint('❌ فشل في إضافة الشركة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة الشركة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث شركة
  static Future<Company> updateCompany(Company company) async {
    try {
      final updatedCompany = company.copyWith(updatedAt: DateTime.now());

      await OfflineDatabaseService.saveBoxItem(
        _companiesBox,
        company.id.toString(),
        updatedCompany.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث الشركة "${updatedCompany.name}" بنجاح',
      );

      debugPrint('✅ تم تحديث الشركة: ${updatedCompany.name}');
      return updatedCompany;
    } catch (e) {
      debugPrint('❌ فشل في تحديث الشركة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث الشركة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// حذف شركة
  static Future<void> deleteCompany(int companyId) async {
    try {
      final company = await getCompanyById(companyId);
      if (company == null) {
        throw Exception('الشركة غير موجودة');
      }

      // فحص إذا كانت الشركة النشطة
      final activeCompany = await getActiveCompany();
      if (activeCompany?.id == companyId) {
        // البحث عن شركة أخرى لتفعيلها
        final companies = await getAllCompanies();
        final otherCompanies = companies.where((c) => c.id != companyId && c.isActive).toList();
        
        if (otherCompanies.isNotEmpty) {
          await setActiveCompany(otherCompanies.first.id);
        } else {
          await OfflineStorageService.saveSetting(_activeCompanyKey, 0);
        }
      }

      await OfflineDatabaseService.deleteBoxItem(_companiesBox, companyId.toString());

      await OfflineNotificationService.showSuccessNotification(
        'تم حذف الشركة "${company.name}" بنجاح',
      );

      debugPrint('✅ تم حذف الشركة: ${company.name}');
    } catch (e) {
      debugPrint('❌ فشل في حذف الشركة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في حذف الشركة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تغيير حالة الشركة
  static Future<Company> changeCompanyStatus(int companyId, CompanyStatus newStatus) async {
    try {
      final company = await getCompanyById(companyId);
      if (company == null) {
        throw Exception('الشركة غير موجودة');
      }

      final updatedCompany = company.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await OfflineDatabaseService.saveBoxItem(
        _companiesBox,
        companyId.toString(),
        updatedCompany.toJson(),
      );

      // إذا تم إلغاء تفعيل الشركة النشطة، ابحث عن شركة أخرى
      if (newStatus != CompanyStatus.active) {
        final activeCompany = await getActiveCompany();
        if (activeCompany?.id == companyId) {
          final companies = await getAllCompanies();
          final activeCompanies = companies.where((c) => c.id != companyId && c.isActive).toList();
          
          if (activeCompanies.isNotEmpty) {
            await setActiveCompany(activeCompanies.first.id);
          } else {
            await OfflineStorageService.saveSetting(_activeCompanyKey, 0);
          }
        }
      }

      await OfflineNotificationService.showSuccessNotification(
        'تم تغيير حالة الشركة "${updatedCompany.name}" إلى "${updatedCompany.statusDisplayName}"',
      );

      debugPrint('✅ تم تغيير حالة الشركة: ${updatedCompany.name}');
      return updatedCompany;
    } catch (e) {
      debugPrint('❌ فشل في تغيير حالة الشركة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تغيير حالة الشركة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// إضافة حساب بنكي لشركة
  static Future<Company> addBankAccount(int companyId, BankAccount bankAccount) async {
    try {
      final company = await getCompanyById(companyId);
      if (company == null) {
        throw Exception('الشركة غير موجودة');
      }

      final newBankAccounts = List<BankAccount>.from(company.bankAccounts);
      
      // إنشاء معرف جديد للحساب البنكي
      final newId = newBankAccounts.isEmpty ? 1 : 
          newBankAccounts.map((a) => a.id).reduce((a, b) => a > b ? a : b) + 1;
      
      final newBankAccount = BankAccount(
        id: newId,
        bankName: bankAccount.bankName,
        accountNumber: bankAccount.accountNumber,
        accountName: bankAccount.accountName,
        iban: bankAccount.iban,
        swiftCode: bankAccount.swiftCode,
        currency: bankAccount.currency,
        balance: bankAccount.balance,
        isActive: bankAccount.isActive,
        isDefault: bankAccount.isDefault,
        createdAt: DateTime.now(),
      );

      // إذا كان الحساب افتراضي، إلغاء الافتراضية من الحسابات الأخرى
      if (newBankAccount.isDefault) {
        for (int i = 0; i < newBankAccounts.length; i++) {
          newBankAccounts[i] = BankAccount(
            id: newBankAccounts[i].id,
            bankName: newBankAccounts[i].bankName,
            accountNumber: newBankAccounts[i].accountNumber,
            accountName: newBankAccounts[i].accountName,
            iban: newBankAccounts[i].iban,
            swiftCode: newBankAccounts[i].swiftCode,
            currency: newBankAccounts[i].currency,
            balance: newBankAccounts[i].balance,
            isActive: newBankAccounts[i].isActive,
            isDefault: false,
            createdAt: newBankAccounts[i].createdAt,
          );
        }
      }

      newBankAccounts.add(newBankAccount);

      final updatedCompany = company.copyWith(
        bankAccounts: newBankAccounts,
        updatedAt: DateTime.now(),
      );

      await OfflineDatabaseService.saveBoxItem(
        _companiesBox,
        companyId.toString(),
        updatedCompany.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة الحساب البنكي "${newBankAccount.accountName}" بنجاح',
      );

      debugPrint('✅ تم إضافة حساب بنكي للشركة: ${company.name}');
      return updatedCompany;
    } catch (e) {
      debugPrint('❌ فشل في إضافة الحساب البنكي: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة الحساب البنكي: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// الحصول على الشركات النشطة فقط
  static Future<List<Company>> getActiveCompanies() async {
    final companies = await getAllCompanies();
    return companies.where((company) => company.isActive).toList();
  }

  /// البحث في الشركات
  static Future<List<Company>> searchCompanies(String query) async {
    final companies = await getAllCompanies();
    
    if (query.isEmpty) return companies;
    
    return companies.where((company) =>
        company.name.toLowerCase().contains(query.toLowerCase()) ||
        company.nameEn.toLowerCase().contains(query.toLowerCase()) ||
        company.taxId.contains(query) ||
        company.commercialRegister.contains(query)).toList();
  }

  /// إحصائيات الشركات
  static Future<Map<String, dynamic>> getCompaniesStatistics() async {
    try {
      final companies = await getAllCompanies();
      
      final activeCount = companies.where((c) => c.status == CompanyStatus.active).length;
      final inactiveCount = companies.where((c) => c.status == CompanyStatus.inactive).length;
      final suspendedCount = companies.where((c) => c.status == CompanyStatus.suspended).length;
      final closedCount = companies.where((c) => c.status == CompanyStatus.closed).length;
      
      final typeStats = <String, int>{};
      for (var company in companies) {
        final typeName = company.typeDisplayName;
        typeStats[typeName] = (typeStats[typeName] ?? 0) + 1;
      }
      
      return {
        'total_companies': companies.length,
        'active_companies': activeCount,
        'inactive_companies': inactiveCount,
        'suspended_companies': suspendedCount,
        'closed_companies': closedCount,
        'type_statistics': typeStats,
        'currencies_used': companies.map((c) => c.baseCurrency).toSet().toList(),
      };
    } catch (e) {
      debugPrint('❌ فشل في جلب إحصائيات الشركات: $e');
      return {};
    }
  }

  // ==================== وظائف مساعدة ====================

  /// إنشاء شركة افتراضية
  static Future<void> _createDefaultCompany() async {
    final now = DateTime.now();
    final defaultCompany = Company(
      id: 1,
      name: 'شركتي',
      nameEn: 'My Company',
      address: 'العنوان',
      city: 'المدينة',
      country: 'سوريا',
      baseCurrency: 'SYP',
      type: CompanyType.limited,
      status: CompanyStatus.active,
      fiscalYearStart: DateTime(now.year, 1, 1),
      fiscalYearEnd: DateTime(now.year, 12, 31),
      createdAt: now,
      settings: const CompanySettings(),
      bankAccounts: [],
    );

    await OfflineDatabaseService.saveBoxItem(
      _companiesBox,
      '1',
      defaultCompany.toJson(),
    );

    await setActiveCompany(1);
  }

  /// الحصول على معرف شركة جديد
  static Future<int> _getNextCompanyId() async {
    final companies = await getAllCompanies();
    if (companies.isEmpty) return 1;
    
    final maxId = companies.map((c) => c.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }
}
