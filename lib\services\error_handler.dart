import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

/// خدمة معالجة الأخطاء المحسنة
class ErrorHandler {
  static const String _logTag = 'ErrorHandler';

  /// معالجة الأخطاء العامة
  static void handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    bool showToUser = true,
    BuildContext? buildContext,
  }) {
    _logError(error, stackTrace, context);

    if (showToUser && buildContext != null) {
      _showErrorToUser(buildContext, error);
    }
  }

  /// معالجة أخطاء الشبكة
  static void handleNetworkError(
    dynamic error, {
    BuildContext? context,
    String? customMessage,
  }) {
    String message = customMessage ?? _getNetworkErrorMessage(error);

    _logError(error, null, 'Network Error');

    if (context != null) {
      _showErrorSnackBar(context, message, isNetworkError: true);
    }
  }

  /// معالجة أخطاء قاعدة البيانات
  static void handleDatabaseError(
    dynamic error, {
    BuildContext? context,
    String? operation,
  }) {
    String message = 'حدث خطأ في قاعدة البيانات';
    if (operation != null) {
      message += ' أثناء $operation';
    }

    _logError(error, null, 'Database Error: $operation');

    if (context != null) {
      _showErrorSnackBar(context, message, isDatabaseError: true);
    }
  }

  /// تسجيل الخطأ
  static void _logError(
    dynamic error,
    StackTrace? stackTrace,
    String? context,
  ) {
    if (kDebugMode) {
      debugPrint('[$_logTag] ${context ?? 'Error'}: $error');
      if (stackTrace != null) {
        debugPrint('StackTrace: $stackTrace');
      }
    }
  }

  /// عرض الخطأ للمستخدم
  static void _showErrorToUser(BuildContext context, dynamic error) {
    String message = _getUserFriendlyMessage(error);
    _showErrorSnackBar(context, message);
  }

  /// عرض رسالة خطأ في SnackBar
  static void _showErrorSnackBar(
    BuildContext context,
    String message, {
    bool isNetworkError = false,
    bool isDatabaseError = false,
  }) {
    Color backgroundColor = Colors.red;
    IconData icon = Icons.error;

    if (isNetworkError) {
      backgroundColor = Colors.orange;
      icon = Icons.wifi_off;
    } else if (isDatabaseError) {
      backgroundColor = Colors.purple;
      icon = Icons.storage;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// الحصول على رسالة مفهومة للمستخدم
  static String _getUserFriendlyMessage(dynamic error) {
    if (error is SocketException) {
      return 'لا يوجد اتصال بالإنترنت';
    } else if (error is FormatException) {
      return 'خطأ في تنسيق البيانات';
    } else if (error is String) {
      return error;
    } else {
      return 'حدث خطأ غير متوقع';
    }
  }

  /// الحصول على رسالة خطأ الشبكة
  static String _getNetworkErrorMessage(dynamic error) {
    if (error is SocketException) {
      return 'لا يوجد اتصال بالإنترنت';
    } else if (error is HttpException) {
      return 'خطأ في الخادم';
    } else {
      return 'خطأ في الاتصال';
    }
  }
}

/// استثناءات مخصصة للتطبيق
class AppException implements Exception {
  final String message;
  final String? code;

  AppException(this.message, {this.code});

  @override
  String toString() => message;
}

class NetworkException extends AppException {
  NetworkException(super.message, {super.code});
}

class DatabaseException extends AppException {
  DatabaseException(super.message, {super.code});
}
