import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/currency_provider.dart';

class UpdateDollarRateDialog extends StatefulWidget {
  const UpdateDollarRateDialog({super.key});

  @override
  State<UpdateDollarRateDialog> createState() => _UpdateDollarRateDialogState();
}

class _UpdateDollarRateDialogState extends State<UpdateDollarRateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _rateController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // تعبئة الحقل بالسعر الحالي
    final currentRate = context.read<CurrencyProvider>().dollarRate;
    _rateController.text = currentRate.toStringAsFixed(0);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.currency_exchange, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          const Text('تحديث سعر صرف الدولار'),
        ],
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سيتم تحديث أسعار جميع المنتجات تلقائياً بناءً على السعر الجديد',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.orange[700],
                  ),
            ),
            const SizedBox(height: 16),
            Consumer<CurrencyProvider>(
              builder: (context, currencyProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'السعر الحالي: ${currencyProvider.formatSyp(currencyProvider.dollarRate)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _rateController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      decoration: InputDecoration(
                        labelText: 'السعر الجديد',
                        hintText: 'مثال: 15000',
                        prefixIcon: const Icon(Icons.attach_money),
                        suffixText: 'ل.س',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال سعر الصرف';
                        }
                        final rate = double.tryParse(value);
                        if (rate == null) {
                          return 'الرجاء إدخال رقم صحيح';
                        }
                        if (rate <= 0) {
                          return 'يجب أن يكون السعر أكبر من الصفر';
                        }
                        if (rate < 1000 || rate > 100000) {
                          return 'السعر غير منطقي (1000 - 100000)';
                        }
                        return null;
                      },
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم إعادة حساب أسعار البيع والشراء لجميع المنتجات',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateRate,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('تحديث'),
        ),
      ],
    );
  }

  Future<void> _updateRate() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newRate = double.parse(_rateController.text);
      final currencyProvider = context.read<CurrencyProvider>();

      await currencyProvider.updateDollarRate(newRate);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Expanded(
                  child: Text('تم تحديث سعر الصرف وأسعار المنتجات بنجاح'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _rateController.dispose();
    super.dispose();
  }
}
