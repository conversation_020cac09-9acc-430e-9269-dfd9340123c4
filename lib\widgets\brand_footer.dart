import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class BrandFooter extends StatelessWidget {
  final bool showOnBottom;
  final Color? textColor;
  final double? fontSize;

  const BrandFooter({
    super.key,
    this.showOnBottom = true,
    this.textColor,
    this.fontSize = 12,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ??
        (theme.brightness == Brightness.dark
            ? Colors.white70
            : Colors.grey[600]);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.copyright,
            size: fontSize! + 2,
            color: effectiveTextColor,
          ),
          const SizedBox(width: 4),
          Text(
            'Ali Ahmad Nouh',
            style: GoogleFonts.cairo(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: effectiveTextColor,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(width: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
            decoration: BoxDecoration(
              border: Border.all(
                color: effectiveTextColor!,
                width: 0.8,
              ),
              borderRadius: BorderRadius.circular(3),
            ),
            child: Text(
              '®',
              style: GoogleFonts.cairo(
                fontSize: fontSize! - 2,
                fontWeight: FontWeight.bold,
                color: effectiveTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget للعلامة التجارية في الـ Drawer
class DrawerBrandFooter extends StatelessWidget {
  const DrawerBrandFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: const BrandFooter(
        showOnBottom: false,
        fontSize: 14,
      ),
    );
  }
}

/// Widget للعلامة التجارية في شاشة تسجيل الدخول
class LoginBrandFooter extends StatelessWidget {
  const LoginBrandFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 20,
      left: 0,
      right: 0,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const BrandFooter(
              showOnBottom: false,
              textColor: Colors.black87,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget للعلامة التجارية في أسفل الشاشات
class BottomBrandFooter extends StatelessWidget {
  const BottomBrandFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: const BrandFooter(),
    );
  }
}
