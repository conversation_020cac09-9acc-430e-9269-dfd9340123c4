import 'invoice_item.dart';

enum InvoiceStatus {
  pending,
  paid,
  partial,
  overdue,
  cancelled,
  returned,
}

enum PaymentType {
  cash,
  debt,
  partial,
}

class Invoice {
  final int id;
  final int customerId;
  final String customerName;
  final DateTime date;
  final List<InvoiceItem> items;
  final double totalAmountSyp;
  final double totalAmountUsd;
  final double paidAmountSyp;
  final double remainingAmountSyp;
  final InvoiceStatus status;
  final PaymentType paymentType;
  final String notes;
  final DateTime? returnedDate;
  final String? returnReason;
  final bool isReturned;

  Invoice({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.date,
    required this.items,
    required this.totalAmountSyp,
    required this.totalAmountUsd,
    required this.paidAmountSyp,
    required this.remainingAmountSyp,
    required this.status,
    required this.paymentType,
    this.notes = '',
    this.returnedDate,
    this.returnReason,
    this.isReturned = false,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'] ?? 0,
      customerId: json['customer_id'] ?? 0,
      customerName: json['customer_name'] ?? '',
      date: DateTime.parse(json['date']),
      items: (json['items'] as List)
          .map((item) => InvoiceItem.fromJson(item))
          .toList(),
      totalAmountSyp: (json['total_amount_syp'] ?? 0).toDouble(),
      totalAmountUsd: (json['total_amount_usd'] ?? 0).toDouble(),
      paidAmountSyp: (json['paid_amount_syp'] ?? 0).toDouble(),
      remainingAmountSyp: (json['remaining_amount_syp'] ?? 0).toDouble(),
      status: InvoiceStatus.values.firstWhere(
        (e) => e.toString() == 'InvoiceStatus.${json['status']}',
        orElse: () => InvoiceStatus.pending,
      ),
      paymentType: PaymentType.values.firstWhere(
        (e) => e.toString() == 'PaymentType.${json['payment_type']}',
        orElse: () => PaymentType.cash,
      ),
      notes: json['notes'] ?? '',
      returnedDate: json['returned_date'] != null
          ? DateTime.parse(json['returned_date'])
          : null,
      returnReason: json['return_reason'],
      isReturned: json['is_returned'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'date': date.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'total_amount_syp': totalAmountSyp,
      'total_amount_usd': totalAmountUsd,
      'paid_amount_syp': paidAmountSyp,
      'remaining_amount_syp': remainingAmountSyp,
      'status': status.toString().split('.').last,
      'payment_type': paymentType.toString().split('.').last,
      'notes': notes,
      'returned_date': returnedDate?.toIso8601String(),
      'return_reason': returnReason,
      'is_returned': isReturned,
    };
  }

  Invoice copyWith({
    int? id,
    int? customerId,
    String? customerName,
    DateTime? date,
    List<InvoiceItem>? items,
    double? totalAmountSyp,
    double? totalAmountUsd,
    double? paidAmountSyp,
    double? remainingAmountSyp,
    InvoiceStatus? status,
    PaymentType? paymentType,
    String? notes,
    DateTime? returnedDate,
    String? returnReason,
    bool? isReturned,
  }) {
    return Invoice(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      date: date ?? this.date,
      items: items ?? this.items,
      totalAmountSyp: totalAmountSyp ?? this.totalAmountSyp,
      totalAmountUsd: totalAmountUsd ?? this.totalAmountUsd,
      paidAmountSyp: paidAmountSyp ?? this.paidAmountSyp,
      remainingAmountSyp: remainingAmountSyp ?? this.remainingAmountSyp,
      status: status ?? this.status,
      paymentType: paymentType ?? this.paymentType,
      notes: notes ?? this.notes,
      returnedDate: returnedDate ?? this.returnedDate,
      returnReason: returnReason ?? this.returnReason,
      isReturned: isReturned ?? this.isReturned,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Invoice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Invoice(id: $id, customerName: $customerName, totalAmountSyp: $totalAmountSyp, status: $status)';
  }
}
