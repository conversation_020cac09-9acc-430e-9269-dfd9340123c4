import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

/// خدمة السحابة للمزامنة والنسخ الاحتياطي
class CloudService {
  static const String _baseUrl = 'https://api.invoicesystem.com/v1';
  static String? _apiKey;
  static String? _userId;
  static bool _isEnabled = false;

  /// تهيئة خدمة السحابة
  static Future<void> initialize({
    required String apiKey,
    required String userId,
  }) async {
    try {
      _apiKey = apiKey;
      _userId = userId;
      _isEnabled = true;

      // اختبار الاتصال
      final isConnected = await testConnection();
      if (isConnected) {
        debugPrint('✅ تم تهيئة خدمة السحابة بنجاح');

        // بدء المزامنة التلقائية
        _startAutoSync();
      } else {
        debugPrint('❌ فشل في الاتصال بالخدمة السحابية');
        _isEnabled = false;
      }
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة السحابة: $e');
      _isEnabled = false;
      rethrow;
    }
  }

  /// اختبار الاتصال بالخدمة السحابية
  static Future<bool> testConnection() async {
    try {
      if (_apiKey == null || _userId == null) return false;

      final response = await http.get(
        Uri.parse('$_baseUrl/ping'),
        headers: _getHeaders(),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ فشل في اختبار الاتصال: $e');
      return false;
    }
  }

  // ==================== المزامنة ====================

  /// مزامنة جميع البيانات
  static Future<bool> syncAllData() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('بدء مزامنة جميع البيانات...');

      final results = await Future.wait([
        syncProducts(),
        syncCustomers(),
        syncInvoices(),
        syncWarehouses(),
      ]);

      final success = results.every((result) => result);

      if (success) {
        await OfflineNotificationService.showSuccessNotification(
          'تم مزامنة جميع البيانات مع السحابة بنجاح',
        );
        debugPrint('✅ تم مزامنة جميع البيانات بنجاح');
      } else {
        await OfflineNotificationService.showErrorNotification(
          'فشل في مزامنة بعض البيانات مع السحابة',
        );
        debugPrint('❌ فشل في مزامنة بعض البيانات');
      }

      return success;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة البيانات: $e');
      return false;
    }
  }

  /// مزامنة المنتجات
  static Future<bool> syncProducts() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('مزامنة المنتجات...');

      // رفع المنتجات المحلية
      final localProducts = await OfflineDatabaseService.getAllProducts();
      final uploadSuccess = await _uploadData(
          'products', localProducts.map((p) => p.toJson()).toList());

      if (!uploadSuccess) return false;

      // تحميل المنتجات من السحابة
      final cloudProducts = await _downloadData('products');
      if (cloudProducts != null) {
        // دمج البيانات
        await _mergeProducts(cloudProducts);
      }

      debugPrint('✅ تم مزامنة المنتجات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة المنتجات: $e');
      return false;
    }
  }

  /// مزامنة العملاء
  static Future<bool> syncCustomers() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('مزامنة العملاء...');

      // رفع العملاء المحليين
      final localCustomers = await OfflineDatabaseService.getAllCustomers();
      final uploadSuccess = await _uploadData(
          'customers', localCustomers.map((c) => c.toJson()).toList());

      if (!uploadSuccess) return false;

      // تحميل العملاء من السحابة
      final cloudCustomers = await _downloadData('customers');
      if (cloudCustomers != null) {
        // دمج البيانات
        await _mergeCustomers(cloudCustomers);
      }

      debugPrint('✅ تم مزامنة العملاء بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة العملاء: $e');
      return false;
    }
  }

  /// مزامنة الفواتير
  static Future<bool> syncInvoices() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('مزامنة الفواتير...');

      // رفع الفواتير المحلية
      final localInvoices = await OfflineDatabaseService.getAllInvoices();
      final uploadSuccess = await _uploadData(
          'invoices', localInvoices.map((i) => i.toJson()).toList());

      if (!uploadSuccess) return false;

      // تحميل الفواتير من السحابة
      final cloudInvoices = await _downloadData('invoices');
      if (cloudInvoices != null) {
        // دمج البيانات
        await _mergeInvoices(cloudInvoices);
      }

      debugPrint('✅ تم مزامنة الفواتير بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة الفواتير: $e');
      return false;
    }
  }

  /// مزامنة المستودعات
  static Future<bool> syncWarehouses() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('مزامنة المستودعات...');

      // رفع المستودعات المحلية
      final localWarehouses =
          await OfflineDatabaseService.getBoxData('warehouses');
      final warehousesList = localWarehouses.values.toList();
      final uploadSuccess = await _uploadData(
          'warehouses', warehousesList.cast<Map<String, dynamic>>());

      if (!uploadSuccess) return false;

      // تحميل المستودعات من السحابة
      final cloudWarehouses = await _downloadData('warehouses');
      if (cloudWarehouses != null) {
        // دمج البيانات
        await _mergeWarehouses(cloudWarehouses);
      }

      debugPrint('✅ تم مزامنة المستودعات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة المستودعات: $e');
      return false;
    }
  }

  // ==================== النسخ الاحتياطي ====================

  /// إنشاء نسخة احتياطية كاملة
  static Future<bool> createFullBackup() async {
    try {
      if (!_isEnabled) return false;

      debugPrint('إنشاء نسخة احتياطية كاملة...');

      // جمع جميع البيانات
      final backupData = {
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
        'user_id': _userId,
        'products': (await OfflineDatabaseService.getAllProducts())
            .map((p) => p.toJson())
            .toList(),
        'customers': (await OfflineDatabaseService.getAllCustomers())
            .map((c) => c.toJson())
            .toList(),
        'invoices': (await OfflineDatabaseService.getAllInvoices())
            .map((i) => i.toJson())
            .toList(),
        'warehouses': (await OfflineDatabaseService.getBoxData('warehouses'))
            .values
            .toList(),
        'settings': await OfflineDatabaseService.getBoxData('settings'),
      };

      // رفع النسخة الاحتياطية
      final response = await http.post(
        Uri.parse('$_baseUrl/backup'),
        headers: _getHeaders(),
        body: jsonEncode(backupData),
      );

      if (response.statusCode == 200) {
        await OfflineNotificationService.showSuccessNotification(
          'تم إنشاء النسخة الاحتياطية بنجاح',
        );
        debugPrint('✅ تم إنشاء النسخة الاحتياطية بنجاح');
        return true;
      } else {
        throw Exception('فشل في رفع النسخة الاحتياطية: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ فشل في إنشاء النسخة الاحتياطية: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إنشاء النسخة الاحتياطية: ${e.toString()}',
      );
      return false;
    }
  }

  /// استعادة من النسخة الاحتياطية
  static Future<bool> restoreFromBackup(String backupId) async {
    try {
      if (!_isEnabled) return false;

      debugPrint('استعادة من النسخة الاحتياطية: $backupId');

      // تحميل النسخة الاحتياطية
      final response = await http.get(
        Uri.parse('$_baseUrl/backup/$backupId'),
        headers: _getHeaders(),
      );

      if (response.statusCode != 200) {
        throw Exception(
            'فشل في تحميل النسخة الاحتياطية: ${response.statusCode}');
      }

      final backupData = jsonDecode(response.body);

      // استعادة البيانات
      await _restoreData(backupData);

      await OfflineNotificationService.showSuccessNotification(
        'تم استعادة البيانات من النسخة الاحتياطية بنجاح',
      );
      debugPrint('✅ تم استعادة البيانات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في استعادة البيانات: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في استعادة البيانات: ${e.toString()}',
      );
      return false;
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  static Future<List<Map<String, dynamic>>> getBackupList() async {
    try {
      if (!_isEnabled) return [];

      final response = await http.get(
        Uri.parse('$_baseUrl/backups'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['backups'] ?? []);
      } else {
        throw Exception(
            'فشل في جلب قائمة النسخ الاحتياطية: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ فشل في جلب قائمة النسخ الاحتياطية: $e');
      return [];
    }
  }

  // ==================== وظائف مساعدة ====================

  /// الحصول على headers للطلبات
  static Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $_apiKey',
      'X-User-ID': _userId ?? '',
    };
  }

  /// رفع البيانات إلى السحابة
  static Future<bool> _uploadData(
      String endpoint, List<Map<String, dynamic>> data) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/$endpoint/sync'),
        headers: _getHeaders(),
        body: jsonEncode({'data': data}),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ فشل في رفع البيانات: $e');
      return false;
    }
  }

  /// تحميل البيانات من السحابة
  static Future<List<Map<String, dynamic>>?> _downloadData(
      String endpoint) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/$endpoint'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في تحميل البيانات: $e');
      return null;
    }
  }

  /// دمج المنتجات
  static Future<void> _mergeProducts(
      List<Map<String, dynamic>> cloudProducts) async {
    // TODO: تنفيذ منطق الدمج الذكي
    debugPrint('دمج ${cloudProducts.length} منتج من السحابة');
  }

  /// دمج العملاء
  static Future<void> _mergeCustomers(
      List<Map<String, dynamic>> cloudCustomers) async {
    // TODO: تنفيذ منطق الدمج الذكي
    debugPrint('دمج ${cloudCustomers.length} عميل من السحابة');
  }

  /// دمج الفواتير
  static Future<void> _mergeInvoices(
      List<Map<String, dynamic>> cloudInvoices) async {
    // TODO: تنفيذ منطق الدمج الذكي
    debugPrint('دمج ${cloudInvoices.length} فاتورة من السحابة');
  }

  /// دمج المستودعات
  static Future<void> _mergeWarehouses(
      List<Map<String, dynamic>> cloudWarehouses) async {
    // TODO: تنفيذ منطق الدمج الذكي
    debugPrint('دمج ${cloudWarehouses.length} مستودع من السحابة');
  }

  /// استعادة البيانات
  static Future<void> _restoreData(Map<String, dynamic> backupData) async {
    // TODO: تنفيذ استعادة البيانات
    debugPrint('استعادة البيانات من النسخة الاحتياطية');
  }

  /// بدء المزامنة التلقائية
  static void _startAutoSync() {
    // TODO: تنفيذ المزامنة التلقائية كل فترة
    debugPrint('بدء المزامنة التلقائية');
  }

  /// فحص حالة الخدمة
  static bool get isEnabled => _isEnabled;
  static String? get userId => _userId;
}
