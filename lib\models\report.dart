class SalesReport {
  final double totalSalesSyp;
  final double totalSalesUsd;
  final double totalDebtSyp;
  final double totalDebtUsd;
  final List<DailySales> dailySales;
  final List<TopProduct> topProducts;
  final List<CustomerDebt> customerDebts;

  SalesReport({
    required this.totalSalesSyp,
    required this.totalSalesUsd,
    required this.totalDebtSyp,
    required this.totalDebtUsd,
    required this.dailySales,
    required this.topProducts,
    required this.customerDebts,
  });

  factory SalesReport.fromJson(Map<String, dynamic> json) {
    return SalesReport(
      totalSalesSyp: json['total_sales_syp'].toDouble(),
      totalSalesUsd: json['total_sales_usd'].toDouble(),
      totalDebtSyp: json['total_debt_syp'].toDouble(),
      totalDebtUsd: json['total_debt_usd'].toDouble(),
      dailySales: (json['daily_sales'] as List)
          .map((item) => DailySales.fromJson(item))
          .toList(),
      topProducts: (json['top_products'] as List)
          .map((item) => TopProduct.fromJson(item))
          .toList(),
      customerDebts: (json['customer_debts'] as List)
          .map((item) => CustomerDebt.fromJson(item))
          .toList(),
    );
  }
}

class DailySales {
  final DateTime date;
  final double totalSyp;
  final double totalUsd;

  DailySales({
    required this.date,
    required this.totalSyp,
    required this.totalUsd,
  });

  factory DailySales.fromJson(Map<String, dynamic> json) {
    return DailySales(
      date: DateTime.parse(json['date']),
      totalSyp: json['total_syp'].toDouble(),
      totalUsd: json['total_usd'].toDouble(),
    );
  }
}

class TopProduct {
  final int productId;
  final String productName;
  final int quantitySold;
  final double totalSalesSyp;
  final double totalSalesUsd;

  TopProduct({
    required this.productId,
    required this.productName,
    required this.quantitySold,
    required this.totalSalesSyp,
    required this.totalSalesUsd,
  });

  factory TopProduct.fromJson(Map<String, dynamic> json) {
    return TopProduct(
      productId: json['product_id'],
      productName: json['product_name'],
      quantitySold: json['quantity_sold'],
      totalSalesSyp: json['total_sales_syp'].toDouble(),
      totalSalesUsd: json['total_sales_usd'].toDouble(),
    );
  }
}

class CustomerDebt {
  final int customerId;
  final String customerName;
  final double debtAmountSyp;
  final double debtAmountUsd;
  final DateTime lastPurchaseDate;

  CustomerDebt({
    required this.customerId,
    required this.customerName,
    required this.debtAmountSyp,
    required this.debtAmountUsd,
    required this.lastPurchaseDate,
  });

  factory CustomerDebt.fromJson(Map<String, dynamic> json) {
    return CustomerDebt(
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      debtAmountSyp: json['debt_amount_syp'].toDouble(),
      debtAmountUsd: json['debt_amount_usd'].toDouble(),
      lastPurchaseDate: DateTime.parse(json['last_purchase_date']),
    );
  }
}