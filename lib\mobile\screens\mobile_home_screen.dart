import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/invoice_provider.dart';
import '../../providers/warehouse_provider.dart';
import '../mobile_main.dart';

class MobileHomeScreen extends StatefulWidget {
  const MobileHomeScreen({super.key});

  @override
  State<MobileHomeScreen> createState() => _MobileHomeScreenState();
}

class _MobileHomeScreenState extends State<MobileHomeScreen> {
  @override
  void initState() {
    super.initState();
    _refreshData();
  }

  Future<void> _refreshData() async {
    if (mounted) {
      await Future.wait([
        context.read<ProductProvider>().fetchProducts(),
        context.read<CustomerProvider>().fetchCustomers(),
        context.read<InvoiceProvider>().fetchInvoices(),
        context.read<WarehouseProvider>().fetchWarehouses(),
      ]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MobileAppBar(
        title: 'نظام إدارة الفواتير',
        automaticallyImplyLeading: false,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeSection(),
              _buildQuickStats(),
              _buildQuickActions(),
              _buildRecentActivity(),
              const SizedBox(height: 100), // مساحة للـ BottomNavigationBar
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مرحباً بك',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'إدارة أعمالك بسهولة وذكاء',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'اليوم: ${_formatDate(DateTime.now())}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات سريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Consumer4<ProductProvider, CustomerProvider, InvoiceProvider, WarehouseProvider>(
            builder: (context, productProvider, customerProvider, invoiceProvider, warehouseProvider, child) {
              return GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 1.2,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                children: [
                  MobileStatCard(
                    title: 'المنتجات',
                    value: '${productProvider.products.length}',
                    icon: Icons.inventory,
                    color: Colors.blue,
                    onTap: () => Navigator.pushNamed(context, '/products'),
                  ),
                  MobileStatCard(
                    title: 'العملاء',
                    value: '${customerProvider.customers.length}',
                    icon: Icons.people,
                    color: Colors.green,
                    onTap: () => Navigator.pushNamed(context, '/customers'),
                  ),
                  MobileStatCard(
                    title: 'الفواتير',
                    value: '${invoiceProvider.invoices.length}',
                    icon: Icons.receipt,
                    color: Colors.orange,
                    onTap: () => Navigator.pushNamed(context, '/invoices'),
                  ),
                  MobileStatCard(
                    title: 'المخازن',
                    value: '${warehouseProvider.warehouses.length}',
                    icon: Icons.warehouse,
                    color: Colors.purple,
                    onTap: () => Navigator.pushNamed(context, '/warehouses'),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  title: 'فاتورة جديدة',
                  icon: Icons.add_shopping_cart,
                  color: Colors.blue,
                  onTap: () => _createNewInvoice(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  title: 'منتج جديد',
                  icon: Icons.add_box,
                  color: Colors.green,
                  onTap: () => _addNewProduct(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  title: 'عميل جديد',
                  icon: Icons.person_add,
                  color: Colors.orange,
                  onTap: () => _addNewCustomer(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  title: 'التقارير',
                  icon: Icons.analytics,
                  color: Colors.purple,
                  onTap: () => _viewReports(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return MobileCard(
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 32,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'النشاط الأخير',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Consumer<InvoiceProvider>(
            builder: (context, invoiceProvider, child) {
              final recentInvoices = invoiceProvider.invoices
                  .take(5)
                  .toList();

              if (recentInvoices.isEmpty) {
                return MobileCard(
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد فواتير حديثة',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'ابدأ بإنشاء فاتورة جديدة',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return MobileCard(
                child: Column(
                  children: recentInvoices.map((invoice) {
                    return MobileListTile(
                      leading: CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.receipt,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      title: Text('فاتورة #${invoice.id}'),
                      subtitle: Text(
                        '${invoice.customerName} - ${_formatCurrency(invoice.totalAmountSyp)}',
                      ),
                      trailing: Text(
                        _formatDate(invoice.date),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      onTap: () => _viewInvoice(invoice.id),
                    );
                  }).toList(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} ل.س';
  }

  void _createNewInvoice() {
    // TODO: تنفيذ إنشاء فاتورة جديدة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إنشاء فاتورة جديدة ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _addNewProduct() {
    // TODO: تنفيذ إضافة منتج جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إضافة منتج جديد ستكون متاحة قريباً'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _addNewCustomer() {
    // TODO: تنفيذ إضافة عميل جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إضافة عميل جديد ستكون متاحة قريباً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _viewReports() {
    // TODO: تنفيذ عرض التقارير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التقارير ستكون متاحة قريباً'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _viewInvoice(int invoiceId) {
    // TODO: تنفيذ عرض تفاصيل الفاتورة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل الفاتورة #$invoiceId'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
