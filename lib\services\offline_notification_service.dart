import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// خدمة الإشعارات المحلية للعمل بدون إنترنت
class OfflineNotificationService {
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات المحلية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    _localNotifications = FlutterLocalNotificationsPlugin();

    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications!.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
    debugPrint('✅ تم تهيئة خدمة الإشعارات المحلية');
  }

  /// عرض إشعار فوري
  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    if (!_isInitialized) await initialize();

    await _localNotifications!.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      _getNotificationDetails(priority),
      payload: payload,
    );
  }

  /// إشعار نجاح العملية
  static Future<void> showSuccessNotification(String message) async {
    await showNotification(
      title: '✅ نجح',
      body: message,
      priority: NotificationPriority.normal,
    );
  }

  /// إشعار خطأ
  static Future<void> showErrorNotification(String message) async {
    await showNotification(
      title: '❌ خطأ',
      body: message,
      priority: NotificationPriority.high,
    );
  }

  /// إشعار تحذير
  static Future<void> showWarningNotification(String message) async {
    await showNotification(
      title: '⚠️ تحذير',
      body: message,
      priority: NotificationPriority.high,
    );
  }

  /// إشعار معلومات
  static Future<void> showInfoNotification(String message) async {
    await showNotification(
      title: 'ℹ️ معلومات',
      body: message,
      priority: NotificationPriority.low,
    );
  }

  /// إشعار المخزون المنخفض
  static Future<void> showLowStockNotification(
      String productName, int quantity) async {
    await showNotification(
      title: '📦 مخزون منخفض',
      body: 'المنتج "$productName" أوشك على النفاد ($quantity متبقي)',
      priority: NotificationPriority.high,
      payload: 'low_stock',
    );
  }

  /// إشعار تحديث سعر الدولار
  static Future<void> showDollarRateUpdateNotification(
      double oldRate, double newRate) async {
    final changePercent = ((newRate - oldRate) / oldRate * 100);
    final isIncrease = newRate > oldRate;
    final changeText = isIncrease ? 'ارتفع' : 'انخفض';
    final changeIcon = isIncrease ? '📈' : '📉';

    await showNotification(
      title: '$changeIcon تحديث سعر الدولار',
      body:
          'السعر $changeText من ${oldRate.toStringAsFixed(0)} إلى ${newRate.toStringAsFixed(0)} ل.س (${changePercent.toStringAsFixed(1)}%)',
      priority: NotificationPriority.high,
      payload: 'dollar_rate_change',
    );
  }

  /// إشعار فاتورة جديدة
  static Future<void> showNewInvoiceNotification(
      String invoiceNumber, double amount) async {
    await showNotification(
      title: '🧾 فاتورة جديدة',
      body:
          'تم إنشاء الفاتورة رقم $invoiceNumber بقيمة ${amount.toStringAsFixed(0)} ل.س',
      priority: NotificationPriority.normal,
      payload: 'new_invoice',
    );
  }

  /// إشعار نسخة احتياطية
  static Future<void> showBackupNotification(String message) async {
    await showNotification(
      title: '💾 نسخة احتياطية',
      body: message,
      priority: NotificationPriority.low,
      payload: 'backup',
    );
  }

  /// الحصول على تفاصيل الإشعار حسب الأولوية
  static NotificationDetails _getNotificationDetails(
      NotificationPriority priority) {
    late Importance importance;
    late Priority androidPriority;
    late String channelId;
    late String channelName;
    late Color? ledColor;
    late bool enableVibration;

    switch (priority) {
      case NotificationPriority.low:
        importance = Importance.low;
        androidPriority = Priority.low;
        channelId = 'low_priority_channel';
        channelName = 'إشعارات منخفضة الأولوية';
        ledColor = null;
        enableVibration = false;
        break;
      case NotificationPriority.normal:
        importance = Importance.defaultImportance;
        androidPriority = Priority.defaultPriority;
        channelId = 'normal_priority_channel';
        channelName = 'إشعارات عادية';
        ledColor = null;
        enableVibration = true;
        break;
      case NotificationPriority.medium:
        importance = Importance.defaultImportance;
        androidPriority = Priority.defaultPriority;
        channelId = 'medium_priority_channel';
        channelName = 'إشعارات متوسطة الأولوية';
        ledColor = const Color(0xFFFF9800);
        enableVibration = true;
        break;
      case NotificationPriority.high:
        importance = Importance.high;
        androidPriority = Priority.high;
        channelId = 'high_priority_channel';
        channelName = 'إشعارات عالية الأولوية';
        ledColor = const Color(0xFFFF5722);
        enableVibration = true;
        break;
      case NotificationPriority.urgent:
        importance = Importance.max;
        androidPriority = Priority.max;
        channelId = 'urgent_priority_channel';
        channelName = 'إشعارات عاجلة';
        ledColor = const Color(0xFFFF0000);
        enableVibration = true;
        break;
    }

    return NotificationDetails(
      android: AndroidNotificationDetails(
        channelId,
        channelName,
        channelDescription: 'قناة $channelName للتطبيق',
        importance: importance,
        priority: androidPriority,
        showWhen: true,
        enableVibration: enableVibration,
        playSound: true,
        ledColor: ledColor,
        ledOnMs: 1000,
        ledOffMs: 500,
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');

    // هنا يمكن إضافة منطق التنقل حسب نوع الإشعار
    if (response.payload != null) {
      final payload = response.payload!;

      if (payload == 'low_stock') {
        debugPrint('التنقل لصفحة المنتجات');
      } else if (payload == 'dollar_rate_change') {
        debugPrint('التنقل لصفحة تاريخ الدولار');
      } else if (payload == 'new_invoice') {
        debugPrint('التنقل لصفحة الفواتير');
      }
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    if (!_isInitialized) return;
    await _localNotifications!.cancelAll();
    debugPrint('🗑️ تم إلغاء جميع الإشعارات');
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    if (!_isInitialized) return;
    await _localNotifications!.cancel(id);
    debugPrint('🗑️ تم إلغاء الإشعار رقم $id');
  }
}

/// أولويات الإشعارات
enum NotificationPriority { low, normal, high, urgent, medium }

/// فئات الإشعارات
enum NotificationCategory {
  general,
  inventory,
  sales,
  customers,
  currency,
  insights,
  backup
}

extension OfflineNotificationServiceExtension on OfflineNotificationService {
  /// إشعار مخصص مع فئة وأولوية
  static Future<void> showCustomNotification({
    required String title,
    required String body,
    NotificationPriority priority = NotificationPriority.normal,
    NotificationCategory category = NotificationCategory.general,
    Map<String, dynamic>? data,
  }) async {
    await OfflineNotificationService.showNotification(
      title: title,
      body: body,
      priority: priority,
      payload: data?.toString(),
    );
  }
}
