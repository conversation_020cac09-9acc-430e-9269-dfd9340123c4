class Product {
  final int id;
  final String name;
  final String? barcode;
  final double purchasePrice;
  final double salePrice;
  final int quantity;
  final String? notes;
  final int minQuantity;
  final double sellingPriceSyp;
  final double sellingPriceUsd;
  final double purchasePriceSyp;
  final double purchasePriceUsd;
  final String purchaseCurrency; // 'SYP' أو 'USD'

  Product({
    required this.id,
    required this.name,
    this.barcode,
    required this.purchasePrice,
    required this.salePrice,
    required this.quantity,
    this.notes,
    this.minQuantity = 5,
    required this.sellingPriceSyp,
    required this.sellingPriceUsd,
    required this.purchasePriceSyp,
    required this.purchasePriceUsd,
    this.purchaseCurrency = 'SYP',
  });

  /// حساب هامش الربح
  double getProfitMargin() {
    if (purchasePrice <= 0) return 0.0;
    return ((salePrice - purchasePrice) / purchasePrice) * 100;
  }

  /// فحص إذا كان المنتج منخفض المخزون
  bool isLowStock() {
    return quantity <= minQuantity;
  }

  /// فحص إذا كان المنتج نفد من المخزون
  bool isOutOfStock() {
    return quantity <= 0;
  }

  /// تحديث الأسعار بناءً على سعر الدولار الجديد
  Product updatePricesWithDollarRate(double newDollarRate) {
    double newSellingPriceSyp;
    double newSellingPriceUsd;
    double newPurchasePriceSyp;
    double newPurchasePriceUsd;

    if (purchaseCurrency == 'USD') {
      // إذا كان سعر الشراء بالدولار، نحدث السعر بالليرة
      newPurchasePriceSyp = purchasePriceUsd * newDollarRate;
      newPurchasePriceUsd = purchasePriceUsd;
      newSellingPriceSyp = sellingPriceUsd * newDollarRate;
      newSellingPriceUsd = sellingPriceUsd;
    } else {
      // إذا كان سعر الشراء بالليرة، نحدث السعر بالدولار
      newPurchasePriceSyp = purchasePriceSyp;
      newPurchasePriceUsd = purchasePriceSyp / newDollarRate;
      newSellingPriceSyp = sellingPriceSyp;
      newSellingPriceUsd = sellingPriceSyp / newDollarRate;
    }

    return copyWith(
      purchasePriceSyp: newPurchasePriceSyp,
      purchasePriceUsd: newPurchasePriceUsd,
      sellingPriceSyp: newSellingPriceSyp,
      sellingPriceUsd: newSellingPriceUsd,
      purchasePrice:
          purchaseCurrency == 'USD' ? newPurchasePriceUsd : newPurchasePriceSyp,
      salePrice:
          purchaseCurrency == 'USD' ? newSellingPriceUsd : newSellingPriceSyp,
    );
  }

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int,
      name: json['name'] as String,
      barcode: json['barcode'] as String?,
      purchasePrice:
          double.tryParse(json['purchase_price']?.toString() ?? '0.0') ?? 0.0,
      salePrice:
          double.tryParse(json['sale_price']?.toString() ?? '0.0') ?? 0.0,
      quantity: json['quantity'] as int? ?? 0,
      notes: json['notes'] as String?,
      minQuantity: json['min_quantity'] as int? ?? 5,
      sellingPriceSyp:
          double.tryParse(json['selling_price_syp']?.toString() ?? '0.0') ??
              0.0,
      sellingPriceUsd:
          double.tryParse(json['selling_price_usd']?.toString() ?? '0.0') ??
              0.0,
      purchasePriceSyp:
          double.tryParse(json['purchase_price_syp']?.toString() ?? '0.0') ??
              0.0,
      purchasePriceUsd:
          double.tryParse(json['purchase_price_usd']?.toString() ?? '0.0') ??
              0.0,
      purchaseCurrency: json['purchase_currency'] as String? ?? 'SYP',
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'barcode': barcode,
        'purchase_price': purchasePrice.toString(),
        'sale_price': salePrice.toString(),
        'quantity': quantity,
        'notes': notes,
        'min_quantity': minQuantity,
        'selling_price_syp': sellingPriceSyp.toString(),
        'selling_price_usd': sellingPriceUsd.toString(),
        'purchase_price_syp': purchasePriceSyp.toString(),
        'purchase_price_usd': purchasePriceUsd.toString(),
        'purchase_currency': purchaseCurrency,
      };

  Map<String, dynamic> toJsonForCreate() => {
        'name': name,
        'barcode': barcode,
        'purchase_price': purchasePrice.toString(),
        'sale_price': salePrice.toString(),
        'quantity': quantity,
        'notes': notes,
        'min_quantity': minQuantity,
        'selling_price_syp': sellingPriceSyp.toString(),
        'selling_price_usd': sellingPriceUsd.toString(),
        'purchase_price_syp': purchasePriceSyp.toString(),
        'purchase_price_usd': purchasePriceUsd.toString(),
        'purchase_currency': purchaseCurrency,
      };

  Product copyWith({
    int? id,
    String? name,
    String? barcode,
    double? purchasePrice,
    double? salePrice,
    int? quantity,
    String? notes,
    int? minQuantity,
    double? sellingPriceSyp,
    double? sellingPriceUsd,
    double? purchasePriceSyp,
    double? purchasePriceUsd,
    String? purchaseCurrency,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      barcode: barcode ?? this.barcode,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salePrice: salePrice ?? this.salePrice,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      minQuantity: minQuantity ?? this.minQuantity,
      sellingPriceSyp: sellingPriceSyp ?? this.sellingPriceSyp,
      sellingPriceUsd: sellingPriceUsd ?? this.sellingPriceUsd,
      purchasePriceSyp: purchasePriceSyp ?? this.purchasePriceSyp,
      purchasePriceUsd: purchasePriceUsd ?? this.purchasePriceUsd,
      purchaseCurrency: purchaseCurrency ?? this.purchaseCurrency,
    );
  }
}
