from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Sum
from .models import Currency, Product, Customer, Invoice, InvoiceItem, Payment
from .serializers import (
    CurrencySerializer, ProductSerializer, CustomerSerializer,
    InvoiceSerializer, InvoiceItemSerializer, PaymentSerializer
)

class CurrencyViewSet(viewsets.ModelViewSet):
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    permission_classes = [IsAuthenticated]

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['GET'])
    def low_stock(self, request):
        products = Product.objects.filter(quantity__lte=models.F('min_quantity'))
        serializer = self.get_serializer(products, many=True)
        return Response(serializer.data)

class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['GET'])
    def debt_history(self, request, pk=None):
        customer = self.get_object()
        invoices = Invoice.objects.filter(customer=customer, payment_type__in=['DEBT', 'PARTIAL'])
        payments = Payment.objects.filter(customer=customer)
        
        invoice_serializer = InvoiceSerializer(invoices, many=True)
        payment_serializer = PaymentSerializer(payments, many=True)
        
        return Response({
            'invoices': invoice_serializer.data,
            'payments': payment_serializer.data
        })

class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        invoice = serializer.save()
        items_data = self.request.data.get('items', [])
        
        for item_data in items_data:
            item_data['invoice'] = invoice.id
            item_serializer = InvoiceItemSerializer(data=item_data)
            if item_serializer.is_valid():
                item_serializer.save()

class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        payment = serializer.save()
        customer = payment.customer
        customer.total_debt -= payment.amount_syp
        customer.save()