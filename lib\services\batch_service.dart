import 'package:flutter/foundation.dart';
import '../models/product_batch.dart';

import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';
import '../services/warehouse_service.dart';

/// خدمة إدارة الدفعات
class BatchService {
  static const String _batchesBox = 'product_batches';

  /// تهيئة خدمة الدفعات
  static Future<void> initialize() async {
    try {
      debugPrint('✅ تم تهيئة خدمة الدفعات');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة الدفعات: $e');
      rethrow;
    }
  }

  // ==================== إدارة الدفعات ====================

  /// الحصول على جميع الدفعات
  static Future<List<ProductBatch>> getAllBatches() async {
    try {
      final batchesData = await OfflineDatabaseService.getBoxData(_batchesBox);
      final batches = <ProductBatch>[];

      for (var batchMap in batchesData.values) {
        batches.add(ProductBatch.fromJson(Map<String, dynamic>.from(batchMap)));
      }

      // ترتيب حسب تاريخ الإنتاج (الأحدث أولاً)
      batches
          .sort((a, b) => b.manufacturingDate.compareTo(a.manufacturingDate));

      return batches;
    } catch (e) {
      debugPrint('❌ فشل في جلب الدفعات: $e');
      return [];
    }
  }

  /// الحصول على دفعة بالمعرف
  static Future<ProductBatch?> getBatchById(int id) async {
    try {
      final batchData =
          await OfflineDatabaseService.getBoxItem(_batchesBox, id.toString());
      if (batchData != null) {
        return ProductBatch.fromJson(Map<String, dynamic>.from(batchData));
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب الدفعة: $e');
      return null;
    }
  }

  /// الحصول على دفعة برقم الدفعة
  static Future<ProductBatch?> getBatchByNumber(String batchNumber) async {
    try {
      final batches = await getAllBatches();
      return batches.firstWhere(
        (batch) => batch.batchNumber == batchNumber,
        orElse: () => throw Exception('الدفعة غير موجودة'),
      );
    } catch (e) {
      debugPrint('❌ فشل في جلب الدفعة برقم الدفعة: $e');
      return null;
    }
  }

  /// الحصول على دفعات منتج
  static Future<List<ProductBatch>> getProductBatches(int productId) async {
    try {
      final batches = await getAllBatches();
      return batches.where((batch) => batch.productId == productId).toList();
    } catch (e) {
      debugPrint('❌ فشل في جلب دفعات المنتج: $e');
      return [];
    }
  }

  /// الحصول على دفعات مستودع
  static Future<List<ProductBatch>> getWarehouseBatches(int warehouseId) async {
    try {
      final batches = await getAllBatches();
      return batches
          .where((batch) => batch.warehouseId == warehouseId)
          .toList();
    } catch (e) {
      debugPrint('❌ فشل في جلب دفعات المستودع: $e');
      return [];
    }
  }

  /// إضافة دفعة جديدة
  static Future<ProductBatch> addBatch(ProductBatch batch) async {
    try {
      // التحقق من عدم تكرار رقم الدفعة
      final existingBatch = await getBatchByNumber(batch.batchNumber);
      if (existingBatch != null) {
        throw Exception('رقم الدفعة موجود مسبقاً');
      }

      // إنشاء معرف جديد
      final newId = await _getNextBatchId();
      final newBatch = batch.copyWith(
        id: newId,
        createdAt: DateTime.now(),
      );

      // حفظ الدفعة
      await OfflineDatabaseService.saveBoxItem(
        _batchesBox,
        newId.toString(),
        newBatch.toJson(),
      );

      // تحديث مخزون المستودع
      await WarehouseService.updateProductStock(
        productId: newBatch.productId,
        warehouseId: newBatch.warehouseId,
        newQuantity: newBatch.currentQuantity,
        reference: 'إضافة دفعة ${newBatch.batchNumber}',
        notes: 'دفعة جديدة من ${newBatch.supplier ?? "مورد غير محدد"}',
      );

      // إرسال إشعار
      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة الدفعة "${newBatch.batchNumber}" بنجاح',
      );

      debugPrint('✅ تم إضافة الدفعة: ${newBatch.batchNumber}');
      return newBatch;
    } catch (e) {
      debugPrint('❌ فشل في إضافة الدفعة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة الدفعة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث دفعة
  static Future<ProductBatch> updateBatch(ProductBatch batch) async {
    try {
      final updatedBatch = batch.copyWith(updatedAt: DateTime.now());

      await OfflineDatabaseService.saveBoxItem(
        _batchesBox,
        batch.id.toString(),
        updatedBatch.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث الدفعة "${updatedBatch.batchNumber}" بنجاح',
      );

      debugPrint('✅ تم تحديث الدفعة: ${updatedBatch.batchNumber}');
      return updatedBatch;
    } catch (e) {
      debugPrint('❌ فشل في تحديث الدفعة: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث الدفعة: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// بيع من دفعة (FIFO/LIFO)
  static Future<ProductBatch> sellFromBatch(
    String batchNumber,
    int quantity, {
    String? reference,
    String? notes,
  }) async {
    try {
      final batch = await getBatchByNumber(batchNumber);
      if (batch == null) {
        throw Exception('الدفعة غير موجودة');
      }

      if (batch.availableQuantity < quantity) {
        throw Exception('الكمية المطلوبة غير متوفرة في الدفعة');
      }

      if (!batch.isAvailableForSale) {
        throw Exception('الدفعة غير متاحة للبيع');
      }

      // تحديث كميات الدفعة
      final updatedBatch = batch.copyWith(
        currentQuantity: batch.currentQuantity - quantity,
        availableQuantity: batch.availableQuantity - quantity,
        soldQuantity: batch.soldQuantity + quantity,
        status: (batch.currentQuantity - quantity) <= 0
            ? BatchStatus.sold
            : batch.status,
        updatedAt: DateTime.now(),
      );

      await updateBatch(updatedBatch);

      debugPrint('✅ تم البيع من الدفعة: $batchNumber');
      return updatedBatch;
    } catch (e) {
      debugPrint('❌ فشل في البيع من الدفعة: $e');
      rethrow;
    }
  }

  /// حجز كمية من دفعة
  static Future<ProductBatch> reserveFromBatch(
    String batchNumber,
    int quantity, {
    String? reference,
    String? notes,
  }) async {
    try {
      final batch = await getBatchByNumber(batchNumber);
      if (batch == null) {
        throw Exception('الدفعة غير موجودة');
      }

      if (batch.availableQuantity < quantity) {
        throw Exception('الكمية المطلوبة غير متوفرة للحجز');
      }

      // تحديث كميات الدفعة
      final updatedBatch = batch.copyWith(
        availableQuantity: batch.availableQuantity - quantity,
        reservedQuantity: batch.reservedQuantity + quantity,
        updatedAt: DateTime.now(),
      );

      await updateBatch(updatedBatch);

      debugPrint('✅ تم حجز $quantity من الدفعة: $batchNumber');
      return updatedBatch;
    } catch (e) {
      debugPrint('❌ فشل في حجز من الدفعة: $e');
      rethrow;
    }
  }

  /// إلغاء حجز من دفعة
  static Future<ProductBatch> unreserveFromBatch(
    String batchNumber,
    int quantity, {
    String? reference,
    String? notes,
  }) async {
    try {
      final batch = await getBatchByNumber(batchNumber);
      if (batch == null) {
        throw Exception('الدفعة غير موجودة');
      }

      if (batch.reservedQuantity < quantity) {
        throw Exception('الكمية المحجوزة أقل من المطلوب إلغاؤها');
      }

      // تحديث كميات الدفعة
      final updatedBatch = batch.copyWith(
        availableQuantity: batch.availableQuantity + quantity,
        reservedQuantity: batch.reservedQuantity - quantity,
        updatedAt: DateTime.now(),
      );

      await updateBatch(updatedBatch);

      debugPrint('✅ تم إلغاء حجز $quantity من الدفعة: $batchNumber');
      return updatedBatch;
    } catch (e) {
      debugPrint('❌ فشل في إلغاء حجز من الدفعة: $e');
      rethrow;
    }
  }

  // ==================== تقارير الدفعات ====================

  /// الحصول على الدفعات المنتهية الصلاحية
  static Future<List<ProductBatch>> getExpiredBatches() async {
    try {
      final batches = await getAllBatches();
      return batches.where((batch) => batch.isExpired).toList();
    } catch (e) {
      debugPrint('❌ فشل في جلب الدفعات المنتهية الصلاحية: $e');
      return [];
    }
  }

  /// الحصول على الدفعات التي ستنتهي صلاحيتها قريباً
  static Future<List<ProductBatch>> getExpiringBatches(int daysAhead) async {
    try {
      final batches = await getAllBatches();
      return batches
          .where((batch) => batch.isExpiringWithin(daysAhead))
          .toList();
    } catch (e) {
      debugPrint('❌ فشل في جلب الدفعات التي ستنتهي صلاحيتها: $e');
      return [];
    }
  }

  /// تقرير الدفعات
  static Future<Map<String, dynamic>> getBatchesReport() async {
    try {
      final batches = await getAllBatches();

      final activeBatches =
          batches.where((b) => b.status == BatchStatus.active).toList();
      final expiredBatches = batches.where((b) => b.isExpired).toList();
      final expiringBatches =
          batches.where((b) => b.isExpiringWithin(30)).toList();
      final soldBatches =
          batches.where((b) => b.status == BatchStatus.sold).toList();

      double totalValue =
          batches.fold(0, (sum, batch) => sum + batch.totalValue);
      double availableValue =
          batches.fold(0, (sum, batch) => sum + batch.availableValue);

      return {
        'total_batches': batches.length,
        'active_batches': activeBatches.length,
        'expired_batches': expiredBatches.length,
        'expiring_batches': expiringBatches.length,
        'sold_batches': soldBatches.length,
        'total_value': totalValue,
        'available_value': availableValue,
        'expired_value':
            expiredBatches.fold(0.0, (sum, batch) => sum + batch.totalValue),
        'expiring_value':
            expiringBatches.fold(0.0, (sum, batch) => sum + batch.totalValue),
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل في إنشاء تقرير الدفعات: $e');
      return {};
    }
  }

  // ==================== وظائف مساعدة ====================

  /// الحصول على معرف دفعة جديد
  static Future<int> _getNextBatchId() async {
    final batches = await getAllBatches();
    if (batches.isEmpty) return 1;

    final maxId = batches.map((b) => b.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// إنشاء رقم دفعة تلقائي
  static String generateBatchNumber(int productId) {
    final now = DateTime.now();
    final dateStr =
        '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr =
        '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    return 'B${productId.toString().padLeft(4, '0')}-$dateStr-$timeStr';
  }
}
