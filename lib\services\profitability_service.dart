import 'package:flutter/foundation.dart';
import '../models/profitability_analysis.dart';
import '../models/invoice.dart';
import '../models/product.dart';

import '../services/offline_database_service.dart';

/// خدمة تحليل الربحية
class ProfitabilityService {
  static const String _analysisBox = 'profitability_analysis';
  static const String _productProfitBox = 'product_profitability';
  static const String _customerProfitBox = 'customer_profitability';

  /// تحليل الربحية لفترة محددة
  static Future<ProfitabilityAnalysis> analyzeProfitability({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      debugPrint(
          'تحليل الربحية من ${startDate.toIso8601String()} إلى ${endDate.toIso8601String()}');

      // جلب البيانات
      final invoices = await _getInvoicesInPeriod(startDate, endDate);
      final products = await OfflineDatabaseService.getAllProducts();
      // final customers = await OfflineDatabaseService.getAllCustomers(); // غير مستخدم حالياً

      // حساب الإيرادات والتكاليف
      double totalRevenue = 0;
      double totalCost = 0;
      Map<String, double> revenueByCategory = {};
      Map<String, double> costByCategory = {};
      Map<String, double> profitByProduct = {};
      Map<String, double> profitByCustomer = {};

      for (var invoice in invoices) {
        totalRevenue += invoice.totalAmountSyp;

        // تحليل حسب العميل
        final customerName = invoice.customerName;
        profitByCustomer[customerName] =
            (profitByCustomer[customerName] ?? 0) + invoice.totalAmountSyp;

        // تحليل حسب المنتج
        for (var item in invoice.items) {
          final product = products.firstWhere(
            (p) => p.id.toString() == item.productId,
            orElse: () => Product(
              id: 0,
              name: 'منتج غير معروف',
              purchasePrice: 0,
              salePrice: 0,
              quantity: 0,
              sellingPriceSyp: 0,
              sellingPriceUsd: 0,
              purchasePriceSyp: 0,
              purchasePriceUsd: 0,
            ),
          );

          final itemRevenue = item.unitPriceSyp * item.quantity;
          final itemCost = product.purchasePrice * item.quantity;
          final itemProfit = itemRevenue - itemCost;

          totalCost += itemCost;
          profitByProduct[product.name] =
              (profitByProduct[product.name] ?? 0) + itemProfit;

          // تحليل حسب الفئة (يمكن إضافة فئات المنتجات لاحقاً)
          const category = 'عام'; // يمكن تحسينه لاحقاً
          revenueByCategory[category] =
              (revenueByCategory[category] ?? 0) + itemRevenue;
          costByCategory[category] = (costByCategory[category] ?? 0) + itemCost;
        }
      }

      // حساب المؤشرات
      final grossProfit = totalRevenue - totalCost;
      final operatingExpenses =
          await _calculateOperatingExpenses(startDate, endDate);
      final netProfit = grossProfit - operatingExpenses;
      final grossProfitMargin =
          totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0.0;
      final netProfitMargin =
          totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0.0;

      // إنشاء الاتجاهات
      final trends = await _generateProfitTrends(startDate, endDate);

      // إنشاء التحليل
      final analysis = ProfitabilityAnalysis(
        id: DateTime.now().millisecondsSinceEpoch,
        periodStart: startDate,
        periodEnd: endDate,
        totalRevenue: totalRevenue,
        totalCost: totalCost,
        grossProfit: grossProfit,
        operatingExpenses: operatingExpenses,
        netProfit: netProfit,
        grossProfitMargin: grossProfitMargin,
        netProfitMargin: netProfitMargin,
        revenueByCategory: revenueByCategory,
        costByCategory: costByCategory,
        profitByProduct: profitByProduct,
        profitByCustomer: profitByCustomer,
        trends: trends,
        createdAt: DateTime.now(),
      );

      // حفظ التحليل
      await OfflineDatabaseService.saveBoxItem(
        _analysisBox,
        analysis.id.toString(),
        analysis.toJson(),
      );

      debugPrint('✅ تم تحليل الربحية بنجاح');
      return analysis;
    } catch (e) {
      debugPrint('❌ فشل في تحليل الربحية: $e');
      rethrow;
    }
  }

  /// تحليل ربحية المنتجات
  static Future<List<ProductProfitability>> analyzeProductProfitability({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final invoices = await _getInvoicesInPeriod(startDate, endDate);
      final products = await OfflineDatabaseService.getAllProducts();

      Map<String, Map<String, dynamic>> productData = {};

      // جمع بيانات المنتجات
      for (var invoice in invoices) {
        for (var item in invoice.items) {
          if (!productData.containsKey(item.productId)) {
            productData[item.productId] = {
              'quantity': 0,
              'revenue': 0.0,
              'cost': 0.0,
            };
          }

          final productIdInt = int.tryParse(item.productId) ?? 0;
          final product = products.firstWhere(
            (p) => p.id == productIdInt,
            orElse: () => Product(
              id: productIdInt,
              name: 'منتج غير معروف',
              purchasePrice: 0,
              salePrice: 0,
              quantity: 0,
              sellingPriceSyp: 0,
              sellingPriceUsd: 0,
              purchasePriceSyp: 0,
              purchasePriceUsd: 0,
            ),
          );

          productData[item.productId]!['quantity'] += item.quantity;
          productData[item.productId]!['revenue'] += item.price * item.quantity;
          productData[item.productId]!['cost'] +=
              product.purchasePrice * item.quantity;
        }
      }

      // إنشاء تحليل المنتجات
      List<ProductProfitability> productAnalysis = [];

      for (var entry in productData.entries) {
        final productIdString = entry.key;
        final productId = int.tryParse(productIdString) ?? 0;
        final data = entry.value;

        final product = products.firstWhere(
          (p) => p.id == productId,
          orElse: () => Product(
            id: productId,
            name: 'منتج غير معروف',
            purchasePrice: 0,
            salePrice: 0,
            quantity: 0,
            sellingPriceSyp: 0,
            sellingPriceUsd: 0,
            purchasePriceSyp: 0,
            purchasePriceUsd: 0,
          ),
        );

        final revenue = data['revenue'] as double;
        final cost = data['cost'] as double;
        final quantity = data['quantity'] as int;
        final profit = revenue - cost;
        final margin = revenue > 0 ? (profit / revenue) * 100 : 0.0;

        final analysis = ProductProfitability(
          productId: productId,
          productName: product.name,
          quantitySold: quantity,
          totalRevenue: revenue,
          totalCost: cost,
          grossProfit: profit,
          profitMargin: margin.toDouble(),
          averageSellingPrice: quantity > 0 ? revenue / quantity : 0,
          averageCost: quantity > 0 ? cost / quantity : 0,
          periodStart: startDate,
          periodEnd: endDate,
        );

        productAnalysis.add(analysis);

        // حفظ تحليل المنتج
        await OfflineDatabaseService.saveBoxItem(
          _productProfitBox,
          '${productId}_${startDate.millisecondsSinceEpoch}',
          analysis.toJson(),
        );
      }

      // ترتيب حسب الربحية
      productAnalysis.sort((a, b) => b.grossProfit.compareTo(a.grossProfit));

      debugPrint('✅ تم تحليل ربحية ${productAnalysis.length} منتج');
      return productAnalysis;
    } catch (e) {
      debugPrint('❌ فشل في تحليل ربحية المنتجات: $e');
      return [];
    }
  }

  /// تحليل ربحية العملاء
  static Future<List<CustomerProfitability>> analyzeCustomerProfitability({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final invoices = await _getInvoicesInPeriod(startDate, endDate);
      final products = await OfflineDatabaseService.getAllProducts();

      Map<int, Map<String, dynamic>> customerData = {};

      // جمع بيانات العملاء
      for (var invoice in invoices) {
        if (!customerData.containsKey(invoice.customerId)) {
          customerData[invoice.customerId] = {
            'name': invoice.customerName,
            'orders': 0,
            'revenue': 0.0,
            'cost': 0.0,
            'first_order': invoice.date,
            'last_order': invoice.date,
          };
        }

        customerData[invoice.customerId]!['orders'] += 1;
        customerData[invoice.customerId]!['revenue'] += invoice.totalAmountSyp;

        // تحديث تواريخ الطلبات
        final firstOrder =
            customerData[invoice.customerId]!['first_order'] as DateTime;
        final lastOrder =
            customerData[invoice.customerId]!['last_order'] as DateTime;

        if (invoice.date.isBefore(firstOrder)) {
          customerData[invoice.customerId]!['first_order'] = invoice.date;
        }
        if (invoice.date.isAfter(lastOrder)) {
          customerData[invoice.customerId]!['last_order'] = invoice.date;
        }

        // حساب التكلفة
        for (var item in invoice.items) {
          final productIdInt = int.tryParse(item.productId) ?? 0;
          final product = products.firstWhere(
            (p) => p.id == productIdInt,
            orElse: () => Product(
              id: 0,
              name: '',
              purchasePrice: 0,
              salePrice: 0,
              quantity: 0,
              sellingPriceSyp: 0,
              sellingPriceUsd: 0,
              purchasePriceSyp: 0,
              purchasePriceUsd: 0,
            ),
          );
          customerData[invoice.customerId]!['cost'] +=
              product.purchasePrice * item.quantity;
        }
      }

      // إنشاء تحليل العملاء
      List<CustomerProfitability> customerAnalysis = [];

      for (var entry in customerData.entries) {
        final customerId = entry.key;
        final data = entry.value;

        final revenue = data['revenue'] as double;
        final cost = data['cost'] as double;
        final orders = data['orders'] as int;
        final profit = revenue - cost;
        final margin = revenue > 0 ? (profit / revenue) * 100 : 0.0;

        final analysis = CustomerProfitability(
          customerId: customerId,
          customerName: data['name'] as String,
          totalOrders: orders,
          totalRevenue: revenue,
          totalCost: cost,
          grossProfit: profit,
          profitMargin: margin.toDouble(),
          averageOrderValue: orders > 0 ? revenue / orders : 0,
          customerLifetimeValue: profit, // يمكن تحسينه لاحقاً
          firstOrderDate: data['first_order'] as DateTime,
          lastOrderDate: data['last_order'] as DateTime,
        );

        customerAnalysis.add(analysis);

        // حفظ تحليل العميل
        await OfflineDatabaseService.saveBoxItem(
          _customerProfitBox,
          '${customerId}_${startDate.millisecondsSinceEpoch}',
          analysis.toJson(),
        );
      }

      // ترتيب حسب الربحية
      customerAnalysis.sort((a, b) => b.grossProfit.compareTo(a.grossProfit));

      debugPrint('✅ تم تحليل ربحية ${customerAnalysis.length} عميل');
      return customerAnalysis;
    } catch (e) {
      debugPrint('❌ فشل في تحليل ربحية العملاء: $e');
      return [];
    }
  }

  /// الحصول على الفواتير في فترة محددة
  static Future<List<Invoice>> _getInvoicesInPeriod(
      DateTime start, DateTime end) async {
    final allInvoices = await OfflineDatabaseService.getAllInvoices();
    return allInvoices.where((invoice) {
      return invoice.date.isAfter(start.subtract(const Duration(days: 1))) &&
          invoice.date.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  /// حساب المصروفات التشغيلية
  static Future<double> _calculateOperatingExpenses(
      DateTime start, DateTime end) async {
    // يمكن تحسينه لاحقاً لجلب المصروفات الفعلية من النظام المحاسبي
    return 0.0;
  }

  /// إنشاء اتجاهات الربحية
  static Future<List<ProfitTrend>> _generateProfitTrends(
      DateTime start, DateTime end) async {
    List<ProfitTrend> trends = [];

    final days = end.difference(start).inDays;
    if (days <= 0) return trends;

    for (int i = 0; i <= days; i++) {
      final date = start.add(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final dayInvoices = await _getInvoicesInPeriod(dayStart, dayEnd);

      double dayRevenue = 0;
      double dayCost = 0;

      for (var invoice in dayInvoices) {
        dayRevenue += invoice.totalAmountSyp;
        // يمكن حساب التكلفة الفعلية هنا
      }

      final dayProfit = dayRevenue - dayCost;
      final dayMargin = dayRevenue > 0 ? (dayProfit / dayRevenue) * 100 : 0.0;

      trends.add(ProfitTrend(
        date: dayStart,
        revenue: dayRevenue,
        cost: dayCost,
        profit: dayProfit,
        profitMargin: dayMargin.toDouble(),
      ));
    }

    return trends;
  }

  /// الحصول على تحليل محفوظ
  static Future<ProfitabilityAnalysis?> getSavedAnalysis(int id) async {
    try {
      final data =
          await OfflineDatabaseService.getBoxItem(_analysisBox, id.toString());
      if (data != null) {
        return ProfitabilityAnalysis.fromJson(Map<String, dynamic>.from(data));
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب التحليل: $e');
      return null;
    }
  }

  /// الحصول على جميع التحليلات المحفوظة
  static Future<List<ProfitabilityAnalysis>> getAllAnalyses() async {
    try {
      final data = await OfflineDatabaseService.getBoxData(_analysisBox);
      final analyses = <ProfitabilityAnalysis>[];

      for (var item in data.values) {
        analyses.add(
            ProfitabilityAnalysis.fromJson(Map<String, dynamic>.from(item)));
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      analyses.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return analyses;
    } catch (e) {
      debugPrint('❌ فشل في جلب التحليلات: $e');
      return [];
    }
  }
}
