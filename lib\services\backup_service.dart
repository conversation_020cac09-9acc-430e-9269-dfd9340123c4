import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/invoice.dart';
import '../services/offline_storage_service.dart';

/// خدمة النسخ الاحتياطي والاستعادة
class BackupService {
  static const String _backupFileName = 'invoice_system_backup';
  static const String _backupExtension = '.json';

  /// إنشاء نسخة احتياطية شاملة
  static Future<File> createFullBackup({
    List<Product>? products,
    List<Customer>? customers,
    List<Invoice>? invoices,
    Map<String, dynamic>? settings,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء النسخة الاحتياطية...');

      final backupData = {
        'metadata': {
          'version': '1.0.0',
          'created_at': DateTime.now().toIso8601String(),
          'app_name': 'نظام إدارة الفواتير',
          'backup_type': 'full',
        },
        'data': {
          'products': products?.map((p) => p.toJson()).toList() ?? [],
          'customers': customers?.map((c) => c.toJson()).toList() ?? [],
          'invoices': invoices?.map((i) => i.toJson()).toList() ?? [],
          'settings': settings ?? await _getAllSettings(),
          'currency_history': await _getCurrencyHistory(),
        },
        'statistics': {
          'total_products': products?.length ?? 0,
          'total_customers': customers?.length ?? 0,
          'total_invoices': invoices?.length ?? 0,
          'backup_size_estimate': 'calculating...',
        }
      };

      final jsonString = jsonEncode(backupData);
      final file = await _saveBackupToFile(jsonString);

      // تحديث حجم النسخة الاحتياطية
      final fileSize = await file.length();
      final statistics = backupData['statistics'] as Map<String, dynamic>;
      statistics['backup_size_estimate'] = _formatFileSize(fileSize);

      debugPrint('✅ تم إنشاء النسخة الاحتياطية بنجاح: ${file.path}');
      debugPrint('📊 حجم الملف: ${_formatFileSize(fileSize)}');

      return file;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// استعادة البيانات من النسخة الاحتياطية
  static Future<BackupRestoreResult> restoreFromBackup(File backupFile) async {
    try {
      debugPrint('🔄 بدء استعادة البيانات من النسخة الاحتياطية...');

      final jsonString = await backupFile.readAsString();
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // التحقق من صحة النسخة الاحتياطية
      final validationResult = _validateBackup(backupData);
      if (!validationResult.isValid) {
        throw Exception(
            'النسخة الاحتياطية غير صالحة: ${validationResult.error}');
      }

      final data = backupData['data'] as Map<String, dynamic>;
      final result = BackupRestoreResult();

      // استعادة المنتجات
      if (data['products'] != null) {
        final productsJson = data['products'] as List;
        result.products = productsJson
            .map((json) => Product.fromJson(json as Map<String, dynamic>))
            .toList();
        debugPrint('📦 تم استعادة ${result.products.length} منتج');
      }

      // استعادة العملاء
      if (data['customers'] != null) {
        final customersJson = data['customers'] as List;
        result.customers = customersJson
            .map((json) => Customer.fromJson(json as Map<String, dynamic>))
            .toList();
        debugPrint('👥 تم استعادة ${result.customers.length} عميل');
      }

      // استعادة الفواتير
      if (data['invoices'] != null) {
        final invoicesJson = data['invoices'] as List;
        result.invoices = invoicesJson
            .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
            .toList();
        debugPrint('🧾 تم استعادة ${result.invoices.length} فاتورة');
      }

      // استعادة الإعدادات
      if (data['settings'] != null) {
        result.settings = data['settings'] as Map<String, dynamic>;
        await _restoreSettings(result.settings);
        debugPrint('⚙️ تم استعادة الإعدادات');
      }

      // استعادة تاريخ العملات
      if (data['currency_history'] != null) {
        await _restoreCurrencyHistory(data['currency_history']);
        debugPrint('💱 تم استعادة تاريخ العملات');
      }

      result.success = true;
      result.metadata = backupData['metadata'] as Map<String, dynamic>;

      debugPrint('✅ تم استعادة البيانات بنجاح');
      return result;
    } catch (e) {
      debugPrint('❌ فشل في استعادة البيانات: $e');
      return BackupRestoreResult()
        ..success = false
        ..error = e.toString();
    }
  }

  /// إنشاء نسخة احتياطية تلقائية
  static Future<void> createAutoBackup() async {
    try {
      final lastBackup = await _getLastBackupTime();
      final now = DateTime.now();

      // إنشاء نسخة احتياطية كل 24 ساعة
      if (lastBackup == null || now.difference(lastBackup).inHours >= 24) {
        debugPrint('🤖 بدء النسخ الاحتياطي التلقائي...');

        final backupFile = await createFullBackup();
        await _saveLastBackupTime(now);
        await _cleanupOldBackups();

        debugPrint(
            '✅ تم إنشاء النسخة الاحتياطية التلقائية: ${backupFile.path}');
      }
    } catch (e) {
      debugPrint('❌ فشل في النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  static Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final directory = await _getBackupDirectory();
      final files = directory
          .listSync()
          .where((file) => file.path.endsWith(_backupExtension))
          .cast<File>()
          .toList();

      final backups = <BackupInfo>[];

      for (var file in files) {
        try {
          final jsonString = await file.readAsString();
          final backupData = jsonDecode(jsonString) as Map<String, dynamic>;
          final metadata = backupData['metadata'] as Map<String, dynamic>;
          final statistics = backupData['statistics'] as Map<String, dynamic>;

          backups.add(BackupInfo(
            file: file,
            createdAt: DateTime.parse(metadata['created_at']),
            version: metadata['version'],
            backupType: metadata['backup_type'],
            totalProducts: statistics['total_products'],
            totalCustomers: statistics['total_customers'],
            totalInvoices: statistics['total_invoices'],
            fileSize: await file.length(),
          ));
        } catch (e) {
          debugPrint('⚠️ تخطي ملف نسخة احتياطية تالف: ${file.path}');
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return backups;
    } catch (e) {
      debugPrint('❌ فشل في الحصول على النسخ الاحتياطية: $e');
      return [];
    }
  }

  /// حذف نسخة احتياطية
  static Future<bool> deleteBackup(File backupFile) async {
    try {
      await backupFile.delete();
      debugPrint('🗑️ تم حذف النسخة الاحتياطية: ${backupFile.path}');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في حذف النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> _cleanupOldBackups() async {
    try {
      final backups = await getAvailableBackups();
      const maxBackups = 10; // الاحتفاظ بآخر 10 نسخ احتياطية

      if (backups.length > maxBackups) {
        final backupsToDelete = backups.skip(maxBackups).toList();

        for (var backup in backupsToDelete) {
          await deleteBackup(backup.file);
        }

        debugPrint('🧹 تم حذف ${backupsToDelete.length} نسخة احتياطية قديمة');
      }
    } catch (e) {
      debugPrint('❌ فشل في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }

  /// حفظ النسخة الاحتياطية في ملف
  static Future<File> _saveBackupToFile(String jsonString) async {
    final directory = await _getBackupDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = '${_backupFileName}_$timestamp$_backupExtension';
    final file = File('${directory.path}/$fileName');

    await file.writeAsString(jsonString);
    return file;
  }

  /// الحصول على مجلد النسخ الاحتياطية
  static Future<Directory> _getBackupDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/backups');

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    return backupDir;
  }

  /// التحقق من صحة النسخة الاحتياطية
  static BackupValidationResult _validateBackup(
      Map<String, dynamic> backupData) {
    try {
      // التحقق من وجود البيانات الأساسية
      if (!backupData.containsKey('metadata') ||
          !backupData.containsKey('data')) {
        return BackupValidationResult(
            false, 'بنية النسخة الاحتياطية غير صحيحة');
      }

      final metadata = backupData['metadata'] as Map<String, dynamic>;

      // التحقق من الإصدار
      if (!metadata.containsKey('version')) {
        return BackupValidationResult(
            false, 'إصدار النسخة الاحتياطية غير محدد');
      }

      return BackupValidationResult(true, null);
    } catch (e) {
      return BackupValidationResult(
          false, 'خطأ في التحقق من النسخة الاحتياطية: $e');
    }
  }

  /// الحصول على جميع الإعدادات
  static Future<Map<String, dynamic>> _getAllSettings() async {
    // هنا يمكن جمع جميع الإعدادات من OfflineStorageService
    return {
      'theme_mode': 'light',
      'language': 'ar',
      'currency_rate': 13500.0,
      // إضافة المزيد من الإعدادات حسب الحاجة
    };
  }

  /// الحصول على تاريخ العملات
  static Future<List<Map<String, dynamic>>> _getCurrencyHistory() async {
    // هنا يمكن جمع تاريخ العملات
    return [];
  }

  /// استعادة الإعدادات
  static Future<void> _restoreSettings(Map<String, dynamic> settings) async {
    for (var entry in settings.entries) {
      await OfflineStorageService.saveSetting(entry.key, entry.value);
    }
  }

  /// استعادة تاريخ العملات
  static Future<void> _restoreCurrencyHistory(dynamic currencyHistory) async {
    // هنا يمكن استعادة تاريخ العملات
  }

  /// الحصول على وقت آخر نسخة احتياطية
  static Future<DateTime?> _getLastBackupTime() async {
    final timeString =
        OfflineStorageService.getSetting<String>('last_backup_time');
    return timeString != null ? DateTime.tryParse(timeString) : null;
  }

  /// حفظ وقت آخر نسخة احتياطية
  static Future<void> _saveLastBackupTime(DateTime time) async {
    await OfflineStorageService.saveSetting(
        'last_backup_time', time.toIso8601String());
  }

  /// تنسيق حجم الملف
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// نتيجة استعادة النسخة الاحتياطية
class BackupRestoreResult {
  bool success = false;
  String? error;
  List<Product> products = [];
  List<Customer> customers = [];
  List<Invoice> invoices = [];
  Map<String, dynamic> settings = {};
  Map<String, dynamic>? metadata;
}

/// معلومات النسخة الاحتياطية
class BackupInfo {
  final File file;
  final DateTime createdAt;
  final String version;
  final String backupType;
  final int totalProducts;
  final int totalCustomers;
  final int totalInvoices;
  final int fileSize;

  BackupInfo({
    required this.file,
    required this.createdAt,
    required this.version,
    required this.backupType,
    required this.totalProducts,
    required this.totalCustomers,
    required this.totalInvoices,
    required this.fileSize,
  });
}

/// نتيجة التحقق من صحة النسخة الاحتياطية
class BackupValidationResult {
  final bool isValid;
  final String? error;

  BackupValidationResult(this.isValid, this.error);
}
