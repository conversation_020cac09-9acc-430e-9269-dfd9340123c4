import 'package:flutter/material.dart';
import '../services/cloud_service.dart';
import '../widgets/loading_widget.dart';

class CloudSettingsScreen extends StatefulWidget {
  const CloudSettingsScreen({super.key});

  @override
  State<CloudSettingsScreen> createState() => _CloudSettingsScreenState();
}

class _CloudSettingsScreenState extends State<CloudSettingsScreen> {
  final _apiKeyController = TextEditingController();
  final _userIdController = TextEditingController();
  bool _isLoading = false;
  bool _isConnected = false;
  List<Map<String, dynamic>> _backups = [];

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkConnection();
    _loadBackups();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _userIdController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    // TODO: تحميل الإعدادات المحفوظة
    setState(() {
      _userIdController.text = CloudService.userId ?? '';
    });
  }

  Future<void> _checkConnection() async {
    setState(() => _isLoading = true);

    try {
      final connected = await CloudService.testConnection();
      setState(() => _isConnected = connected);
    } catch (e) {
      setState(() => _isConnected = false);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadBackups() async {
    if (!CloudService.isEnabled) return;

    try {
      final backups = await CloudService.getBackupList();
      setState(() => _backups = backups);
    } catch (e) {
      debugPrint('فشل في تحميل قائمة النسخ الاحتياطية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات السحابة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري التحقق من الاتصال...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildConnectionStatus(),
                  const SizedBox(height: 24),
                  _buildConnectionSettings(),
                  const SizedBox(height: 24),
                  _buildSyncSection(),
                  const SizedBox(height: 24),
                  _buildBackupSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildConnectionStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الاتصال',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  _isConnected ? Icons.cloud_done : Icons.cloud_off,
                  color: _isConnected ? Colors.green : Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _isConnected ? 'متصل بالسحابة' : 'غير متصل',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _isConnected ? Colors.green : Colors.red,
                            ),
                      ),
                      Text(
                        _isConnected
                            ? 'يمكنك الآن مزامنة البيانات والنسخ الاحتياطي'
                            : 'تحقق من إعدادات الاتصال',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _checkConnection,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'إعادة فحص الاتصال',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الاتصال',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _apiKeyController,
              decoration: const InputDecoration(
                labelText: 'مفتاح API',
                hintText: 'أدخل مفتاح API الخاص بك',
                prefixIcon: Icon(Icons.key),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _userIdController,
              decoration: const InputDecoration(
                labelText: 'معرف المستخدم',
                hintText: 'أدخل معرف المستخدم',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testConnection,
                    icon: const Icon(Icons.wifi_protected_setup),
                    label: const Text('اختبار الاتصال'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _saveSettings,
                    icon: const Icon(Icons.save),
                    label: const Text('حفظ الإعدادات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المزامنة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.sync, color: Colors.blue),
              title: const Text('مزامنة جميع البيانات'),
              subtitle: const Text('مزامنة المنتجات والعملاء والفواتير'),
              trailing: ElevatedButton(
                onPressed: _isConnected ? _syncAllData : null,
                child: const Text('مزامنة'),
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.inventory, color: Colors.green),
              title: const Text('مزامنة المنتجات'),
              subtitle: const Text('مزامنة قائمة المنتجات فقط'),
              trailing: ElevatedButton(
                onPressed: _isConnected ? _syncProducts : null,
                child: const Text('مزامنة'),
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.orange),
              title: const Text('مزامنة العملاء'),
              subtitle: const Text('مزامنة قائمة العملاء فقط'),
              trailing: ElevatedButton(
                onPressed: _isConnected ? _syncCustomers : null,
                child: const Text('مزامنة'),
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.receipt, color: Colors.purple),
              title: const Text('مزامنة الفواتير'),
              subtitle: const Text('مزامنة جميع الفواتير'),
              trailing: ElevatedButton(
                onPressed: _isConnected ? _syncInvoices : null,
                child: const Text('مزامنة'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'النسخ الاحتياطية',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                ElevatedButton.icon(
                  onPressed: _isConnected ? _createBackup : null,
                  icon: const Icon(Icons.backup),
                  label: const Text('نسخة احتياطية'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_backups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(Icons.backup, size: 48, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد نسخ احتياطية',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _backups.length,
                itemBuilder: (context, index) {
                  final backup = _backups[index];
                  return ListTile(
                    leading: const Icon(Icons.backup, color: Colors.blue),
                    title: Text('نسخة احتياطية ${index + 1}'),
                    subtitle: Text(
                      'تاريخ الإنشاء: ${_formatDate(backup['created_at'])}',
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'restore') {
                          _restoreBackup(backup['id']);
                        } else if (value == 'delete') {
                          _deleteBackup(backup['id']);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'restore',
                          child: ListTile(
                            leading: Icon(Icons.restore),
                            title: Text('استعادة'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text('حذف'),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  Future<void> _testConnection() async {
    if (_apiKeyController.text.isEmpty || _userIdController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال مفتاح API ومعرف المستخدم'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      await CloudService.initialize(
        apiKey: _apiKeyController.text,
        userId: _userIdController.text,
      );

      final connected = await CloudService.testConnection();
      setState(() => _isConnected = connected);

      if (mounted) {
        if (connected) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم الاتصال بالسحابة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في الاتصال بالسحابة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() => _isConnected = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    // TODO: حفظ الإعدادات محلياً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ الإعدادات بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _syncAllData() async {
    setState(() => _isLoading = true);

    try {
      final success = await CloudService.syncAllData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'تم مزامنة البيانات بنجاح'
                : 'فشل في مزامنة البيانات'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المزامنة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _syncProducts() async {
    final success = await CloudService.syncProducts();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              success ? 'تم مزامنة المنتجات بنجاح' : 'فشل في مزامنة المنتجات'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _syncCustomers() async {
    final success = await CloudService.syncCustomers();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              success ? 'تم مزامنة العملاء بنجاح' : 'فشل في مزامنة العملاء'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _syncInvoices() async {
    final success = await CloudService.syncInvoices();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              success ? 'تم مزامنة الفواتير بنجاح' : 'فشل في مزامنة الفواتير'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _createBackup() async {
    setState(() => _isLoading = true);

    try {
      final success = await CloudService.createFullBackup();

      if (success) {
        await _loadBackups(); // إعادة تحميل قائمة النسخ الاحتياطية
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'تم إنشاء النسخة الاحتياطية بنجاح'
                : 'فشل في إنشاء النسخة الاحتياطية'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء النسخة الاحتياطية: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _restoreBackup(String backupId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      try {
        final success = await CloudService.restoreFromBackup(backupId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(success
                  ? 'تم استعادة البيانات بنجاح'
                  : 'فشل في استعادة البيانات'),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في الاستعادة: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deleteBackup(String backupId) async {
    // TODO: تنفيذ حذف النسخة الاحتياطية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة حذف النسخة الاحتياطية ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
