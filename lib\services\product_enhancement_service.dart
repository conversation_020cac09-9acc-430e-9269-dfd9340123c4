import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../services/advanced_notification_service.dart';

/// خدمة تحسين إدارة المنتجات
class ProductEnhancementService {
  /// تحديث أسعار المنتجات بناءً على سعر الدولار الجديد
  static Future<List<Product>> updateProductPricesWithDollarRate(
    List<Product> products,
    double newDollarRate,
  ) async {
    final updatedProducts = <Product>[];

    for (var product in products) {
      final updatedProduct = product.updatePricesWithDollarRate(newDollarRate);
      updatedProducts.add(updatedProduct);
    }

    // إرسال إشعار بتحديث الأسعار
    await _sendPriceUpdateNotification(products.length, newDollarRate);

    return updatedProducts;
  }

  /// حساب إحصائيات المنتجات
  static Map<String, dynamic> calculateProductStatistics(
      List<Product> products) {
    if (products.isEmpty) {
      return {
        'total_products': 0,
        'total_value_syp': 0.0,
        'total_value_usd': 0.0,
        'average_profit_margin': 0.0,
        'low_stock_count': 0,
        'out_of_stock_count': 0,
        'categories': <String, int>{},
      };
    }

    final totalProducts = products.length;
    final totalValueSyp = products.fold(0.0,
        (sum, product) => sum + (product.sellingPriceSyp * product.quantity));
    final totalValueUsd = products.fold(0.0,
        (sum, product) => sum + (product.sellingPriceUsd * product.quantity));

    final profitMargins = products.map((p) => p.getProfitMargin()).toList();
    final averageProfitMargin = profitMargins.isNotEmpty
        ? profitMargins.reduce((a, b) => a + b) / profitMargins.length
        : 0.0;

    final lowStockCount = products.where((p) => p.isLowStock()).length;
    final outOfStockCount = products.where((p) => p.isOutOfStock()).length;

    return {
      'total_products': totalProducts,
      'total_value_syp': totalValueSyp,
      'total_value_usd': totalValueUsd,
      'average_profit_margin': averageProfitMargin,
      'low_stock_count': lowStockCount,
      'out_of_stock_count': outOfStockCount,
      'healthy_stock_count': totalProducts - lowStockCount,
      'stock_health_percentage':
          ((totalProducts - lowStockCount) / totalProducts * 100),
    };
  }

  /// الحصول على أفضل المنتجات ربحية
  static List<Product> getMostProfitableProducts(
    List<Product> products, {
    int limit = 10,
  }) {
    final sortedProducts = List<Product>.from(products);
    sortedProducts
        .sort((a, b) => b.getProfitMargin().compareTo(a.getProfitMargin()));
    return sortedProducts.take(limit).toList();
  }

  /// الحصول على المنتجات التي تحتاج إعادة طلب
  static List<Map<String, dynamic>> getReorderRecommendations(
    List<Product> products,
  ) {
    final lowStockProducts = products.where((p) => p.isLowStock()).toList();

    return lowStockProducts.map((product) {
      final recommendedQuantity = _calculateRecommendedQuantity(product);
      final estimatedCostSyp = recommendedQuantity * product.purchasePriceSyp;
      final estimatedCostUsd = recommendedQuantity * product.purchasePriceUsd;

      return {
        'product': product,
        'current_quantity': product.quantity,
        'min_quantity': product.minQuantity,
        'recommended_quantity': recommendedQuantity,
        'estimated_cost_syp': estimatedCostSyp,
        'estimated_cost_usd': estimatedCostUsd,
        'urgency_level': _getUrgencyLevel(product),
        'days_until_stockout': _calculateDaysUntilStockout(product),
      };
    }).toList();
  }

  /// تحليل اتجاهات الأسعار
  static Map<String, dynamic> analyzePriceTrends(
    List<Product> products,
    double currentDollarRate,
    double previousDollarRate,
  ) {
    final priceChanges = <Map<String, dynamic>>[];
    double totalPriceChangePercentage = 0.0;

    for (var product in products) {
      final oldPriceSyp = product.purchaseCurrency == 'USD'
          ? product.purchasePriceUsd * previousDollarRate
          : product.purchasePriceSyp;

      final newPriceSyp = product.purchaseCurrency == 'USD'
          ? product.purchasePriceUsd * currentDollarRate
          : product.purchasePriceSyp;

      final changePercentage = oldPriceSyp > 0
          ? ((newPriceSyp - oldPriceSyp) / oldPriceSyp * 100)
          : 0.0;

      totalPriceChangePercentage += changePercentage;

      priceChanges.add({
        'product': product,
        'old_price_syp': oldPriceSyp,
        'new_price_syp': newPriceSyp,
        'change_percentage': changePercentage,
        'change_amount_syp': newPriceSyp - oldPriceSyp,
      });
    }

    final averageChangePercentage = products.isNotEmpty
        ? totalPriceChangePercentage / products.length
        : 0.0;

    return {
      'average_change_percentage': averageChangePercentage,
      'total_products_affected': products.length,
      'price_changes': priceChanges,
      'dollar_rate_change': currentDollarRate - previousDollarRate,
      'dollar_rate_change_percentage': previousDollarRate > 0
          ? ((currentDollarRate - previousDollarRate) /
              previousDollarRate *
              100)
          : 0.0,
    };
  }

  /// تصدير تقرير المنتجات
  static Map<String, dynamic> exportProductReport(List<Product> products) {
    final statistics = calculateProductStatistics(products);
    final profitableProducts = getMostProfitableProducts(products, limit: 5);
    final reorderRecommendations = getReorderRecommendations(products);

    return {
      'report_date': DateTime.now().toIso8601String(),
      'statistics': statistics,
      'most_profitable_products': profitableProducts
          .map((p) => {
                'name': p.name,
                'profit_margin': p.getProfitMargin(),
                'selling_price_syp': p.sellingPriceSyp,
                'purchase_price_syp': p.purchasePriceSyp,
              })
          .toList(),
      'reorder_recommendations': reorderRecommendations,
      'total_reorder_cost_syp': reorderRecommendations.fold(
          0.0, (sum, item) => sum + item['estimated_cost_syp']),
      'total_reorder_cost_usd': reorderRecommendations.fold(
          0.0, (sum, item) => sum + item['estimated_cost_usd']),
    };
  }

  /// حساب الكمية الموصى بطلبها
  static int _calculateRecommendedQuantity(Product product) {
    // خوارزمية بسيطة: 3 أضعاف الحد الأدنى أو 50 قطعة (أيهما أكبر)
    return (product.minQuantity * 3).clamp(50, 1000);
  }

  /// تحديد مستوى الإلحاح
  static String _getUrgencyLevel(Product product) {
    if (product.quantity <= 0) return 'عاجل جداً';
    if (product.quantity <= product.minQuantity * 0.3) return 'عاجل';
    if (product.quantity <= product.minQuantity * 0.7) return 'متوسط';
    return 'منخفض';
  }

  /// حساب الأيام حتى نفاد المخزون (تقدير بسيط)
  static int _calculateDaysUntilStockout(Product product) {
    // تقدير بسيط: افتراض بيع قطعة واحدة يومياً
    return product.quantity;
  }

  /// إرسال إشعار تحديث الأسعار
  static Future<void> _sendPriceUpdateNotification(
    int productCount,
    double newDollarRate,
  ) async {
    try {
      await AdvancedNotificationService.notifyProductUpdate(
        'تم تحديث $productCount منتج',
        'updated',
      );
    } catch (e) {
      debugPrint('فشل في إرسال إشعار تحديث الأسعار: $e');
    }
  }

  /// فحص صحة بيانات المنتج
  static Map<String, dynamic> validateProduct(Product product) {
    final errors = <String>[];
    final warnings = <String>[];

    // فحص الاسم
    if (product.name.trim().isEmpty) {
      errors.add('اسم المنتج مطلوب');
    } else if (product.name.trim().length < 2) {
      warnings.add('اسم المنتج قصير جداً');
    }

    // فحص الباركود
    if (product.barcode?.trim().isEmpty ?? true) {
      warnings.add('الباركود غير محدد');
    }

    // فحص الأسعار
    if (product.purchasePrice <= 0) {
      errors.add('سعر الشراء يجب أن يكون أكبر من صفر');
    }

    if (product.salePrice <= 0) {
      errors.add('سعر البيع يجب أن يكون أكبر من صفر');
    }

    if (product.salePrice <= product.purchasePrice) {
      warnings.add('سعر البيع أقل من أو يساوي سعر الشراء (لا يوجد ربح)');
    }

    // فحص الكميات
    if (product.quantity < 0) {
      errors.add('الكمية لا يمكن أن تكون سالبة');
    }

    if (product.minQuantity < 0) {
      errors.add('الحد الأدنى للكمية لا يمكن أن يكون سالب');
    }

    if (product.quantity <= product.minQuantity) {
      warnings.add('الكمية الحالية أقل من أو تساوي الحد الأدنى');
    }

    return {
      'is_valid': errors.isEmpty,
      'errors': errors,
      'warnings': warnings,
      'has_warnings': warnings.isNotEmpty,
    };
  }
}
