import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

/// خدمة النسخ الاحتياطي الذكي
class SmartBackupService {
  static const String _backupSettingsBox = 'backup_settings';
  static const String _backupHistoryBox = 'backup_history';

  /// تهيئة خدمة النسخ الاحتياطي
  static Future<void> initialize() async {
    try {
      await _setDefaultBackupSettings();
      await _scheduleAutoBackup();
      debugPrint('✅ تم تهيئة خدمة النسخ الاحتياطي الذكي');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة النسخ الاحتياطي: $e');
    }
  }

  /// إنشاء نسخة احتياطية كاملة
  static Future<BackupResult> createFullBackup({
    String? customName,
    bool includeSettings = true,
    bool compress = true,
  }) async {
    try {
      final backupId = DateTime.now().millisecondsSinceEpoch.toString();
      final backupName = customName ?? 'backup_${DateTime.now().toIso8601String().split('T')[0]}';
      
      // جمع جميع البيانات
      final backupData = <String, dynamic>{
        'metadata': {
          'backup_id': backupId,
          'backup_name': backupName,
          'created_at': DateTime.now().toIso8601String(),
          'app_version': '1.0.0',
          'backup_type': 'full',
          'include_settings': includeSettings,
        },
        'data': {},
      };

      // البيانات الأساسية
      final boxes = [
        'products',
        'customers',
        'invoices',
        'accounts',
        'journal_entries',
        'companies',
        'exchange_rates',
        'currency_history',
      ];

      for (var boxName in boxes) {
        try {
          final boxData = await OfflineDatabaseService.getBoxData(boxName);
          backupData['data'][boxName] = boxData;
        } catch (e) {
          debugPrint('⚠️ تخطي صندوق $boxName: $e');
          backupData['data'][boxName] = {};
        }
      }

      // الإعدادات (اختياري)
      if (includeSettings) {
        try {
          final settingsBoxes = [
            'settings',
            'notification_settings',
            'backup_settings',
          ];
          
          for (var boxName in settingsBoxes) {
            final boxData = await OfflineDatabaseService.getBoxData(boxName);
            backupData['data'][boxName] = boxData;
          }
        } catch (e) {
          debugPrint('⚠️ فشل في نسخ الإعدادات: $e');
        }
      }

      // حفظ النسخة الاحتياطية
      final backupFile = await _saveBackupToFile(backupData, backupName, compress);
      
      // حفظ في السجل
      final backupRecord = BackupRecord(
        id: backupId,
        name: backupName,
        type: BackupType.full,
        filePath: backupFile.path,
        fileSize: await backupFile.length(),
        createdAt: DateTime.now(),
        includeSettings: includeSettings,
        compressed: compress,
      );

      await _saveBackupRecord(backupRecord);

      // إشعار النجاح
      await OfflineNotificationService.showSuccessNotification(
        'تم إنشاء النسخة الاحتياطية "$backupName" بنجاح',
      );

      return BackupResult(
        success: true,
        backupId: backupId,
        filePath: backupFile.path,
        fileSize: backupRecord.fileSize,
        message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      );

    } catch (e) {
      debugPrint('❌ فشل في إنشاء النسخة الاحتياطية: $e');
      
      await OfflineNotificationService.showErrorNotification(
        'فشل في إنشاء النسخة الاحتياطية: ${e.toString()}',
      );

      return BackupResult(
        success: false,
        message: 'فشل في إنشاء النسخة الاحتياطية: ${e.toString()}',
      );
    }
  }

  /// إنشاء نسخة احتياطية تزايدية (البيانات المتغيرة فقط)
  static Future<BackupResult> createIncrementalBackup() async {
    try {
      final lastBackup = await _getLastBackupDate();
      final backupId = DateTime.now().millisecondsSinceEpoch.toString();
      final backupName = 'incremental_${DateTime.now().toIso8601String().split('T')[0]}';

      final backupData = <String, dynamic>{
        'metadata': {
          'backup_id': backupId,
          'backup_name': backupName,
          'created_at': DateTime.now().toIso8601String(),
          'backup_type': 'incremental',
          'since_date': lastBackup?.toIso8601String(),
        },
        'data': {},
      };

      // جمع البيانات المتغيرة فقط
      final boxes = ['products', 'customers', 'invoices', 'journal_entries'];
      
      for (var boxName in boxes) {
        final changedData = await _getChangedDataSince(boxName, lastBackup);
        if (changedData.isNotEmpty) {
          backupData['data'][boxName] = changedData;
        }
      }

      if (backupData['data'].isEmpty) {
        return BackupResult(
          success: true,
          message: 'لا توجد تغييرات منذ آخر نسخة احتياطية',
        );
      }

      final backupFile = await _saveBackupToFile(backupData, backupName, true);
      
      final backupRecord = BackupRecord(
        id: backupId,
        name: backupName,
        type: BackupType.incremental,
        filePath: backupFile.path,
        fileSize: await backupFile.length(),
        createdAt: DateTime.now(),
        compressed: true,
      );

      await _saveBackupRecord(backupRecord);

      return BackupResult(
        success: true,
        backupId: backupId,
        filePath: backupFile.path,
        fileSize: backupRecord.fileSize,
        message: 'تم إنشاء النسخة الاحتياطية التزايدية بنجاح',
      );

    } catch (e) {
      debugPrint('❌ فشل في إنشاء النسخة الاحتياطية التزايدية: $e');
      return BackupResult(
        success: false,
        message: 'فشل في إنشاء النسخة الاحتياطية التزايدية: ${e.toString()}',
      );
    }
  }

  /// استعادة من نسخة احتياطية
  static Future<RestoreResult> restoreFromBackup(String backupFilePath) async {
    try {
      // قراءة ملف النسخة الاحتياطية
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final backupContent = await backupFile.readAsString();
      final backupData = jsonDecode(backupContent);

      // التحقق من صحة النسخة الاحتياطية
      if (!_validateBackupData(backupData)) {
        throw Exception('ملف النسخة الاحتياطية تالف أو غير صحيح');
      }

      final metadata = backupData['metadata'];
      final data = backupData['data'];

      // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
      await createFullBackup(
        customName: 'before_restore_${DateTime.now().millisecondsSinceEpoch}',
      );

      // استعادة البيانات
      int restoredBoxes = 0;
      for (var boxName in data.keys) {
        try {
          await OfflineDatabaseService.clearBox(boxName);
          
          final boxData = Map<String, dynamic>.from(data[boxName]);
          for (var entry in boxData.entries) {
            await OfflineDatabaseService.saveBoxItem(boxName, entry.key, entry.value);
          }
          
          restoredBoxes++;
          debugPrint('✅ تم استعادة صندوق: $boxName');
        } catch (e) {
          debugPrint('❌ فشل في استعادة صندوق $boxName: $e');
        }
      }

      // حفظ سجل الاستعادة
      await _saveRestoreRecord(
        backupId: metadata['backup_id'],
        backupName: metadata['backup_name'],
        restoredBoxes: restoredBoxes,
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم استعادة البيانات من "${metadata['backup_name']}" بنجاح',
      );

      return RestoreResult(
        success: true,
        backupName: metadata['backup_name'],
        restoredItems: restoredBoxes,
        message: 'تم استعادة البيانات بنجاح',
      );

    } catch (e) {
      debugPrint('❌ فشل في استعادة النسخة الاحتياطية: $e');
      
      await OfflineNotificationService.showErrorNotification(
        'فشل في استعادة النسخة الاحتياطية: ${e.toString()}',
      );

      return RestoreResult(
        success: false,
        message: 'فشل في استعادة النسخة الاحتياطية: ${e.toString()}',
      );
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  static Future<List<BackupRecord>> getBackupHistory() async {
    try {
      final historyData = await OfflineDatabaseService.getBoxData(_backupHistoryBox);
      final backups = historyData.values
          .map((data) => BackupRecord.fromJson(Map<String, dynamic>.from(data)))
          .toList();

      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return backups;
    } catch (e) {
      debugPrint('❌ فشل في جلب سجل النسخ الاحتياطية: $e');
      return [];
    }
  }

  /// حذف نسخة احتياطية
  static Future<bool> deleteBackup(String backupId) async {
    try {
      final backup = await _getBackupRecord(backupId);
      if (backup == null) return false;

      // حذف الملف
      final backupFile = File(backup.filePath);
      if (await backupFile.exists()) {
        await backupFile.delete();
      }

      // حذف من السجل
      await OfflineDatabaseService.deleteBoxItem(_backupHistoryBox, backupId);

      return true;
    } catch (e) {
      debugPrint('❌ فشل في حذف النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> cleanupOldBackups({int keepCount = 10}) async {
    try {
      final backups = await getBackupHistory();
      
      if (backups.length > keepCount) {
        final backupsToDelete = backups.skip(keepCount).toList();
        
        for (var backup in backupsToDelete) {
          await deleteBackup(backup.id);
        }
        
        debugPrint('✅ تم حذف ${backupsToDelete.length} نسخة احتياطية قديمة');
      }
    } catch (e) {
      debugPrint('❌ فشل في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }

  // وظائف مساعدة
  static Future<File> _saveBackupToFile(
    Map<String, dynamic> backupData,
    String backupName,
    bool compress,
  ) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupsDir = Directory('${directory.path}/backups');
    
    if (!await backupsDir.exists()) {
      await backupsDir.create(recursive: true);
    }

    final fileName = '${backupName}_${DateTime.now().millisecondsSinceEpoch}.json';
    final backupFile = File('${backupsDir.path}/$fileName');

    final jsonString = jsonEncode(backupData);
    
    if (compress) {
      // يمكن إضافة ضغط هنا إذا لزم الأمر
      await backupFile.writeAsString(jsonString);
    } else {
      await backupFile.writeAsString(jsonString);
    }

    return backupFile;
  }

  static Future<void> _saveBackupRecord(BackupRecord record) async {
    await OfflineDatabaseService.saveBoxItem(
      _backupHistoryBox,
      record.id,
      record.toJson(),
    );
  }

  static Future<BackupRecord?> _getBackupRecord(String backupId) async {
    final data = await OfflineDatabaseService.getBoxItem(_backupHistoryBox, backupId);
    return data != null ? BackupRecord.fromJson(Map<String, dynamic>.from(data)) : null;
  }

  static Future<DateTime?> _getLastBackupDate() async {
    final backups = await getBackupHistory();
    return backups.isNotEmpty ? backups.first.createdAt : null;
  }

  static Future<Map<String, dynamic>> _getChangedDataSince(
    String boxName,
    DateTime? sinceDate,
  ) async {
    if (sinceDate == null) {
      return await OfflineDatabaseService.getBoxData(boxName);
    }

    // هنا يمكن تنفيذ منطق للحصول على البيانات المتغيرة فقط
    // حالياً نعيد جميع البيانات
    return await OfflineDatabaseService.getBoxData(boxName);
  }

  static bool _validateBackupData(Map<String, dynamic> backupData) {
    return backupData.containsKey('metadata') && 
           backupData.containsKey('data') &&
           backupData['metadata']['backup_id'] != null;
  }

  static Future<void> _saveRestoreRecord({
    required String backupId,
    required String backupName,
    required int restoredBoxes,
  }) async {
    final restoreRecord = {
      'backup_id': backupId,
      'backup_name': backupName,
      'restored_at': DateTime.now().toIso8601String(),
      'restored_boxes': restoredBoxes,
    };

    await OfflineDatabaseService.saveBoxItem(
      'restore_history',
      DateTime.now().millisecondsSinceEpoch.toString(),
      restoreRecord,
    );
  }

  static Future<void> _setDefaultBackupSettings() async {
    final defaultSettings = {
      'auto_backup_enabled': true,
      'auto_backup_frequency': 'daily', // daily, weekly, monthly
      'keep_backup_count': 10,
      'include_settings': true,
      'compress_backups': true,
      'backup_on_app_close': true,
    };

    await OfflineDatabaseService.saveBoxItem(
      _backupSettingsBox,
      'default',
      defaultSettings,
    );
  }

  static Future<void> _scheduleAutoBackup() async {
    // هنا يمكن إضافة جدولة النسخ الاحتياطية التلقائية
    debugPrint('📅 تم جدولة النسخ الاحتياطية التلقائية');
  }
}

/// نموذج سجل النسخة الاحتياطية
class BackupRecord {
  final String id;
  final String name;
  final BackupType type;
  final String filePath;
  final int fileSize;
  final DateTime createdAt;
  final bool includeSettings;
  final bool compressed;

  BackupRecord({
    required this.id,
    required this.name,
    required this.type,
    required this.filePath,
    required this.fileSize,
    required this.createdAt,
    this.includeSettings = true,
    this.compressed = false,
  });

  factory BackupRecord.fromJson(Map<String, dynamic> json) {
    return BackupRecord(
      id: json['id'],
      name: json['name'],
      type: BackupType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => BackupType.full,
      ),
      filePath: json['file_path'],
      fileSize: json['file_size'],
      createdAt: DateTime.parse(json['created_at']),
      includeSettings: json['include_settings'] ?? true,
      compressed: json['compressed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.toString().split('.').last,
      'file_path': filePath,
      'file_size': fileSize,
      'created_at': createdAt.toIso8601String(),
      'include_settings': includeSettings,
      'compressed': compressed,
    };
  }

  String get formattedSize {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// أنواع النسخ الاحتياطية
enum BackupType {
  full,         // كاملة
  incremental,  // تزايدية
  differential, // تفاضلية
}

/// نتيجة النسخ الاحتياطي
class BackupResult {
  final bool success;
  final String? backupId;
  final String? filePath;
  final int? fileSize;
  final String message;

  BackupResult({
    required this.success,
    this.backupId,
    this.filePath,
    this.fileSize,
    required this.message,
  });
}

/// نتيجة الاستعادة
class RestoreResult {
  final bool success;
  final String? backupName;
  final int? restoredItems;
  final String message;

  RestoreResult({
    required this.success,
    this.backupName,
    this.restoredItems,
    required this.message,
  });
}
