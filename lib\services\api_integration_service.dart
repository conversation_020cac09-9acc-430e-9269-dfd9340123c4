import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/api_integration.dart';
import '../models/invoice.dart';
import '../models/product.dart';

import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

/// خدمة التكامل مع APIs الخارجية
class APIIntegrationService {
  static const String _integrationsBox = 'api_integrations';
  static const String _syncLogsBox = 'sync_logs';

  /// تهيئة خدمة التكامل
  static Future<void> initialize() async {
    try {
      debugPrint('✅ تم تهيئة خدمة التكامل الخارجي');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة خدمة التكامل: $e');
      rethrow;
    }
  }

  // ==================== إدارة التكاملات ====================

  /// الحصول على جميع التكاملات
  static Future<List<APIIntegration>> getAllIntegrations() async {
    try {
      final data = await OfflineDatabaseService.getBoxData(_integrationsBox);
      final integrations = <APIIntegration>[];

      for (var item in data.values) {
        integrations
            .add(APIIntegration.fromJson(Map<String, dynamic>.from(item)));
      }

      return integrations;
    } catch (e) {
      debugPrint('❌ فشل في جلب التكاملات: $e');
      return [];
    }
  }

  /// إضافة تكامل جديد
  static Future<APIIntegration> addIntegration(
      APIIntegration integration) async {
    try {
      final newId = await _getNextIntegrationId();
      final newIntegration = integration.copyWith(
        id: newId,
        createdAt: DateTime.now(),
      );

      await OfflineDatabaseService.saveBoxItem(
        _integrationsBox,
        newId.toString(),
        newIntegration.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة التكامل "${newIntegration.name}" بنجاح',
      );

      debugPrint('✅ تم إضافة التكامل: ${newIntegration.name}');
      return newIntegration;
    } catch (e) {
      debugPrint('❌ فشل في إضافة التكامل: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة التكامل: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث تكامل
  static Future<APIIntegration> updateIntegration(
      APIIntegration integration) async {
    try {
      await OfflineDatabaseService.saveBoxItem(
        _integrationsBox,
        integration.id.toString(),
        integration.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث التكامل "${integration.name}" بنجاح',
      );

      debugPrint('✅ تم تحديث التكامل: ${integration.name}');
      return integration;
    } catch (e) {
      debugPrint('❌ فشل في تحديث التكامل: $e');
      rethrow;
    }
  }

  /// حذف تكامل
  static Future<void> deleteIntegration(int integrationId) async {
    try {
      await OfflineDatabaseService.deleteBoxItem(
          _integrationsBox, integrationId.toString());

      await OfflineNotificationService.showSuccessNotification(
        'تم حذف التكامل بنجاح',
      );

      debugPrint('✅ تم حذف التكامل: $integrationId');
    } catch (e) {
      debugPrint('❌ فشل في حذف التكامل: $e');
      rethrow;
    }
  }

  /// اختبار التكامل
  static Future<bool> testIntegration(APIIntegration integration) async {
    try {
      debugPrint('اختبار التكامل: ${integration.name}');

      final response = await _makeAPICall(
        integration: integration,
        endpoint: 'test',
        method: 'GET',
        data: {},
      );

      final success = response['success'] ?? false;

      if (success) {
        await updateIntegration(integration.copyWith(
          status: IntegrationStatus.active,
          lastSyncAt: DateTime.now(),
          lastError: null,
        ));

        await OfflineNotificationService.showSuccessNotification(
          'تم اختبار التكامل "${integration.name}" بنجاح',
        );
      } else {
        await updateIntegration(integration.copyWith(
          status: IntegrationStatus.error,
          lastError: response['error'] ?? 'فشل في الاختبار',
        ));

        await OfflineNotificationService.showErrorNotification(
          'فشل في اختبار التكامل "${integration.name}"',
        );
      }

      return success;
    } catch (e) {
      debugPrint('❌ فشل في اختبار التكامل: $e');

      await updateIntegration(integration.copyWith(
        status: IntegrationStatus.error,
        lastError: e.toString(),
      ));

      return false;
    }
  }

  // ==================== عمليات المزامنة ====================

  /// مزامنة المنتجات
  static Future<bool> syncProducts(APIIntegration integration) async {
    try {
      if (!integration.supportsOperation('sync_products')) {
        throw Exception('التكامل لا يدعم مزامنة المنتجات');
      }

      debugPrint('مزامنة المنتجات مع: ${integration.name}');

      final products = await OfflineDatabaseService.getAllProducts();
      final productsData = products.map((p) => p.toJson()).toList();

      final response = await _makeAPICall(
        integration: integration,
        endpoint: 'products/sync',
        method: 'POST',
        data: {'products': productsData},
      );

      await _logSync(
        integrationId: integration.id,
        operation: 'sync_products',
        direction: 'outbound',
        data: {'count': products.length},
        success: response['success'] ?? false,
        error: response['error'],
        responseCode: response['status_code'],
        responseMessage: response['message'],
      );

      if (response['success'] == true) {
        await updateIntegration(integration.copyWith(
          lastSyncAt: DateTime.now(),
          syncCount: integration.syncCount + 1,
          status: IntegrationStatus.active,
          lastError: null,
        ));

        await OfflineNotificationService.showSuccessNotification(
          'تم مزامنة ${products.length} منتج مع ${integration.name}',
        );

        return true;
      } else {
        throw Exception(response['error'] ?? 'فشل في المزامنة');
      }
    } catch (e) {
      debugPrint('❌ فشل في مزامنة المنتجات: $e');

      await updateIntegration(integration.copyWith(
        status: IntegrationStatus.error,
        lastError: e.toString(),
      ));

      await OfflineNotificationService.showErrorNotification(
        'فشل في مزامنة المنتجات: ${e.toString()}',
      );

      return false;
    }
  }

  /// مزامنة الطلبات
  static Future<bool> syncOrders(APIIntegration integration) async {
    try {
      if (!integration.supportsOperation('sync_orders')) {
        throw Exception('التكامل لا يدعم مزامنة الطلبات');
      }

      debugPrint('مزامنة الطلبات مع: ${integration.name}');

      final invoices = await OfflineDatabaseService.getAllInvoices();
      final ordersData =
          invoices.map((i) => _convertInvoiceToOrder(i)).toList();

      final response = await _makeAPICall(
        integration: integration,
        endpoint: 'orders/sync',
        method: 'POST',
        data: {'orders': ordersData},
      );

      await _logSync(
        integrationId: integration.id,
        operation: 'sync_orders',
        direction: 'outbound',
        data: {'count': invoices.length},
        success: response['success'] ?? false,
        error: response['error'],
        responseCode: response['status_code'],
        responseMessage: response['message'],
      );

      if (response['success'] == true) {
        await updateIntegration(integration.copyWith(
          lastSyncAt: DateTime.now(),
          syncCount: integration.syncCount + 1,
          status: IntegrationStatus.active,
          lastError: null,
        ));

        await OfflineNotificationService.showSuccessNotification(
          'تم مزامنة ${invoices.length} طلب مع ${integration.name}',
        );

        return true;
      } else {
        throw Exception(response['error'] ?? 'فشل في المزامنة');
      }
    } catch (e) {
      debugPrint('❌ فشل في مزامنة الطلبات: $e');

      await updateIntegration(integration.copyWith(
        status: IntegrationStatus.error,
        lastError: e.toString(),
      ));

      return false;
    }
  }

  /// تحديث المخزون
  static Future<bool> updateInventory(
      APIIntegration integration, List<Product> products) async {
    try {
      if (!integration.supportsOperation('update_inventory')) {
        throw Exception('التكامل لا يدعم تحديث المخزون');
      }

      debugPrint('تحديث المخزون مع: ${integration.name}');

      final inventoryData = products
          .map((p) => {
                'product_id': p.id,
                'sku': p.id.toString(),
                'quantity': p.quantity,
                'price': p.salePrice,
              })
          .toList();

      final response = await _makeAPICall(
        integration: integration,
        endpoint: 'inventory/update',
        method: 'PUT',
        data: {'inventory': inventoryData},
      );

      await _logSync(
        integrationId: integration.id,
        operation: 'update_inventory',
        direction: 'outbound',
        data: {'count': products.length},
        success: response['success'] ?? false,
        error: response['error'],
        responseCode: response['status_code'],
        responseMessage: response['message'],
      );

      return response['success'] == true;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المخزون: $e');
      return false;
    }
  }

  // ==================== وظائف مساعدة ====================

  /// إجراء استدعاء API
  static Future<Map<String, dynamic>> _makeAPICall({
    required APIIntegration integration,
    required String endpoint,
    required String method,
    required Map<String, dynamic> data,
  }) async {
    try {
      final url = Uri.parse('${integration.baseUrl}$endpoint');
      final headers = Map<String, String>.from(integration.headers);

      // إضافة مفتاح API
      headers['Authorization'] = 'Bearer ${integration.apiKey}';

      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(url, headers: headers);
          break;
        case 'POST':
          response = await http.post(
            url,
            headers: headers,
            body: jsonEncode(data),
          );
          break;
        case 'PUT':
          response = await http.put(
            url,
            headers: headers,
            body: jsonEncode(data),
          );
          break;
        case 'DELETE':
          response = await http.delete(url, headers: headers);
          break;
        default:
          throw Exception('HTTP method غير مدعوم: $method');
      }

      final responseData = jsonDecode(response.body);

      return {
        'success': response.statusCode >= 200 && response.statusCode < 300,
        'status_code': response.statusCode,
        'data': responseData,
        'message': responseData['message'] ?? 'تم بنجاح',
        'error': response.statusCode >= 400
            ? responseData['error'] ?? 'خطأ في الخادم'
            : null,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'status_code': 0,
      };
    }
  }

  /// تسجيل عملية المزامنة
  static Future<void> _logSync({
    required int integrationId,
    required String operation,
    required String direction,
    required Map<String, dynamic> data,
    required bool success,
    String? error,
    int? responseCode,
    String? responseMessage,
  }) async {
    try {
      final logId = await _getNextLogId();

      final log = SyncLog(
        id: logId,
        integrationId: integrationId,
        operation: operation,
        direction: direction,
        data: data,
        success: success,
        error: error,
        timestamp: DateTime.now(),
        responseCode: responseCode,
        responseMessage: responseMessage,
      );

      await OfflineDatabaseService.saveBoxItem(
        _syncLogsBox,
        logId.toString(),
        log.toJson(),
      );
    } catch (e) {
      debugPrint('❌ فشل في تسجيل المزامنة: $e');
    }
  }

  /// تحويل الفاتورة إلى طلب
  static Map<String, dynamic> _convertInvoiceToOrder(Invoice invoice) {
    return {
      'id': invoice.id,
      'customer_id': invoice.customerId,
      'customer_name': invoice.customerName,
      'date': invoice.date.toIso8601String(),
      'total': invoice.totalAmountSyp,
      'currency': 'SYP',
      'payment_type': invoice.paymentType.toString().split('.').last,
      'status': invoice.status.toString().split('.').last,
      'items': invoice.items
          .map((item) => {
                'product_id': item.productId,
                'quantity': item.quantity,
                'price': item.price,
                'total': item.price * item.quantity,
              })
          .toList(),
    };
  }

  /// الحصول على معرف تكامل جديد
  static Future<int> _getNextIntegrationId() async {
    final integrations = await getAllIntegrations();
    if (integrations.isEmpty) return 1;

    final maxId = integrations.map((i) => i.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// الحصول على معرف سجل جديد
  static Future<int> _getNextLogId() async {
    final data = await OfflineDatabaseService.getBoxData(_syncLogsBox);
    if (data.isEmpty) return 1;

    int maxId = 0;
    for (var item in data.values) {
      final log = SyncLog.fromJson(Map<String, dynamic>.from(item));
      if (log.id > maxId) maxId = log.id;
    }

    return maxId + 1;
  }

  /// الحصول على سجلات المزامنة
  static Future<List<SyncLog>> getSyncLogs({int? integrationId}) async {
    try {
      final data = await OfflineDatabaseService.getBoxData(_syncLogsBox);
      final logs = <SyncLog>[];

      for (var item in data.values) {
        final log = SyncLog.fromJson(Map<String, dynamic>.from(item));
        if (integrationId == null || log.integrationId == integrationId) {
          logs.add(log);
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return logs;
    } catch (e) {
      debugPrint('❌ فشل في جلب سجلات المزامنة: $e');
      return [];
    }
  }
}
