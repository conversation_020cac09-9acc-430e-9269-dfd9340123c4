import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/product_provider.dart';
import 'providers/customer_provider.dart';
import 'providers/invoice_provider.dart';
import 'providers/report_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/currency_provider.dart';
import 'providers/warehouse_provider.dart';
import 'screens/login_screen.dart';
import 'screens/home_screen.dart';
import 'screens/products_screen.dart';
import 'screens/customers_screen.dart';
import 'screens/invoices_screen.dart';
import 'screens/create_invoice_screen.dart';
import 'screens/reports_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/accounts_screen.dart';
import 'screens/financial_reports_screen.dart';
import 'screens/companies_screen.dart';
import 'screens/ai_insights_screen.dart';
import 'services/offline_storage_service.dart';
import 'services/offline_database_service.dart';
import 'services/security_service.dart';
import 'services/warehouse_service.dart';
import 'services/batch_service.dart';
import 'services/api_integration_service.dart';
import 'utils/app_config.dart';
import 'screens/activation_screen.dart';
import 'screens/pin_screen.dart';
import 'screens/dollar_history_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/simple_home_screen.dart';
import 'screens/warehouses_screen.dart';
import 'screens/cloud_settings_screen.dart';
import 'models/invoice.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // طباعة معلومات التطبيق (للتطوير)
  AppConfig.printAppInfo();

  // التحقق من صحة الإعدادات
  if (!AppConfig.validateConfig()) {
    debugPrint('تحذير: إعدادات التطبيق غير صحيحة');
  }

  // تهيئة الخدمات الأساسية فقط
  try {
    await OfflineStorageService.initialize();
    await OfflineDatabaseService.initialize();
    await WarehouseService.initialize();
    await BatchService.initialize();
    await APIIntegrationService.initialize();
  } catch (e) {
    debugPrint('خطأ في تهيئة الخدمات: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  String _getInitialRoute() {
    try {
      final securityStatus = SecurityService.getSecurityStatus();

      switch (securityStatus) {
        case SecurityStatus.needsActivation:
          return '/activation';
        case SecurityStatus.needsPinSetup:
          return '/pin-setup';
        case SecurityStatus.needsPinVerification:
          return '/pin-verify';
        case SecurityStatus.authenticated:
          return '/home'; // انتقال مباشر للشاشة الرئيسية
      }
    } catch (e) {
      debugPrint('خطأ في فحص الأمان: $e');
      return '/home'; // في حالة الخطأ، انتقل للشاشة الرئيسية مباشرة
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => CurrencyProvider()),
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => InvoiceProvider()),
        ChangeNotifierProvider(create: (_) => ReportProvider()),
        ChangeNotifierProvider(create: (_) => WarehouseProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'نظام الفواتير',
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode:
                themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            initialRoute: _getInitialRoute(),
            routes: {
              '/splash': (context) => const SplashScreen(),
              '/activation': (context) {
                final args = ModalRoute.of(context)?.settings.arguments
                    as Map<String, dynamic>?;
                return ActivationScreen(isReset: args?['isReset'] ?? false);
              },
              '/pin-setup': (context) => const PinScreen(isSetup: true),
              '/pin-verify': (context) => const PinScreen(isSetup: false),
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const SimpleHomeScreen(),
              '/home-full': (context) => const HomeScreen(),
              '/products': (context) => const ProductsScreen(),
              '/customers': (context) => const CustomersScreen(),
              '/invoices': (context) => const InvoicesScreen(),
              '/create-invoice': (context) => const CreateInvoiceScreen(),
              '/edit-invoice': (context) {
                final invoice =
                    ModalRoute.of(context)!.settings.arguments as Invoice?;
                return CreateInvoiceScreen(invoice: invoice);
              },
              '/reports': (context) => const ReportsScreen(),
              '/settings': (context) => const SettingsScreen(),
              '/accounts': (context) => const AccountsScreen(),
              '/financial-reports': (context) => const FinancialReportsScreen(),
              '/companies': (context) => const CompaniesScreen(),
              '/ai-insights': (context) => const AIInsightsScreen(),
              '/dollar-history': (context) => const DollarHistoryScreen(),
              '/warehouses': (context) => const WarehousesScreen(),
              '/cloud-settings': (context) => const CloudSettingsScreen(),
            },
          );
        },
      ),
    );
  }
}
