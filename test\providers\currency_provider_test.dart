import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_system/providers/currency_provider.dart';

void main() {
  group('CurrencyProvider Tests', () {
    late CurrencyProvider currencyProvider;

    setUp(() {
      currencyProvider = CurrencyProvider();
    });

    group('Dollar Rate Management', () {
      test('should have default dollar rate of 13500', () {
        // Assert
        expect(currencyProvider.dollarRate, 13500.0);
      });
    });

    group('Currency Conversion', () {
      test('should convert SYP to USD correctly', () {
        // Arrange
        const sypAmount = 135000.0;
        const expectedUsd = 10.0; // 135000 / 13500

        // Act
        final result = currencyProvider.convertSypToUsd(sypAmount);

        // Assert
        expect(result, closeTo(expectedUsd, 0.01));
      });

      test('should convert USD to SYP correctly', () {
        // Arrange
        const usdAmount = 10.0;
        const expectedSyp = 135000.0; // 10 * 13500

        // Act
        final result = currencyProvider.convertUsdToSyp(usdAmount);

        // Assert
        expect(result, expectedSyp);
      });

      test('should handle zero amounts in conversion', () {
        // Act
        final sypToUsd = currencyProvider.convertSypToUsd(0);
        final usdToSyp = currencyProvider.convertUsdToSyp(0);

        // Assert
        expect(sypToUsd, 0);
        expect(usdToSyp, 0);
      });

      test('should handle negative amounts in conversion', () {
        // Arrange
        const negativeSyp = -1000.0;
        const negativeUsd = -10.0;

        // Act
        final sypToUsd = currencyProvider.convertSypToUsd(negativeSyp);
        final usdToSyp = currencyProvider.convertUsdToSyp(negativeUsd);

        // Assert
        expect(sypToUsd, isNegative);
        expect(usdToSyp, isNegative);
      });
    });
  });
}
