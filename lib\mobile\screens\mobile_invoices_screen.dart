import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/invoice_provider.dart';
import '../mobile_main.dart';

class MobileInvoicesScreen extends StatefulWidget {
  const MobileInvoicesScreen({super.key});

  @override
  State<MobileInvoicesScreen> createState() => _MobileInvoicesScreenState();
}

class _MobileInvoicesScreenState extends State<MobileInvoicesScreen> {
  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    await context.read<InvoiceProvider>().fetchInvoices();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MobileAppBar(
        title: 'الفواتير',
        automaticallyImplyLeading: false,
      ),
      body: Consumer<InvoiceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const MobileLoadingWidget(message: 'جاري تحميل الفواتير...');
          }

          if (provider.invoices.isEmpty) {
            return _buildEmptyState();
          }

          return RefreshIndicator(
            onRefresh: _loadInvoices,
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: provider.invoices.length,
              itemBuilder: (context, index) {
                final invoice = provider.invoices[index];
                return MobileCard(
                  child: MobileListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(invoice.status).withValues(alpha: 0.1),
                      child: Icon(
                        Icons.receipt,
                        color: _getStatusColor(invoice.status),
                      ),
                    ),
                    title: Text(
                      'فاتورة #${invoice.id}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('العميل: ${invoice.customerName}'),
                        Text('المبلغ: ${invoice.totalAmountSyp.toStringAsFixed(0)} ل.س'),
                        Text('التاريخ: ${_formatDate(invoice.date)}'),
                      ],
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(invoice.status).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(invoice.status),
                        style: TextStyle(
                          color: _getStatusColor(invoice.status),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    onTap: () => _showInvoiceDetails(invoice),
                  ),
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: MobileFloatingActionButton(
        onPressed: _createInvoice,
        icon: Icons.add,
        tooltip: 'إنشاء فاتورة',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فواتير',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء فاتورة جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createInvoice,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء فاتورة'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(status) {
    // TODO: تحديد الألوان حسب حالة الفاتورة
    return Colors.blue;
  }

  String _getStatusText(status) {
    // TODO: تحديد النص حسب حالة الفاتورة
    return 'مكتملة';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showInvoiceDetails(invoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('فاتورة #${invoice.id}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('العميل', invoice.customerName),
            _buildDetailRow('التاريخ', _formatDate(invoice.date)),
            _buildDetailRow('المبلغ', '${invoice.totalAmountSyp.toStringAsFixed(0)} ل.س'),
            _buildDetailRow('عدد الأصناف', '${invoice.items.length}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  void _createInvoice() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة إنشاء فاتورة ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
