import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/journal_entry.dart';
import '../services/offline_database_service.dart';
import '../services/offline_notification_service.dart';

/// خدمة النظام المحاسبي
class AccountingService {
  static const String _accountsBox = 'accounts';
  static const String _journalEntriesBox = 'journal_entries';
  static const String _accountBalancesBox = 'account_balances';

  /// تهيئة النظام المحاسبي
  static Future<void> initialize() async {
    try {
      // فحص إذا كان دليل الحسابات موجود
      final accounts = await getAllAccounts();
      
      if (accounts.isEmpty) {
        // إنشاء دليل الحسابات الافتراضي
        await _createDefaultChartOfAccounts();
        debugPrint('✅ تم إنشاء دليل الحسابات الافتراضي');
      }
      
      debugPrint('✅ تم تهيئة النظام المحاسبي');
    } catch (e) {
      debugPrint('❌ فشل في تهيئة النظام المحاسبي: $e');
      rethrow;
    }
  }

  // ==================== إدارة الحسابات ====================

  /// الحصول على جميع الحسابات
  static Future<List<Account>> getAllAccounts() async {
    try {
      final accountsData = await OfflineDatabaseService.getBoxData(_accountsBox);
      final accounts = <Account>[];
      
      for (var accountMap in accountsData.values) {
        accounts.add(Account.fromJson(Map<String, dynamic>.from(accountMap)));
      }
      
      // ترتيب حسب الرمز
      accounts.sort((a, b) => a.code.compareTo(b.code));
      
      return accounts;
    } catch (e) {
      debugPrint('❌ فشل في جلب الحسابات: $e');
      return [];
    }
  }

  /// الحصول على حساب بالمعرف
  static Future<Account?> getAccountById(int id) async {
    try {
      final accountData = await OfflineDatabaseService.getBoxItem(_accountsBox, id.toString());
      if (accountData != null) {
        return Account.fromJson(Map<String, dynamic>.from(accountData));
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب الحساب: $e');
      return null;
    }
  }

  /// الحصول على حساب بالرمز
  static Future<Account?> getAccountByCode(String code) async {
    try {
      final accounts = await getAllAccounts();
      return accounts.firstWhere(
        (account) => account.code == code,
        orElse: () => throw Exception('الحساب غير موجود'),
      );
    } catch (e) {
      debugPrint('❌ فشل في جلب الحساب بالرمز: $e');
      return null;
    }
  }

  /// إضافة حساب جديد
  static Future<Account> addAccount(Account account) async {
    try {
      // التحقق من عدم تكرار الرمز
      final existingAccount = await getAccountByCode(account.code);
      if (existingAccount != null) {
        throw Exception('رمز الحساب موجود مسبقاً');
      }

      // إنشاء معرف جديد
      final newId = await _getNextAccountId();
      final newAccount = account.copyWith(
        id: newId,
        createdAt: DateTime.now(),
      );

      // حفظ الحساب
      await OfflineDatabaseService.saveBoxItem(
        _accountsBox,
        newId.toString(),
        newAccount.toJson(),
      );

      // إرسال إشعار
      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة الحساب "${newAccount.name}" بنجاح',
      );

      debugPrint('✅ تم إضافة الحساب: ${newAccount.name}');
      return newAccount;
    } catch (e) {
      debugPrint('❌ فشل في إضافة الحساب: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة الحساب: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// تحديث حساب
  static Future<Account> updateAccount(Account account) async {
    try {
      final updatedAccount = account.copyWith(updatedAt: DateTime.now());

      await OfflineDatabaseService.saveBoxItem(
        _accountsBox,
        account.id.toString(),
        updatedAccount.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم تحديث الحساب "${updatedAccount.name}" بنجاح',
      );

      debugPrint('✅ تم تحديث الحساب: ${updatedAccount.name}');
      return updatedAccount;
    } catch (e) {
      debugPrint('❌ فشل في تحديث الحساب: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في تحديث الحساب: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// حذف حساب
  static Future<void> deleteAccount(int accountId) async {
    try {
      final account = await getAccountById(accountId);
      if (account == null) {
        throw Exception('الحساب غير موجود');
      }

      if (account.isSystem) {
        throw Exception('لا يمكن حذف حساب النظام');
      }

      // فحص إذا كان الحساب مستخدم في قيود
      final hasTransactions = await _accountHasTransactions(accountId);
      if (hasTransactions) {
        throw Exception('لا يمكن حذف الحساب لوجود قيود مرتبطة به');
      }

      await OfflineDatabaseService.deleteBoxItem(_accountsBox, accountId.toString());

      await OfflineNotificationService.showSuccessNotification(
        'تم حذف الحساب "${account.name}" بنجاح',
      );

      debugPrint('✅ تم حذف الحساب: ${account.name}');
    } catch (e) {
      debugPrint('❌ فشل في حذف الحساب: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في حذف الحساب: ${e.toString()}',
      );
      rethrow;
    }
  }

  // ==================== إدارة القيود المحاسبية ====================

  /// الحصول على جميع القيود
  static Future<List<JournalEntry>> getAllJournalEntries() async {
    try {
      final entriesData = await OfflineDatabaseService.getBoxData(_journalEntriesBox);
      final entries = <JournalEntry>[];
      
      for (var entryMap in entriesData.values) {
        entries.add(JournalEntry.fromJson(Map<String, dynamic>.from(entryMap)));
      }
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      entries.sort((a, b) => b.date.compareTo(a.date));
      
      return entries;
    } catch (e) {
      debugPrint('❌ فشل في جلب القيود: $e');
      return [];
    }
  }

  /// إضافة قيد محاسبي
  static Future<JournalEntry> addJournalEntry(JournalEntry entry) async {
    try {
      // التحقق من صحة القيد
      if (!entry.isValid) {
        throw Exception('القيد غير متوازن أو غير صحيح');
      }

      // إنشاء معرف ومرجع جديد
      final newId = await _getNextJournalEntryId();
      final reference = entry.reference.isEmpty 
          ? await _generateJournalEntryReference()
          : entry.reference;

      final newEntry = entry.copyWith(
        id: newId,
        reference: reference,
        createdAt: DateTime.now(),
      );

      // حفظ القيد
      await OfflineDatabaseService.saveBoxItem(
        _journalEntriesBox,
        newId.toString(),
        newEntry.toJson(),
      );

      // تحديث أرصدة الحسابات
      await _updateAccountBalances(newEntry);

      await OfflineNotificationService.showSuccessNotification(
        'تم إضافة القيد ${newEntry.reference} بنجاح',
      );

      debugPrint('✅ تم إضافة القيد: ${newEntry.reference}');
      return newEntry;
    } catch (e) {
      debugPrint('❌ فشل في إضافة القيد: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في إضافة القيد: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// ترحيل قيد
  static Future<JournalEntry> postJournalEntry(int entryId) async {
    try {
      final entryData = await OfflineDatabaseService.getBoxItem(
        _journalEntriesBox,
        entryId.toString(),
      );
      
      if (entryData == null) {
        throw Exception('القيد غير موجود');
      }

      final entry = JournalEntry.fromJson(Map<String, dynamic>.from(entryData));
      
      if (entry.status == EntryStatus.posted) {
        throw Exception('القيد مرحل مسبقاً');
      }

      final postedEntry = entry.copyWith(
        status: EntryStatus.posted,
        updatedAt: DateTime.now(),
      );

      await OfflineDatabaseService.saveBoxItem(
        _journalEntriesBox,
        entryId.toString(),
        postedEntry.toJson(),
      );

      await OfflineNotificationService.showSuccessNotification(
        'تم ترحيل القيد ${postedEntry.reference} بنجاح',
      );

      debugPrint('✅ تم ترحيل القيد: ${postedEntry.reference}');
      return postedEntry;
    } catch (e) {
      debugPrint('❌ فشل في ترحيل القيد: $e');
      await OfflineNotificationService.showErrorNotification(
        'فشل في ترحيل القيد: ${e.toString()}',
      );
      rethrow;
    }
  }

  // ==================== التقارير المحاسبية ====================

  /// الحصول على رصيد حساب
  static Future<double> getAccountBalance(int accountId) async {
    try {
      final balanceData = await OfflineDatabaseService.getBoxItem(
        _accountBalancesBox,
        accountId.toString(),
      );
      
      if (balanceData != null) {
        return (balanceData['balance'] ?? 0.0).toDouble();
      }
      
      return 0.0;
    } catch (e) {
      debugPrint('❌ فشل في جلب رصيد الحساب: $e');
      return 0.0;
    }
  }

  /// الحصول على ميزان المراجعة
  static Future<List<Map<String, dynamic>>> getTrialBalance() async {
    try {
      final accounts = await getAllAccounts();
      final trialBalance = <Map<String, dynamic>>[];
      
      for (var account in accounts) {
        final balance = await getAccountBalance(account.id);
        
        trialBalance.add({
          'account_id': account.id,
          'account_code': account.code,
          'account_name': account.name,
          'account_type': account.type.toString().split('.').last,
          'debit_balance': account.isDebitAccount && balance > 0 ? balance : 0.0,
          'credit_balance': account.isCreditAccount && balance > 0 ? balance : 0.0,
        });
      }
      
      return trialBalance;
    } catch (e) {
      debugPrint('❌ فشل في إنشاء ميزان المراجعة: $e');
      return [];
    }
  }

  // ==================== وظائف مساعدة ====================

  /// إنشاء دليل الحسابات الافتراضي
  static Future<void> _createDefaultChartOfAccounts() async {
    final defaultAccounts = DefaultChartOfAccounts.getDefaultAccounts();
    
    for (var account in defaultAccounts) {
      await OfflineDatabaseService.saveBoxItem(
        _accountsBox,
        account.id.toString(),
        account.toJson(),
      );
    }
  }

  /// الحصول على معرف حساب جديد
  static Future<int> _getNextAccountId() async {
    final accounts = await getAllAccounts();
    if (accounts.isEmpty) return 1;
    
    final maxId = accounts.map((a) => a.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// الحصول على معرف قيد جديد
  static Future<int> _getNextJournalEntryId() async {
    final entries = await getAllJournalEntries();
    if (entries.isEmpty) return 1;
    
    final maxId = entries.map((e) => e.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// إنشاء مرجع قيد جديد
  static Future<String> _generateJournalEntryReference() async {
    final entries = await getAllJournalEntries();
    final count = entries.length + 1;
    return 'JE${count.toString().padLeft(6, '0')}';
  }

  /// فحص إذا كان الحساب له معاملات
  static Future<bool> _accountHasTransactions(int accountId) async {
    final entries = await getAllJournalEntries();
    
    for (var entry in entries) {
      for (var line in entry.lines) {
        if (line.accountId == accountId) {
          return true;
        }
      }
    }
    
    return false;
  }

  /// تحديث أرصدة الحسابات
  static Future<void> _updateAccountBalances(JournalEntry entry) async {
    for (var line in entry.lines) {
      final currentBalance = await getAccountBalance(line.accountId);
      final account = await getAccountById(line.accountId);
      
      if (account != null) {
        double newBalance;
        
        if (account.isDebitAccount) {
          // حسابات مدينة: المدين يزيد الرصيد، الدائن ينقص
          newBalance = currentBalance + line.debitAmount - line.creditAmount;
        } else {
          // حسابات دائنة: الدائن يزيد الرصيد، المدين ينقص
          newBalance = currentBalance + line.creditAmount - line.debitAmount;
        }
        
        await OfflineDatabaseService.saveBoxItem(
          _accountBalancesBox,
          line.accountId.toString(),
          {
            'account_id': line.accountId,
            'balance': newBalance,
            'updated_at': DateTime.now().toIso8601String(),
          },
        );
      }
    }
  }
}
