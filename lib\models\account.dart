/// نموذج الحساب المحاسبي
class Account {
  final int id;
  final String code; // رمز الحساب (1000, 1100, etc.)
  final String name; // اسم الحساب
  final String nameEn; // الاسم بالإنجليزية
  final AccountType type; // نوع الحساب
  final int? parentId; // الحساب الأب
  final bool isActive; // نشط أم لا
  final double balance; // الرصيد الحالي
  final String currency; // العملة الأساسية
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime? updatedAt; // تاريخ آخر تحديث
  final String? description; // وصف الحساب
  final bool isSystem; // حساب نظام (لا يمكن حذفه)
  final int level; // مستوى الحساب في الشجرة

  const Account({
    required this.id,
    required this.code,
    required this.name,
    required this.nameEn,
    required this.type,
    this.parentId,
    this.isActive = true,
    this.balance = 0.0,
    this.currency = 'SYP',
    required this.createdAt,
    this.updatedAt,
    this.description,
    this.isSystem = false,
    this.level = 1,
  });

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  Account copyWith({
    int? id,
    String? code,
    String? name,
    String? nameEn,
    AccountType? type,
    int? parentId,
    bool? isActive,
    double? balance,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
    bool? isSystem,
    int? level,
  }) {
    return Account(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      description: description ?? this.description,
      isSystem: isSystem ?? this.isSystem,
      level: level ?? this.level,
    );
  }

  /// تحويل من JSON
  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'] ?? 0,
      code: json['code'] ?? '',
      name: json['name'] ?? '',
      nameEn: json['name_en'] ?? '',
      type: AccountType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AccountType.asset,
      ),
      parentId: json['parent_id'],
      isActive: json['is_active'] ?? true,
      balance: (json['balance'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'SYP',
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      description: json['description'],
      isSystem: json['is_system'] ?? false,
      level: json['level'] ?? 1,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'name_en': nameEn,
      'type': type.toString().split('.').last,
      'parent_id': parentId,
      'is_active': isActive,
      'balance': balance,
      'currency': currency,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'description': description,
      'is_system': isSystem,
      'level': level,
    };
  }

  /// فحص إذا كان الحساب من نوع مدين
  bool get isDebitAccount {
    return type == AccountType.asset || type == AccountType.expense;
  }

  /// فحص إذا كان الحساب من نوع دائن
  bool get isCreditAccount {
    return type == AccountType.liability ||
        type == AccountType.equity ||
        type == AccountType.revenue;
  }

  /// الحصول على اسم نوع الحساب
  String get typeDisplayName {
    switch (type) {
      case AccountType.asset:
        return 'أصول';
      case AccountType.liability:
        return 'خصوم';
      case AccountType.equity:
        return 'حقوق الملكية';
      case AccountType.revenue:
        return 'إيرادات';
      case AccountType.expense:
        return 'مصروفات';
    }
  }

  /// الحصول على رمز نوع الحساب
  String get typeCode {
    switch (type) {
      case AccountType.asset:
        return '1';
      case AccountType.liability:
        return '2';
      case AccountType.equity:
        return '3';
      case AccountType.revenue:
        return '4';
      case AccountType.expense:
        return '5';
    }
  }

  /// تحديث الرصيد
  Account updateBalance(double newBalance) {
    return copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
    );
  }

  /// إضافة مبلغ للرصيد
  Account addToBalance(double amount) {
    return updateBalance(balance + amount);
  }

  /// خصم مبلغ من الرصيد
  Account subtractFromBalance(double amount) {
    return updateBalance(balance - amount);
  }

  /// فحص إذا كان الحساب له حسابات فرعية
  bool get hasChildren {
    // سيتم تنفيذ هذا في الخدمة
    return false;
  }

  /// الحصول على الرصيد المنسق
  String get formattedBalance {
    return '${balance.toStringAsFixed(2)} $currency';
  }

  /// فحص صحة رمز الحساب
  bool get isValidCode {
    return code.isNotEmpty && RegExp(r'^\d{4,6}$').hasMatch(code);
  }

  @override
  String toString() {
    return 'Account(id: $id, code: $code, name: $name, type: $type, balance: $balance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أنواع الحسابات المحاسبية
enum AccountType {
  asset, // أصول
  liability, // خصوم
  equity, // حقوق الملكية
  revenue, // إيرادات
  expense, // مصروفات
}

/// فئات الحسابات الفرعية
enum AccountSubType {
  // أصول
  currentAssets, // أصول متداولة
  fixedAssets, // أصول ثابتة
  intangibleAssets, // أصول غير ملموسة

  // خصوم
  currentLiabilities, // خصوم متداولة
  longTermLiabilities, // خصوم طويلة الأجل

  // حقوق الملكية
  capital, // رأس المال
  retainedEarnings, // الأرباح المحتجزة

  // إيرادات
  operatingRevenue, // إيرادات تشغيلية
  nonOperatingRevenue, // إيرادات غير تشغيلية

  // مصروفات
  operatingExpenses, // مصروفات تشغيلية
  nonOperatingExpenses, // مصروفات غير تشغيلية
}

/// دليل الحسابات الافتراضي
class DefaultChartOfAccounts {
  static List<Account> getDefaultAccounts() {
    final now = DateTime.now();

    return [
      // الأصول (1000-1999)
      Account(
        id: 1,
        code: '1000',
        name: 'الأصول',
        nameEn: 'Assets',
        type: AccountType.asset,
        createdAt: now,
        isSystem: true,
        level: 1,
      ),
      Account(
        id: 2,
        code: '1100',
        name: 'الأصول المتداولة',
        nameEn: 'Current Assets',
        type: AccountType.asset,
        parentId: 1,
        createdAt: now,
        isSystem: true,
        level: 2,
      ),
      Account(
        id: 3,
        code: '1110',
        name: 'النقدية',
        nameEn: 'Cash',
        type: AccountType.asset,
        parentId: 2,
        createdAt: now,
        isSystem: true,
        level: 3,
      ),
      Account(
        id: 4,
        code: '1120',
        name: 'البنوك',
        nameEn: 'Banks',
        type: AccountType.asset,
        parentId: 2,
        createdAt: now,
        isSystem: true,
        level: 3,
      ),
      Account(
        id: 5,
        code: '1130',
        name: 'العملاء',
        nameEn: 'Accounts Receivable',
        type: AccountType.asset,
        parentId: 2,
        createdAt: now,
        isSystem: true,
        level: 3,
      ),
      Account(
        id: 6,
        code: '1140',
        name: 'المخزون',
        nameEn: 'Inventory',
        type: AccountType.asset,
        parentId: 2,
        createdAt: now,
        isSystem: true,
        level: 3,
      ),

      // الخصوم (2000-2999)
      Account(
        id: 7,
        code: '2000',
        name: 'الخصوم',
        nameEn: 'Liabilities',
        type: AccountType.liability,
        createdAt: now,
        isSystem: true,
        level: 1,
      ),
      Account(
        id: 8,
        code: '2100',
        name: 'الخصوم المتداولة',
        nameEn: 'Current Liabilities',
        type: AccountType.liability,
        parentId: 7,
        createdAt: now,
        isSystem: true,
        level: 2,
      ),
      Account(
        id: 9,
        code: '2110',
        name: 'الموردون',
        nameEn: 'Accounts Payable',
        type: AccountType.liability,
        parentId: 8,
        createdAt: now,
        isSystem: true,
        level: 3,
      ),

      // حقوق الملكية (3000-3999)
      Account(
        id: 10,
        code: '3000',
        name: 'حقوق الملكية',
        nameEn: 'Equity',
        type: AccountType.equity,
        createdAt: now,
        isSystem: true,
        level: 1,
      ),
      Account(
        id: 11,
        code: '3100',
        name: 'رأس المال',
        nameEn: 'Capital',
        type: AccountType.equity,
        parentId: 10,
        createdAt: now,
        isSystem: true,
        level: 2,
      ),

      // الإيرادات (4000-4999)
      Account(
        id: 12,
        code: '4000',
        name: 'الإيرادات',
        nameEn: 'Revenue',
        type: AccountType.revenue,
        createdAt: now,
        isSystem: true,
        level: 1,
      ),
      Account(
        id: 13,
        code: '4100',
        name: 'إيرادات المبيعات',
        nameEn: 'Sales Revenue',
        type: AccountType.revenue,
        parentId: 12,
        createdAt: now,
        isSystem: true,
        level: 2,
      ),

      // المصروفات (5000-5999)
      Account(
        id: 14,
        code: '5000',
        name: 'المصروفات',
        nameEn: 'Expenses',
        type: AccountType.expense,
        createdAt: now,
        isSystem: true,
        level: 1,
      ),
      Account(
        id: 15,
        code: '5100',
        name: 'تكلفة البضاعة المباعة',
        nameEn: 'Cost of Goods Sold',
        type: AccountType.expense,
        parentId: 14,
        createdAt: now,
        isSystem: true,
        level: 2,
      ),
    ];
  }
}
