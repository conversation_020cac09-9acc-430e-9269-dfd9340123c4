import 'dart:async';
import 'package:flutter/material.dart';
import '../services/offline_notification_service.dart';
import '../services/global_currency_service.dart';
import '../services/company_service.dart';
import '../services/accounting_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();

    // مهلة زمنية قصوى - إذا لم ينته التحميل خلال 10 ثواني، انتقل للشاشة الرئيسية
    Timer(const Duration(seconds: 10), () {
      if (mounted) {
        debugPrint('انتهت المهلة الزمنية - الانتقال للشاشة الرئيسية');
        Navigator.pushReplacementNamed(context, '/home');
      }
    });
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // تهيئة الخدمات الأساسية فقط
      debugPrint('بدء تهيئة التطبيق...');

      // تهيئة الخدمات واحدة تلو الأخرى مع معالجة الأخطاء
      await _initializeServiceSafely('OfflineNotificationService', () async {
        await OfflineNotificationService.initialize();
      });

      await _initializeServiceSafely('GlobalCurrencyService', () async {
        await GlobalCurrencyService.initialize();
      });

      await _initializeServiceSafely('CompanyService', () async {
        await CompanyService.initialize();
      });

      await _initializeServiceSafely('AccountingService', () async {
        await AccountingService.initialize();
      });

      debugPrint('تم الانتهاء من تهيئة الخدمات');

      // انتظار قليل لإظهار الشاشة
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        debugPrint('الانتقال للشاشة الرئيسية...');
        Navigator.pushReplacementNamed(context, '/home');
      }
    } catch (e) {
      debugPrint('خطأ عام في تهيئة التطبيق: $e');
      // في حالة الخطأ، انتقل للشاشة الرئيسية مباشرة بعد ثانيتين
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/home');
      }
    }
  }

  Future<void> _initializeServiceSafely(
      String serviceName, Future<void> Function() initFunction) async {
    try {
      debugPrint('تهيئة $serviceName...');
      await initFunction();
      debugPrint('تم تهيئة $serviceName بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة $serviceName: $e');
      // نتجاهل الخطأ ونكمل
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار التطبيق
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.receipt_long,
                        size: 60,
                        color: Color(0xFF2196F3),
                      ),
                    ),
                    const SizedBox(height: 30),

                    // اسم التطبيق
                    const Text(
                      'نظام المحاسبة الذكي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),

                    // الوصف
                    const Text(
                      'حلول محاسبية ذكية ومتطورة',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 50),

                    // مؤشر التحميل
                    const SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 3,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // نص التحميل
                    const Text(
                      'جاري تحميل النظام...',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
