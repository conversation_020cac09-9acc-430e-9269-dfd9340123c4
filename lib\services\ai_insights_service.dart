import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../models/invoice.dart';
import '../models/customer.dart';
import '../services/offline_database_service.dart';

/// خدمة الذكاء الاصطناعي للتحليلات والتنبؤات
class AIInsightsService {
  /// توليد رؤى ذكية للأعمال
  static Future<List<BusinessInsight>> generateBusinessInsights() async {
    try {
      final insights = <BusinessInsight>[];

      // تحليل المبيعات
      final salesInsights = await _analyzeSalesTrends();
      insights.addAll(salesInsights);

      // تحليل المخزون
      final inventoryInsights = await _analyzeInventory();
      insights.addAll(inventoryInsights);

      // تحليل العملاء
      final customerInsights = await _analyzeCustomers();
      insights.addAll(customerInsights);

      // تحليل الربحية
      final profitabilityInsights = await _analyzeProfitability();
      insights.addAll(profitabilityInsights);

      // ترتيب حسب الأولوية
      insights.sort((a, b) => b.priority.compareTo(a.priority));

      return insights;
    } catch (e) {
      debugPrint('❌ فشل في توليد الرؤى الذكية: $e');
      return [];
    }
  }

  /// تحليل اتجاهات المبيعات
  static Future<List<BusinessInsight>> _analyzeSalesTrends() async {
    final insights = <BusinessInsight>[];

    try {
      final invoicesData = await OfflineDatabaseService.getBoxData('invoices');
      final invoices = invoicesData.values
          .map((data) => Invoice.fromJson(Map<String, dynamic>.from(data)))
          .toList();

      if (invoices.length < 7) return insights;

      // تحليل نمو المبيعات
      final last7Days = _getLastNDaysData(invoices, 7);
      final previous7Days = _getPreviousNDaysData(invoices, 7, 14);

      final currentTotal =
          last7Days.fold(0.0, (sum, inv) => sum + inv.totalAmountSyp);
      final previousTotal =
          previous7Days.fold(0.0, (sum, inv) => sum + inv.totalAmountSyp);

      if (previousTotal > 0) {
        final growthRate =
            ((currentTotal - previousTotal) / previousTotal) * 100;

        if (growthRate > 20) {
          insights.add(BusinessInsight(
            id: 'sales_growth_high',
            title: 'نمو ممتاز في المبيعات',
            description:
                'نمت مبيعاتك بنسبة ${growthRate.toStringAsFixed(1)}% خلال الأسبوع الماضي',
            type: InsightType.positive,
            priority: 5,
            actionable: true,
            recommendations: [
              'استمر في الاستراتيجية الحالية',
              'فكر في زيادة المخزون للمنتجات الأكثر مبيعاً',
              'قم بتوسيع خطوط الإنتاج الناجحة',
            ],
            icon: '📈',
          ));
        } else if (growthRate < -10) {
          insights.add(BusinessInsight(
            id: 'sales_decline',
            title: 'انخفاض في المبيعات',
            description:
                'انخفضت مبيعاتك بنسبة ${growthRate.abs().toStringAsFixed(1)}% خلال الأسبوع الماضي',
            type: InsightType.warning,
            priority: 4,
            actionable: true,
            recommendations: [
              'راجع استراتيجية التسويق',
              'قدم عروض وخصومات جذابة',
              'تواصل مع العملاء السابقين',
              'حلل أسباب انخفاض الطلب',
            ],
            icon: '📉',
          ));
        }
      }

      // تحليل أفضل أيام المبيعات
      final salesByDay = <int, double>{};
      for (var invoice in last7Days) {
        final dayOfWeek = invoice.date.weekday;
        salesByDay[dayOfWeek] =
            (salesByDay[dayOfWeek] ?? 0) + invoice.totalAmountSyp;
      }

      if (salesByDay.isNotEmpty) {
        final bestDay =
            salesByDay.entries.reduce((a, b) => a.value > b.value ? a : b);
        final dayName = _getDayName(bestDay.key);

        insights.add(BusinessInsight(
          id: 'best_sales_day',
          title: 'أفضل يوم للمبيعات',
          description: 'يوم $dayName هو أفضل أيامك في المبيعات',
          type: InsightType.info,
          priority: 2,
          actionable: true,
          recommendations: [
            'ركز على العروض الخاصة في يوم $dayName',
            'زد من المخزون قبل هذا اليوم',
            'خطط للحملات التسويقية في هذا اليوم',
          ],
          icon: '🗓️',
        ));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحليل اتجاهات المبيعات: $e');
    }

    return insights;
  }

  /// تحليل المخزون
  static Future<List<BusinessInsight>> _analyzeInventory() async {
    final insights = <BusinessInsight>[];

    try {
      final productsData = await OfflineDatabaseService.getBoxData('products');
      final products = productsData.values
          .map((data) => Product.fromJson(Map<String, dynamic>.from(data)))
          .toList();

      // تحليل المخزون المنخفض
      final lowStockProducts =
          products.where((p) => p.quantity <= p.minQuantity).toList();
      final outOfStockProducts =
          products.where((p) => p.quantity == 0).toList();

      if (outOfStockProducts.isNotEmpty) {
        insights.add(BusinessInsight(
          id: 'out_of_stock',
          title: 'منتجات نفدت من المخزون',
          description: '${outOfStockProducts.length} منتج نفد من المخزون',
          type: InsightType.critical,
          priority: 5,
          actionable: true,
          recommendations: [
            'أعد تموين المنتجات النافدة فوراً',
            'تواصل مع الموردين',
            'أبلغ العملاء عن تواريخ التوفر المتوقعة',
          ],
          icon: '🚨',
          data: {'products': outOfStockProducts.map((p) => p.name).toList()},
        ));
      }

      if (lowStockProducts.isNotEmpty &&
          outOfStockProducts.length != lowStockProducts.length) {
        insights.add(BusinessInsight(
          id: 'low_stock',
          title: 'مخزون منخفض',
          description: '${lowStockProducts.length} منتج يحتاج إعادة تموين',
          type: InsightType.warning,
          priority: 3,
          actionable: true,
          recommendations: [
            'خطط لإعادة تموين هذه المنتجات',
            'راجع مستويات الحد الأدنى للمخزون',
            'فكر في زيادة كميات الطلب',
          ],
          icon: '⚠️',
          data: {'products': lowStockProducts.map((p) => p.name).toList()},
        ));
      }

      // تحليل المنتجات الراكدة
      final invoicesData = await OfflineDatabaseService.getBoxData('invoices');
      final recentInvoices = invoicesData.values
          .map((data) => Invoice.fromJson(Map<String, dynamic>.from(data)))
          .where((inv) => DateTime.now().difference(inv.date).inDays <= 30)
          .toList();

      final soldProductNames = <String>{};
      for (var invoice in recentInvoices) {
        for (var item in invoice.items) {
          soldProductNames.add(item.productName);
        }
      }

      final stagnantProducts = products
          .where((p) => !soldProductNames.contains(p.name) && p.quantity > 0)
          .toList();

      if (stagnantProducts.isNotEmpty) {
        insights.add(BusinessInsight(
          id: 'stagnant_inventory',
          title: 'منتجات راكدة',
          description:
              '${stagnantProducts.length} منتج لم يُباع خلال الشهر الماضي',
          type: InsightType.warning,
          priority: 2,
          actionable: true,
          recommendations: [
            'قدم خصومات على هذه المنتجات',
            'راجع استراتيجية التسويق لها',
            'فكر في تطوير منتجات بديلة',
            'قلل من كميات الطلب المستقبلية',
          ],
          icon: '📦',
          data: {'products': stagnantProducts.map((p) => p.name).toList()},
        ));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحليل المخزون: $e');
    }

    return insights;
  }

  /// تحليل العملاء
  static Future<List<BusinessInsight>> _analyzeCustomers() async {
    final insights = <BusinessInsight>[];

    try {
      final customersData =
          await OfflineDatabaseService.getBoxData('customers');
      final customers = customersData.values
          .map((data) => Customer.fromJson(Map<String, dynamic>.from(data)))
          .toList();

      // تحليل الديون
      final debtors = customers.where((c) => c.totalDebt > 0).toList();
      final totalDebt = debtors.fold(0.0, (sum, c) => sum + c.totalDebt);

      if (totalDebt > 100000) {
        // أكثر من 100 ألف
        insights.add(BusinessInsight(
          id: 'high_debt',
          title: 'ديون مرتفعة',
          description:
              'إجمالي الديون ${totalDebt.toStringAsFixed(0)} ل.س من ${debtors.length} عميل',
          type: InsightType.warning,
          priority: 4,
          actionable: true,
          recommendations: [
            'تواصل مع العملاء المدينين',
            'ضع خطة لتحصيل الديون',
            'راجع سياسة الائتمان',
            'فكر في تقديم خصومات للدفع المبكر',
          ],
          icon: '💰',
          data: {'total_debt': totalDebt, 'debtors_count': debtors.length},
        ));
      }

      // تحليل العملاء الجدد
      final newCustomers = customers
          .where((c) => DateTime.now().difference(c.createdAt).inDays <= 30)
          .toList();

      if (newCustomers.isNotEmpty) {
        insights.add(BusinessInsight(
          id: 'new_customers',
          title: 'عملاء جدد',
          description: 'انضم ${newCustomers.length} عميل جديد هذا الشهر',
          type: InsightType.positive,
          priority: 3,
          actionable: true,
          recommendations: [
            'رحب بالعملاء الجدد بعروض خاصة',
            'تابع مع العملاء الجدد لضمان رضاهم',
            'اطلب تقييمات وآراء',
            'شجعهم على الإحالات',
          ],
          icon: '👥',
          data: {'new_customers_count': newCustomers.length},
        ));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحليل العملاء: $e');
    }

    return insights;
  }

  /// تحليل الربحية
  static Future<List<BusinessInsight>> _analyzeProfitability() async {
    final insights = <BusinessInsight>[];

    try {
      final productsData = await OfflineDatabaseService.getBoxData('products');
      final products = productsData.values
          .map((data) => Product.fromJson(Map<String, dynamic>.from(data)))
          .toList();

      final invoicesData = await OfflineDatabaseService.getBoxData('invoices');
      final recentInvoices = invoicesData.values
          .map((data) => Invoice.fromJson(Map<String, dynamic>.from(data)))
          .where((inv) => DateTime.now().difference(inv.date).inDays <= 30)
          .toList();

      // حساب الربحية لكل منتج
      final productProfitability = <String, double>{};

      for (var invoice in recentInvoices) {
        for (var item in invoice.items) {
          final product = products.firstWhere(
            (p) => p.name == item.productName,
            orElse: () => Product(
              id: 0,
              name: item.productName,
              barcode: '',
              purchasePrice: 0,
              salePrice: item.unitPriceSyp,
              quantity: 0,
              minQuantity: 0,
              sellingPriceSyp: item.unitPriceSyp,
              sellingPriceUsd: item.unitPriceUsd,
              purchasePriceSyp: 0,
              purchasePriceUsd: 0,
              purchaseCurrency: 'SYP',
            ),
          );

          final profit =
              (item.unitPriceSyp - product.purchasePrice) * item.quantity;
          productProfitability[item.productName] =
              (productProfitability[item.productName] ?? 0) + profit;
        }
      }

      if (productProfitability.isNotEmpty) {
        // أكثر المنتجات ربحية
        final mostProfitable = productProfitability.entries
            .reduce((a, b) => a.value > b.value ? a : b);

        insights.add(BusinessInsight(
          id: 'most_profitable_product',
          title: 'أكثر المنتجات ربحية',
          description:
              '${mostProfitable.key} حقق ربح ${mostProfitable.value.toStringAsFixed(0)} ل.س',
          type: InsightType.positive,
          priority: 3,
          actionable: true,
          recommendations: [
            'ركز على تسويق هذا المنتج',
            'زد من المخزون',
            'طور منتجات مشابهة',
            'ادرس إمكانية زيادة السعر',
          ],
          icon: '💎',
          data: {'product': mostProfitable.key, 'profit': mostProfitable.value},
        ));

        // المنتجات الأقل ربحية
        final leastProfitable = productProfitability.entries
            .where((e) => e.value < 10000) // أقل من 10 آلاف ربح
            .toList();

        if (leastProfitable.isNotEmpty) {
          insights.add(BusinessInsight(
            id: 'low_profit_products',
            title: 'منتجات قليلة الربحية',
            description: '${leastProfitable.length} منتج يحقق ربح منخفض',
            type: InsightType.info,
            priority: 2,
            actionable: true,
            recommendations: [
              'راجع أسعار هذه المنتجات',
              'ابحث عن موردين أرخص',
              'فكر في إيقاف المنتجات غير المربحة',
              'حسن من استراتيجية التسويق',
            ],
            icon: '📊',
            data: {'products': leastProfitable.map((e) => e.key).toList()},
          ));
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في تحليل الربحية: $e');
    }

    return insights;
  }

  // وظائف مساعدة
  static List<Invoice> _getLastNDaysData(List<Invoice> invoices, int days) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return invoices.where((inv) => inv.date.isAfter(cutoffDate)).toList();
  }

  static List<Invoice> _getPreviousNDaysData(
      List<Invoice> invoices, int days, int offset) {
    final endDate = DateTime.now().subtract(Duration(days: offset));
    final startDate = endDate.subtract(Duration(days: days));
    return invoices
        .where(
            (inv) => inv.date.isAfter(startDate) && inv.date.isBefore(endDate))
        .toList();
  }

  static String _getDayName(int dayOfWeek) {
    const days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return days[dayOfWeek - 1];
  }
}

/// نموذج الرؤية التجارية
class BusinessInsight {
  final String id;
  final String title;
  final String description;
  final InsightType type;
  final int priority; // 1-5 (5 = أعلى أولوية)
  final bool actionable;
  final List<String> recommendations;
  final String icon;
  final Map<String, dynamic>? data;
  final DateTime createdAt;

  BusinessInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
    this.actionable = false,
    this.recommendations = const [],
    this.icon = '💡',
    this.data,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'priority': priority,
      'actionable': actionable,
      'recommendations': recommendations,
      'icon': icon,
      'data': data,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// أنواع الرؤى
enum InsightType {
  positive, // إيجابي (أخضر)
  warning, // تحذير (برتقالي)
  critical, // حرج (أحمر)
  info, // معلومات (أزرق)
}
