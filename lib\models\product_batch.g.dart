// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_batch.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProductBatchAdapter extends TypeAdapter<ProductBatch> {
  @override
  final int typeId = 15;

  @override
  ProductBatch read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductBatch(
      id: fields[0] as int,
      batchNumber: fields[1] as String,
      productId: fields[2] as int,
      warehouseId: fields[3] as int,
      originalQuantity: fields[4] as int,
      currentQuantity: fields[5] as int,
      availableQuantity: fields[6] as int,
      reservedQuantity: fields[7] as int,
      soldQuantity: fields[8] as int,
      costPrice: fields[9] as double,
      sellingPrice: fields[10] as double?,
      manufacturingDate: fields[11] as DateTime,
      expiryDate: fields[12] as DateTime?,
      supplier: fields[13] as String?,
      supplierBatchNumber: fields[14] as String?,
      location: fields[15] as String?,
      status: fields[16] as BatchStatus,
      notes: fields[17] as String?,
      createdAt: fields[18] as DateTime,
      updatedAt: fields[19] as DateTime?,
      qrCode: fields[20] as String?,
      customFields: (fields[21] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, ProductBatch obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.batchNumber)
      ..writeByte(2)
      ..write(obj.productId)
      ..writeByte(3)
      ..write(obj.warehouseId)
      ..writeByte(4)
      ..write(obj.originalQuantity)
      ..writeByte(5)
      ..write(obj.currentQuantity)
      ..writeByte(6)
      ..write(obj.availableQuantity)
      ..writeByte(7)
      ..write(obj.reservedQuantity)
      ..writeByte(8)
      ..write(obj.soldQuantity)
      ..writeByte(9)
      ..write(obj.costPrice)
      ..writeByte(10)
      ..write(obj.sellingPrice)
      ..writeByte(11)
      ..write(obj.manufacturingDate)
      ..writeByte(12)
      ..write(obj.expiryDate)
      ..writeByte(13)
      ..write(obj.supplier)
      ..writeByte(14)
      ..write(obj.supplierBatchNumber)
      ..writeByte(15)
      ..write(obj.location)
      ..writeByte(16)
      ..write(obj.status)
      ..writeByte(17)
      ..write(obj.notes)
      ..writeByte(18)
      ..write(obj.createdAt)
      ..writeByte(19)
      ..write(obj.updatedAt)
      ..writeByte(20)
      ..write(obj.qrCode)
      ..writeByte(21)
      ..write(obj.customFields);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductBatchAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BatchStatusAdapter extends TypeAdapter<BatchStatus> {
  @override
  final int typeId = 14;

  @override
  BatchStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BatchStatus.active;
      case 1:
        return BatchStatus.expired;
      case 2:
        return BatchStatus.recalled;
      case 3:
        return BatchStatus.sold;
      case 4:
        return BatchStatus.damaged;
      case 5:
        return BatchStatus.quarantine;
      default:
        return BatchStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, BatchStatus obj) {
    switch (obj) {
      case BatchStatus.active:
        writer.writeByte(0);
        break;
      case BatchStatus.expired:
        writer.writeByte(1);
        break;
      case BatchStatus.recalled:
        writer.writeByte(2);
        break;
      case BatchStatus.sold:
        writer.writeByte(3);
        break;
      case BatchStatus.damaged:
        writer.writeByte(4);
        break;
      case BatchStatus.quarantine:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BatchStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
