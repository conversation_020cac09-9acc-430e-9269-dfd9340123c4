/// نموذج القيد المحاسبي
class JournalEntry {
  final int id;
  final String reference; // مرجع القيد (JE001, JE002, etc.)
  final DateTime date; // تاريخ القيد
  final String description; // وصف القيد
  final List<JournalLine> lines; // بنود القيد
  final EntryStatus status; // حالة القيد
  final EntryType type; // نوع القيد
  final String? sourceDocument; // المستند المصدر
  final int? sourceId; // معرف المصدر (فاتورة، إيصال، etc.)
  final DateTime createdAt; // تاريخ الإنشاء
  final DateTime? updatedAt; // تاريخ آخر تحديث
  final int? createdBy; // المستخدم الذي أنشأ القيد
  final String? notes; // ملاحظات إضافية

  const JournalEntry({
    required this.id,
    required this.reference,
    required this.date,
    required this.description,
    required this.lines,
    this.status = EntryStatus.draft,
    this.type = EntryType.manual,
    this.sourceDocument,
    this.sourceId,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.notes,
  });

  /// إجمالي المدين
  double get totalDebit {
    return lines.fold(0.0, (sum, line) => sum + line.debitAmount);
  }

  /// إجمالي الدائن
  double get totalCredit {
    return lines.fold(0.0, (sum, line) => sum + line.creditAmount);
  }

  /// فحص توازن القيد
  bool get isBalanced {
    return (totalDebit - totalCredit).abs() <
        0.01; // تسامح صغير للأخطاء العشرية
  }

  /// فحص صحة القيد
  bool get isValid {
    return lines.isNotEmpty &&
        lines.length >= 2 &&
        isBalanced &&
        lines.every((line) => line.isValid);
  }

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  JournalEntry copyWith({
    int? id,
    String? reference,
    DateTime? date,
    String? description,
    List<JournalLine>? lines,
    EntryStatus? status,
    EntryType? type,
    String? sourceDocument,
    int? sourceId,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? createdBy,
    String? notes,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      date: date ?? this.date,
      description: description ?? this.description,
      lines: lines ?? this.lines,
      status: status ?? this.status,
      type: type ?? this.type,
      sourceDocument: sourceDocument ?? this.sourceDocument,
      sourceId: sourceId ?? this.sourceId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      notes: notes ?? this.notes,
    );
  }

  /// تحويل من JSON
  factory JournalEntry.fromJson(Map<String, dynamic> json) {
    return JournalEntry(
      id: json['id'] ?? 0,
      reference: json['reference'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      description: json['description'] ?? '',
      lines: (json['lines'] as List<dynamic>?)
              ?.map((line) => JournalLine.fromJson(line))
              .toList() ??
          [],
      status: EntryStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => EntryStatus.draft,
      ),
      type: EntryType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => EntryType.manual,
      ),
      sourceDocument: json['source_document'],
      sourceId: json['source_id'],
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      createdBy: json['created_by'],
      notes: json['notes'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reference': reference,
      'date': date.toIso8601String(),
      'description': description,
      'lines': lines.map((line) => line.toJson()).toList(),
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'source_document': sourceDocument,
      'source_id': sourceId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
      'notes': notes,
    };
  }

  @override
  String toString() {
    return 'JournalEntry(id: $id, reference: $reference, description: $description, totalDebit: $totalDebit, totalCredit: $totalCredit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// بند القيد المحاسبي
class JournalLine {
  final int id;
  final int accountId; // رقم الحساب
  final String accountCode; // رمز الحساب
  final String accountName; // اسم الحساب
  final double debitAmount; // المبلغ المدين
  final double creditAmount; // المبلغ الدائن
  final String currency; // العملة
  final double exchangeRate; // سعر الصرف
  final String? description; // وصف البند
  final String? reference; // مرجع البند

  const JournalLine({
    required this.id,
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    this.debitAmount = 0.0,
    this.creditAmount = 0.0,
    this.currency = 'SYP',
    this.exchangeRate = 1.0,
    this.description,
    this.reference,
  });

  /// المبلغ بالعملة الأساسية
  double get amountInBaseCurrency {
    return (debitAmount + creditAmount) * exchangeRate;
  }

  /// فحص صحة البند
  bool get isValid {
    return accountId > 0 &&
        (debitAmount > 0 || creditAmount > 0) &&
        (debitAmount == 0 ||
            creditAmount == 0) && // لا يمكن أن يكون مدين ودائن معاً
        exchangeRate > 0;
  }

  /// فحص إذا كان البند مدين
  bool get isDebit {
    return debitAmount > 0;
  }

  /// فحص إذا كان البند دائن
  bool get isCredit {
    return creditAmount > 0;
  }

  /// الحصول على المبلغ (مدين أو دائن)
  double get amount {
    return debitAmount + creditAmount;
  }

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  JournalLine copyWith({
    int? id,
    int? accountId,
    String? accountCode,
    String? accountName,
    double? debitAmount,
    double? creditAmount,
    String? currency,
    double? exchangeRate,
    String? description,
    String? reference,
  }) {
    return JournalLine(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountCode: accountCode ?? this.accountCode,
      accountName: accountName ?? this.accountName,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      currency: currency ?? this.currency,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      description: description ?? this.description,
      reference: reference ?? this.reference,
    );
  }

  /// تحويل من JSON
  factory JournalLine.fromJson(Map<String, dynamic> json) {
    return JournalLine(
      id: json['id'] ?? 0,
      accountId: json['account_id'] ?? 0,
      accountCode: json['account_code'] ?? '',
      accountName: json['account_name'] ?? '',
      debitAmount: (json['debit_amount'] ?? 0.0).toDouble(),
      creditAmount: (json['credit_amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'SYP',
      exchangeRate: (json['exchange_rate'] ?? 1.0).toDouble(),
      description: json['description'],
      reference: json['reference'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'account_id': accountId,
      'account_code': accountCode,
      'account_name': accountName,
      'debit_amount': debitAmount,
      'credit_amount': creditAmount,
      'currency': currency,
      'exchange_rate': exchangeRate,
      'description': description,
      'reference': reference,
    };
  }

  @override
  String toString() {
    return 'JournalLine(accountCode: $accountCode, accountName: $accountName, debit: $debitAmount, credit: $creditAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalLine && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// حالات القيد المحاسبي
enum EntryStatus {
  draft, // مسودة
  posted, // مرحل
  reversed, // معكوس
  cancelled, // ملغي
}

/// أنواع القيود المحاسبية
enum EntryType {
  manual, // يدوي
  sales, // مبيعات
  purchase, // مشتريات
  payment, // دفع
  receipt, // قبض
  adjustment, // تسوية
  closing, // إقفال
  opening, // افتتاحي
  depreciation, // استهلاك
  accrual, // استحقاق
}

/// مساعد لإنشاء القيود المحاسبية
class JournalEntryHelper {
  /// إنشاء قيد مبيعات
  static JournalEntry createSalesEntry({
    required String reference,
    required DateTime date,
    required int customerId,
    required double amount,
    required String currency,
    double exchangeRate = 1.0,
    String? description,
  }) {
    return JournalEntry(
      id: 0, // سيتم تعيينه عند الحفظ
      reference: reference,
      date: date,
      description: description ?? 'قيد مبيعات - العميل $customerId',
      type: EntryType.sales,
      sourceDocument: 'invoice',
      sourceId: customerId,
      createdAt: DateTime.now(),
      lines: [
        JournalLine(
          id: 0,
          accountId: 5, // حساب العملاء
          accountCode: '1130',
          accountName: 'العملاء',
          debitAmount: amount,
          currency: currency,
          exchangeRate: exchangeRate,
          description: 'مبيعات للعميل $customerId',
        ),
        JournalLine(
          id: 0,
          accountId: 13, // حساب إيرادات المبيعات
          accountCode: '4100',
          accountName: 'إيرادات المبيعات',
          creditAmount: amount,
          currency: currency,
          exchangeRate: exchangeRate,
          description: 'إيرادات مبيعات',
        ),
      ],
    );
  }

  /// إنشاء قيد دفع
  static JournalEntry createPaymentEntry({
    required String reference,
    required DateTime date,
    required int paymentAccountId,
    required String paymentAccountCode,
    required String paymentAccountName,
    required int receivingAccountId,
    required String receivingAccountCode,
    required String receivingAccountName,
    required double amount,
    required String currency,
    double exchangeRate = 1.0,
    String? description,
  }) {
    return JournalEntry(
      id: 0,
      reference: reference,
      date: date,
      description: description ?? 'قيد دفع',
      type: EntryType.payment,
      createdAt: DateTime.now(),
      lines: [
        JournalLine(
          id: 0,
          accountId: receivingAccountId,
          accountCode: receivingAccountCode,
          accountName: receivingAccountName,
          debitAmount: amount,
          currency: currency,
          exchangeRate: exchangeRate,
          description: 'دفع',
        ),
        JournalLine(
          id: 0,
          accountId: paymentAccountId,
          accountCode: paymentAccountCode,
          accountName: paymentAccountName,
          creditAmount: amount,
          currency: currency,
          exchangeRate: exchangeRate,
          description: 'دفع من $paymentAccountName',
        ),
      ],
    );
  }

  /// إنشاء قيد تسوية عملة
  static JournalEntry createCurrencyAdjustmentEntry({
    required String reference,
    required DateTime date,
    required List<Map<String, dynamic>> adjustments,
    String? description,
  }) {
    final lines = <JournalLine>[];

    for (var adjustment in adjustments) {
      final amount = adjustment['amount'] as double;
      final accountId = adjustment['account_id'] as int;
      final accountCode = adjustment['account_code'] as String;
      final accountName = adjustment['account_name'] as String;
      final isGain = adjustment['is_gain'] as bool;

      if (isGain) {
        // ربح صرف
        lines.add(JournalLine(
          id: 0,
          accountId: accountId,
          accountCode: accountCode,
          accountName: accountName,
          debitAmount: amount,
          description: 'ربح صرف',
        ));
      } else {
        // خسارة صرف
        lines.add(JournalLine(
          id: 0,
          accountId: accountId,
          accountCode: accountCode,
          accountName: accountName,
          creditAmount: amount,
          description: 'خسارة صرف',
        ));
      }
    }

    return JournalEntry(
      id: 0,
      reference: reference,
      date: date,
      description: description ?? 'قيد تسوية أسعار الصرف',
      type: EntryType.adjustment,
      createdAt: DateTime.now(),
      lines: lines,
    );
  }
}
