// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WarehouseAdapter extends TypeAdapter<Warehouse> {
  @override
  final int typeId = 10;

  @override
  Warehouse read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Warehouse(
      id: fields[0] as int,
      name: fields[1] as String,
      code: fields[2] as String,
      location: fields[3] as String,
      description: fields[4] as String?,
      manager: fields[5] as String,
      phone: fields[6] as String?,
      email: fields[7] as String?,
      isActive: fields[8] as bool,
      isMainWarehouse: fields[9] as bool,
      maxCapacity: fields[10] as double?,
      address: fields[11] as String?,
      createdAt: fields[12] as DateTime,
      updatedAt: fields[13] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Warehouse obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.code)
      ..writeByte(3)
      ..write(obj.location)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.manager)
      ..writeByte(6)
      ..write(obj.phone)
      ..writeByte(7)
      ..write(obj.email)
      ..writeByte(8)
      ..write(obj.isActive)
      ..writeByte(9)
      ..write(obj.isMainWarehouse)
      ..writeByte(10)
      ..write(obj.maxCapacity)
      ..writeByte(11)
      ..write(obj.address)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WarehouseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductWarehouseStockAdapter extends TypeAdapter<ProductWarehouseStock> {
  @override
  final int typeId = 11;

  @override
  ProductWarehouseStock read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductWarehouseStock(
      id: fields[0] as int,
      productId: fields[1] as int,
      warehouseId: fields[2] as int,
      quantity: fields[3] as int,
      reservedQuantity: fields[4] as int,
      availableQuantity: fields[5] as int,
      location: fields[6] as String?,
      minQuantity: fields[7] as int?,
      maxQuantity: fields[8] as int?,
      lastUpdated: fields[9] as DateTime,
      lastStockTake: fields[10] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, ProductWarehouseStock obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.warehouseId)
      ..writeByte(3)
      ..write(obj.quantity)
      ..writeByte(4)
      ..write(obj.reservedQuantity)
      ..writeByte(5)
      ..write(obj.availableQuantity)
      ..writeByte(6)
      ..write(obj.location)
      ..writeByte(7)
      ..write(obj.minQuantity)
      ..writeByte(8)
      ..write(obj.maxQuantity)
      ..writeByte(9)
      ..write(obj.lastUpdated)
      ..writeByte(10)
      ..write(obj.lastStockTake);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductWarehouseStockAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
