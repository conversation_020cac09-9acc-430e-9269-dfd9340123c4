// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_movement.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class StockMovementAdapter extends TypeAdapter<StockMovement> {
  @override
  final int typeId = 13;

  @override
  StockMovement read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return StockMovement(
      id: fields[0] as int,
      productId: fields[1] as int,
      warehouseId: fields[2] as int,
      type: fields[3] as StockMovementType,
      quantity: fields[4] as int,
      quantityBefore: fields[5] as int,
      quantityAfter: fields[6] as int,
      unitCost: fields[7] as double?,
      totalCost: fields[8] as double?,
      reference: fields[9] as String,
      notes: fields[10] as String?,
      batchNumber: fields[11] as String?,
      expiryDate: fields[12] as DateTime?,
      fromWarehouseId: fields[13] as int?,
      toWarehouseId: fields[14] as int?,
      userId: fields[15] as String,
      createdAt: fields[16] as DateTime,
      isApproved: fields[17] as bool,
      approvedBy: fields[18] as String?,
      approvedAt: fields[19] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, StockMovement obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.warehouseId)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.quantity)
      ..writeByte(5)
      ..write(obj.quantityBefore)
      ..writeByte(6)
      ..write(obj.quantityAfter)
      ..writeByte(7)
      ..write(obj.unitCost)
      ..writeByte(8)
      ..write(obj.totalCost)
      ..writeByte(9)
      ..write(obj.reference)
      ..writeByte(10)
      ..write(obj.notes)
      ..writeByte(11)
      ..write(obj.batchNumber)
      ..writeByte(12)
      ..write(obj.expiryDate)
      ..writeByte(13)
      ..write(obj.fromWarehouseId)
      ..writeByte(14)
      ..write(obj.toWarehouseId)
      ..writeByte(15)
      ..write(obj.userId)
      ..writeByte(16)
      ..write(obj.createdAt)
      ..writeByte(17)
      ..write(obj.isApproved)
      ..writeByte(18)
      ..write(obj.approvedBy)
      ..writeByte(19)
      ..write(obj.approvedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StockMovementAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class StockMovementTypeAdapter extends TypeAdapter<StockMovementType> {
  @override
  final int typeId = 12;

  @override
  StockMovementType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return StockMovementType.purchase;
      case 1:
        return StockMovementType.sale;
      case 2:
        return StockMovementType.adjustment;
      case 3:
        return StockMovementType.transfer;
      case 4:
        return StockMovementType.return_in;
      case 5:
        return StockMovementType.return_out;
      case 6:
        return StockMovementType.damage;
      case 7:
        return StockMovementType.expired;
      case 8:
        return StockMovementType.stocktake;
      case 9:
        return StockMovementType.production;
      case 10:
        return StockMovementType.consumption;
      default:
        return StockMovementType.purchase;
    }
  }

  @override
  void write(BinaryWriter writer, StockMovementType obj) {
    switch (obj) {
      case StockMovementType.purchase:
        writer.writeByte(0);
        break;
      case StockMovementType.sale:
        writer.writeByte(1);
        break;
      case StockMovementType.adjustment:
        writer.writeByte(2);
        break;
      case StockMovementType.transfer:
        writer.writeByte(3);
        break;
      case StockMovementType.return_in:
        writer.writeByte(4);
        break;
      case StockMovementType.return_out:
        writer.writeByte(5);
        break;
      case StockMovementType.damage:
        writer.writeByte(6);
        break;
      case StockMovementType.expired:
        writer.writeByte(7);
        break;
      case StockMovementType.stocktake:
        writer.writeByte(8);
        break;
      case StockMovementType.production:
        writer.writeByte(9);
        break;
      case StockMovementType.consumption:
        writer.writeByte(10);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StockMovementTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
