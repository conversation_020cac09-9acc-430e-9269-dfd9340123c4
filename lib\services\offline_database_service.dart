import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/invoice.dart';

/// خدمة قاعدة البيانات المحلية الكاملة
class OfflineDatabaseService {
  static late Box<Map> _productsBox;
  static late Box<Map> _customersBox;
  static late Box<Map> _invoicesBox;
  static late Box<Map> _settingsBox;
  static late Box<Map> _currencyHistoryBox;

  static bool _isInitialized = false;

  /// تهيئة قاعدة البيانات المحلية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // فتح جميع الصناديق
      _productsBox = await Hive.openBox<Map>('products');
      _customersBox = await Hive.openBox<Map>('customers');
      _invoicesBox = await Hive.openBox<Map>('invoices');
      _settingsBox = await Hive.openBox<Map>('settings');
      _currencyHistoryBox = await Hive.openBox<Map>('currency_history');

      _isInitialized = true;
      debugPrint('✅ تم تهيئة قاعدة البيانات المحلية');

      // إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة
      await _createSampleDataIfEmpty();
    } catch (e) {
      debugPrint('❌ فشل في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  // ==================== إدارة المنتجات ====================

  /// الحصول على جميع المنتجات
  static Future<List<Product>> getAllProducts() async {
    if (!_isInitialized) await initialize();

    try {
      final products = <Product>[];
      for (var productMap in _productsBox.values) {
        products.add(Product.fromJson(Map<String, dynamic>.from(productMap)));
      }
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// إضافة منتج جديد
  static Future<Product> addProduct(Product product) async {
    if (!_isInitialized) await initialize();

    try {
      // إنشاء ID جديد
      final newId = _getNextId(_productsBox);
      final newProduct = product.copyWith(id: newId);

      // حفظ المنتج
      await _productsBox.put(newId.toString(), newProduct.toJson());

      debugPrint('✅ تم إضافة المنتج: ${newProduct.name}');
      return newProduct;
    } catch (e) {
      debugPrint('❌ فشل في إضافة المنتج: $e');
      rethrow;
    }
  }

  /// تحديث منتج
  static Future<Product> updateProduct(Product product) async {
    if (!_isInitialized) await initialize();

    try {
      await _productsBox.put(product.id.toString(), product.toJson());
      debugPrint('✅ تم تحديث المنتج: ${product.name}');
      return product;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المنتج: $e');
      rethrow;
    }
  }

  /// حذف منتج
  static Future<void> deleteProduct(int productId) async {
    if (!_isInitialized) await initialize();

    try {
      await _productsBox.delete(productId.toString());
      debugPrint('✅ تم حذف المنتج رقم: $productId');
    } catch (e) {
      debugPrint('❌ فشل في حذف المنتج: $e');
      rethrow;
    }
  }

  // ==================== إدارة العملاء ====================

  /// الحصول على جميع العملاء
  static Future<List<Customer>> getAllCustomers() async {
    if (!_isInitialized) await initialize();

    try {
      final customers = <Customer>[];
      for (var customerMap in _customersBox.values) {
        customers
            .add(Customer.fromJson(Map<String, dynamic>.from(customerMap)));
      }
      return customers;
    } catch (e) {
      debugPrint('خطأ في جلب العملاء: $e');
      return [];
    }
  }

  /// إضافة عميل جديد
  static Future<Customer> addCustomer(Customer customer) async {
    if (!_isInitialized) await initialize();

    try {
      final newId = _getNextId(_customersBox);
      final newCustomer = customer.copyWith(id: newId);

      await _customersBox.put(newId.toString(), newCustomer.toJson());

      debugPrint('✅ تم إضافة العميل: ${newCustomer.name}');
      return newCustomer;
    } catch (e) {
      debugPrint('❌ فشل في إضافة العميل: $e');
      rethrow;
    }
  }

  /// تحديث عميل
  static Future<Customer> updateCustomer(Customer customer) async {
    if (!_isInitialized) await initialize();

    try {
      await _customersBox.put(customer.id.toString(), customer.toJson());
      debugPrint('✅ تم تحديث العميل: ${customer.name}');
      return customer;
    } catch (e) {
      debugPrint('❌ فشل في تحديث العميل: $e');
      rethrow;
    }
  }

  /// حذف عميل
  static Future<void> deleteCustomer(int customerId) async {
    if (!_isInitialized) await initialize();

    try {
      await _customersBox.delete(customerId.toString());
      debugPrint('✅ تم حذف العميل رقم: $customerId');
    } catch (e) {
      debugPrint('❌ فشل في حذف العميل: $e');
      rethrow;
    }
  }

  // ==================== إدارة الفواتير ====================

  /// الحصول على جميع الفواتير
  static Future<List<Invoice>> getAllInvoices() async {
    if (!_isInitialized) await initialize();

    try {
      final invoices = <Invoice>[];
      for (var invoiceMap in _invoicesBox.values) {
        invoices.add(Invoice.fromJson(Map<String, dynamic>.from(invoiceMap)));
      }
      // ترتيب حسب التاريخ (الأحدث أولاً)
      invoices.sort((a, b) => b.date.compareTo(a.date));
      return invoices;
    } catch (e) {
      debugPrint('خطأ في جلب الفواتير: $e');
      return [];
    }
  }

  /// إضافة فاتورة جديدة
  static Future<Invoice> addInvoice(Invoice invoice) async {
    if (!_isInitialized) await initialize();

    try {
      final newId = _getNextId(_invoicesBox);
      final newInvoice = invoice.copyWith(id: newId);

      await _invoicesBox.put(newId.toString(), newInvoice.toJson());

      debugPrint('✅ تم إضافة الفاتورة رقم: ${newInvoice.id}');
      return newInvoice;
    } catch (e) {
      debugPrint('❌ فشل في إضافة الفاتورة: $e');
      rethrow;
    }
  }

  /// تحديث فاتورة
  static Future<Invoice> updateInvoice(Invoice invoice) async {
    if (!_isInitialized) await initialize();

    try {
      await _invoicesBox.put(invoice.id.toString(), invoice.toJson());
      debugPrint('✅ تم تحديث الفاتورة رقم: ${invoice.id}');
      return invoice;
    } catch (e) {
      debugPrint('❌ فشل في تحديث الفاتورة: $e');
      rethrow;
    }
  }

  /// حذف فاتورة
  static Future<void> deleteInvoice(int invoiceId) async {
    if (!_isInitialized) await initialize();

    try {
      await _invoicesBox.delete(invoiceId.toString());
      debugPrint('✅ تم حذف الفاتورة رقم: $invoiceId');
    } catch (e) {
      debugPrint('❌ فشل في حذف الفاتورة: $e');
      rethrow;
    }
  }

  // ==================== إدارة الإعدادات ====================

  /// حفظ إعداد
  static Future<void> saveSetting(String key, dynamic value) async {
    if (!_isInitialized) await initialize();

    try {
      await _settingsBox.put(key,
          {'value': value, 'updated_at': DateTime.now().toIso8601String()});
    } catch (e) {
      debugPrint('❌ فشل في حفظ الإعداد $key: $e');
    }
  }

  /// جلب إعداد
  static T? getSetting<T>(String key, [T? defaultValue]) {
    if (!_isInitialized) return defaultValue;

    try {
      final settingMap = _settingsBox.get(key);
      if (settingMap != null) {
        return settingMap['value'] as T?;
      }
      return defaultValue;
    } catch (e) {
      debugPrint('❌ فشل في جلب الإعداد $key: $e');
      return defaultValue;
    }
  }

  // ==================== إدارة تاريخ العملة ====================

  /// حفظ سعر دولار جديد
  static Future<void> saveDollarRate(double rate) async {
    if (!_isInitialized) await initialize();

    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      await _currencyHistoryBox.put(timestamp, {
        'rate': rate,
        'date': DateTime.now().toIso8601String(),
      });

      // الاحتفاظ بآخر 100 سجل فقط
      if (_currencyHistoryBox.length > 100) {
        final keys = _currencyHistoryBox.keys.toList();
        keys.sort();
        for (int i = 0; i < keys.length - 100; i++) {
          await _currencyHistoryBox.delete(keys[i]);
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في حفظ سعر الدولار: $e');
    }
  }

  /// الحصول على تاريخ أسعار الدولار
  static Future<List<Map<String, dynamic>>> getDollarHistory() async {
    if (!_isInitialized) await initialize();

    try {
      final history = <Map<String, dynamic>>[];
      for (var entry in _currencyHistoryBox.values) {
        history.add(Map<String, dynamic>.from(entry));
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      history.sort((a, b) =>
          DateTime.parse(b['date']).compareTo(DateTime.parse(a['date'])));

      return history;
    } catch (e) {
      debugPrint('❌ فشل في جلب تاريخ الدولار: $e');
      return [];
    }
  }

  // ==================== وظائف مساعدة ====================

  /// الحصول على ID جديد
  static int _getNextId(Box<Map> box) {
    if (box.isEmpty) return 1;

    final keys =
        box.keys.map((key) => int.tryParse(key.toString()) ?? 0).toList();
    keys.sort();
    return keys.last + 1;
  }

  /// إنشاء بيانات تجريبية
  static Future<void> _createSampleDataIfEmpty() async {
    // إنشاء منتجات تجريبية
    if (_productsBox.isEmpty) {
      final sampleProducts = [
        Product(
          id: 1,
          name: 'لابتوب Dell',
          barcode: '123456789',
          purchasePrice: 800000,
          salePrice: 1000000,
          quantity: 10,
          notes: 'لابتوب للألعاب',
          minQuantity: 2,
          sellingPriceSyp: 1000000,
          sellingPriceUsd: 74,
          purchasePriceSyp: 800000,
          purchasePriceUsd: 59,
          purchaseCurrency: 'SYP',
        ),
        Product(
          id: 2,
          name: 'ماوس لاسلكي',
          barcode: '987654321',
          purchasePrice: 50000,
          salePrice: 75000,
          quantity: 25,
          notes: 'ماوس بلوتوث',
          minQuantity: 5,
          sellingPriceSyp: 75000,
          sellingPriceUsd: 5.5,
          purchasePriceSyp: 50000,
          purchasePriceUsd: 3.7,
          purchaseCurrency: 'SYP',
        ),
      ];

      for (var product in sampleProducts) {
        await _productsBox.put(product.id.toString(), product.toJson());
      }
      debugPrint('✅ تم إنشاء منتجات تجريبية');
    }

    // إنشاء عملاء تجريبيين
    if (_customersBox.isEmpty) {
      final sampleCustomers = [
        Customer(
          id: 1,
          name: 'أحمد محمد',
          phone: '0991234567',
          address: 'دمشق - المزة',
          totalDebt: 150000,
          createdAt: DateTime.now(),
        ),
        Customer(
          id: 2,
          name: 'فاطمة علي',
          phone: '0987654321',
          address: 'حلب - الفرقان',
          totalDebt: 0,
          createdAt: DateTime.now(),
        ),
      ];

      for (var customer in sampleCustomers) {
        await _customersBox.put(customer.id.toString(), customer.toJson());
      }
      debugPrint('✅ تم إنشاء عملاء تجريبيين');
    }

    // حفظ سعر دولار افتراضي
    if (getSetting<double>('dollar_rate') == null) {
      await saveSetting('dollar_rate', 13500.0);
      await saveDollarRate(13500.0);
      debugPrint('✅ تم تعيين سعر دولار افتراضي');
    }
  }

  /// مسح جميع البيانات (للاختبار)
  static Future<void> clearAllData() async {
    if (!_isInitialized) await initialize();

    await _productsBox.clear();
    await _customersBox.clear();
    await _invoicesBox.clear();
    await _settingsBox.clear();
    await _currencyHistoryBox.clear();

    debugPrint('🗑️ تم مسح جميع البيانات');
  }

  /// إحصائيات قاعدة البيانات
  static Map<String, int> getDatabaseStats() {
    if (!_isInitialized) return {};

    return {
      'products': _productsBox.length,
      'customers': _customersBox.length,
      'invoices': _invoicesBox.length,
      'settings': _settingsBox.length,
      'currency_history': _currencyHistoryBox.length,
    };
  }

  // ==================== وظائف عامة للصناديق ====================

  /// الحصول على جميع بيانات صندوق معين
  static Future<Map<String, dynamic>> getBoxData(String boxName) async {
    if (!_isInitialized) await initialize();

    try {
      Box<Map>? box = _getBoxByName(boxName);
      if (box == null) return {};

      final data = <String, dynamic>{};
      for (var key in box.keys) {
        data[key.toString()] = box.get(key);
      }
      return data;
    } catch (e) {
      debugPrint('❌ فشل في جلب بيانات الصندوق $boxName: $e');
      return {};
    }
  }

  /// الحصول على عنصر واحد من صندوق
  static Future<Map<String, dynamic>?> getBoxItem(
      String boxName, String key) async {
    if (!_isInitialized) await initialize();

    try {
      Box<Map>? box = _getBoxByName(boxName);
      if (box == null) return null;

      final item = box.get(key);
      return item != null ? Map<String, dynamic>.from(item) : null;
    } catch (e) {
      debugPrint('❌ فشل في جلب العنصر $key من الصندوق $boxName: $e');
      return null;
    }
  }

  /// حفظ عنصر في صندوق
  static Future<void> saveBoxItem(
      String boxName, String key, Map<String, dynamic> data) async {
    if (!_isInitialized) await initialize();

    try {
      Box<Map>? box = _getBoxByName(boxName);
      box ??= await Hive.openBox<Map>(boxName);

      await box.put(key, data);
    } catch (e) {
      debugPrint('❌ فشل في حفظ العنصر $key في الصندوق $boxName: $e');
      rethrow;
    }
  }

  /// حذف عنصر من صندوق
  static Future<void> deleteBoxItem(String boxName, String key) async {
    if (!_isInitialized) await initialize();

    try {
      Box<Map>? box = _getBoxByName(boxName);
      if (box == null) return;

      await box.delete(key);
    } catch (e) {
      debugPrint('❌ فشل في حذف العنصر $key من الصندوق $boxName: $e');
      rethrow;
    }
  }

  /// مسح صندوق كامل
  static Future<void> clearBox(String boxName) async {
    if (!_isInitialized) await initialize();

    try {
      Box<Map>? box = _getBoxByName(boxName);
      if (box == null) return;

      await box.clear();
    } catch (e) {
      debugPrint('❌ فشل في مسح الصندوق $boxName: $e');
      rethrow;
    }
  }

  /// الحصول على صندوق بالاسم
  static Box<Map>? _getBoxByName(String boxName) {
    switch (boxName) {
      case 'products':
        return _productsBox;
      case 'customers':
        return _customersBox;
      case 'invoices':
        return _invoicesBox;
      case 'settings':
        return _settingsBox;
      case 'currency_history':
        return _currencyHistoryBox;
      default:
        // محاولة فتح صندوق جديد
        try {
          return Hive.box<Map>(boxName);
        } catch (e) {
          debugPrint('⚠️ الصندوق $boxName غير موجود');
          return null;
        }
    }
  }

  /// فتح صندوق جديد
  static Future<Box<Map>> openBox(String boxName) async {
    try {
      return await Hive.openBox<Map>(boxName);
    } catch (e) {
      debugPrint('❌ فشل في فتح الصندوق $boxName: $e');
      rethrow;
    }
  }
}
