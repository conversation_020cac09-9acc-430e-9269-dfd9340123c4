import 'package:flutter/material.dart';
import '../models/product.dart';

class AddEditProductDialog extends StatefulWidget {
  final Product? product;

  const AddEditProductDialog({super.key, this.product});

  @override
  State<AddEditProductDialog> createState() => _AddEditProductDialogState();
}

class _AddEditProductDialogState extends State<AddEditProductDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _barcodeController;
  late TextEditingController _purchasePriceController;
  late TextEditingController _salePriceController;
  late TextEditingController _quantityController;
  late TextEditingController _notesController;
  late TextEditingController _minQuantityController;
  late TextEditingController _sellingPriceSypController;
  late TextEditingController _sellingPriceUsdController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.product?.name ?? '');
    _barcodeController =
        TextEditingController(text: widget.product?.barcode ?? '');
    _purchasePriceController = TextEditingController(
        text: widget.product?.purchasePrice.toString() ?? '');
    _salePriceController =
        TextEditingController(text: widget.product?.salePrice.toString() ?? '');
    _quantityController =
        TextEditingController(text: widget.product?.quantity.toString() ?? '');
    _notesController = TextEditingController(text: widget.product?.notes ?? '');
    _minQuantityController = TextEditingController(
        text: widget.product?.minQuantity.toString() ?? '5');
    _sellingPriceSypController = TextEditingController(
        text: widget.product?.sellingPriceSyp.toString() ?? '');
    _sellingPriceUsdController = TextEditingController(
        text: widget.product?.sellingPriceUsd.toString() ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.product == null ? 'إضافة منتج' : 'تعديل منتج'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'اسم المنتج'),
                validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
              ),
              TextFormField(
                controller: _barcodeController,
                decoration: const InputDecoration(labelText: 'الباركود'),
              ),
              TextFormField(
                controller: _purchasePriceController,
                decoration: const InputDecoration(labelText: 'سعر الشراء'),
                keyboardType: TextInputType.number,
                validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
              ),
              TextFormField(
                controller: _salePriceController,
                decoration: const InputDecoration(labelText: 'سعر البيع'),
                keyboardType: TextInputType.number,
                validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
              ),
              TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(labelText: 'الكمية'),
                keyboardType: TextInputType.number,
                validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
              ),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'ملاحظات'),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveProduct,
          child: const Text('حفظ'),
        ),
      ],
    );
  }

  void _saveProduct() {
    if (_formKey.currentState!.validate()) {
      final product = Product(
        id: widget.product?.id ?? 0,
        name: _nameController.text,
        barcode:
            _barcodeController.text.isEmpty ? null : _barcodeController.text,
        purchasePrice: double.parse(_purchasePriceController.text),
        salePrice: double.parse(_salePriceController.text),
        quantity: int.parse(_quantityController.text),
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        minQuantity: int.tryParse(_minQuantityController.text) ?? 5,
        sellingPriceSyp:
            double.tryParse(_sellingPriceSypController.text) ?? 0.0,
        sellingPriceUsd:
            double.tryParse(_sellingPriceUsdController.text) ?? 0.0,
        purchasePriceSyp: double.parse(
            _purchasePriceController.text), // نفس سعر الشراء مؤقتاً
        purchasePriceUsd: 0.0, // سيتم حسابه لاحقاً
      );
      Navigator.of(context).pop(product);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _barcodeController.dispose();
    _purchasePriceController.dispose();
    _salePriceController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    _minQuantityController.dispose();
    _sellingPriceSypController.dispose();
    _sellingPriceUsdController.dispose();
    super.dispose();
  }
}
