import 'package:hive_flutter/hive_flutter.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/invoice.dart';

class OfflineStorageService {
  static const String _productsBox = 'products';
  static const String _customersBox = 'customers';
  static const String _invoicesBox = 'invoices';
  static const String _settingsBox = 'settings';
  static const String _syncQueueBox = 'sync_queue';

  /// تهيئة Hive
  static Future<void> initialize() async {
    await Hive.initFlutter();
    
    // فتح الصناديق
    await Hive.openBox(_productsBox);
    await Hive.openBox(_customersBox);
    await Hive.openBox(_invoicesBox);
    await Hive.openBox(_settingsBox);
    await Hive.openBox(_syncQueueBox);
  }

  /// حفظ المنتجات محلياً
  static Future<void> saveProducts(List<Product> products) async {
    final box = Hive.box(_productsBox);
    final productsMap = {
      for (var product in products) product.id.toString(): product.toJson()
    };
    await box.putAll(productsMap);
  }

  /// جلب المنتجات من التخزين المحلي
  static List<Product> getProducts() {
    final box = Hive.box(_productsBox);
    return box.values
        .map((data) => Product.fromJson(Map<String, dynamic>.from(data)))
        .toList();
  }

  /// حفظ العملاء محلياً
  static Future<void> saveCustomers(List<Customer> customers) async {
    final box = Hive.box(_customersBox);
    final customersMap = {
      for (var customer in customers) customer.id.toString(): customer.toJson()
    };
    await box.putAll(customersMap);
  }

  /// جلب العملاء من التخزين المحلي
  static List<Customer> getCustomers() {
    final box = Hive.box(_customersBox);
    return box.values
        .map((data) => Customer.fromJson(Map<String, dynamic>.from(data)))
        .toList();
  }

  /// حفظ الفواتير محلياً
  static Future<void> saveInvoices(List<Invoice> invoices) async {
    final box = Hive.box(_invoicesBox);
    final invoicesMap = {
      for (var invoice in invoices) invoice.id.toString(): invoice.toJson()
    };
    await box.putAll(invoicesMap);
  }

  /// جلب الفواتير من التخزين المحلي
  static List<Invoice> getInvoices() {
    final box = Hive.box(_invoicesBox);
    return box.values
        .map((data) => Invoice.fromJson(Map<String, dynamic>.from(data)))
        .toList();
  }

  /// حفظ الإعدادات
  static Future<void> saveSetting(String key, dynamic value) async {
    final box = Hive.box(_settingsBox);
    await box.put(key, value);
  }

  /// جلب إعداد
  static T? getSetting<T>(String key, [T? defaultValue]) {
    final box = Hive.box(_settingsBox);
    return box.get(key, defaultValue: defaultValue);
  }

  /// إضافة عملية للمزامنة
  static Future<void> addToSyncQueue(Map<String, dynamic> operation) async {
    final box = Hive.box(_syncQueueBox);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    await box.put(timestamp.toString(), operation);
  }

  /// جلب قائمة المزامنة
  static List<Map<String, dynamic>> getSyncQueue() {
    final box = Hive.box(_syncQueueBox);
    return box.values
        .map((data) => Map<String, dynamic>.from(data))
        .toList();
  }

  /// مسح عملية من قائمة المزامنة
  static Future<void> removeFromSyncQueue(String key) async {
    final box = Hive.box(_syncQueueBox);
    await box.delete(key);
  }

  /// مسح جميع البيانات المحلية
  static Future<void> clearAllData() async {
    await Hive.box(_productsBox).clear();
    await Hive.box(_customersBox).clear();
    await Hive.box(_invoicesBox).clear();
    await Hive.box(_syncQueueBox).clear();
  }

  /// فحص ما إذا كانت البيانات موجودة محلياً
  static bool hasOfflineData() {
    final productsBox = Hive.box(_productsBox);
    final customersBox = Hive.box(_customersBox);
    final invoicesBox = Hive.box(_invoicesBox);
    
    return productsBox.isNotEmpty || 
           customersBox.isNotEmpty || 
           invoicesBox.isNotEmpty;
  }

  /// حفظ وقت آخر مزامنة
  static Future<void> saveLastSyncTime() async {
    await saveSetting('last_sync_time', DateTime.now().millisecondsSinceEpoch);
  }

  /// جلب وقت آخر مزامنة
  static DateTime? getLastSyncTime() {
    final timestamp = getSetting<int>('last_sync_time');
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  /// فحص ما إذا كانت المزامنة مطلوبة
  static bool isSyncRequired() {
    final lastSync = getLastSyncTime();
    if (lastSync == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    // مزامنة كل 30 دقيقة
    return difference.inMinutes > 30;
  }
}
