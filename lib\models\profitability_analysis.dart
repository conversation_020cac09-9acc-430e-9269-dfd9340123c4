import 'package:hive/hive.dart';

part 'profitability_analysis.g.dart';

/// نموذج تحليل الربحية
@HiveType(typeId: 16)
class ProfitabilityAnalysis extends HiveObject {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final DateTime periodStart;

  @HiveField(2)
  final DateTime periodEnd;

  @HiveField(3)
  final double totalRevenue;

  @HiveField(4)
  final double totalCost;

  @HiveField(5)
  final double grossProfit;

  @HiveField(6)
  final double operatingExpenses;

  @HiveField(7)
  final double netProfit;

  @HiveField(8)
  final double grossProfitMargin;

  @HiveField(9)
  final double netProfitMargin;

  @HiveField(10)
  final Map<String, double> revenueByCategory;

  @HiveField(11)
  final Map<String, double> costByCategory;

  @HiveField(12)
  final Map<String, double> profitByProduct;

  @HiveField(13)
  final Map<String, double> profitByCustomer;

  @HiveField(14)
  final List<ProfitTrend> trends;

  @HiveField(15)
  final DateTime createdAt;

  ProfitabilityAnalysis({
    required this.id,
    required this.periodStart,
    required this.periodEnd,
    required this.totalRevenue,
    required this.totalCost,
    required this.grossProfit,
    required this.operatingExpenses,
    required this.netProfit,
    required this.grossProfitMargin,
    required this.netProfitMargin,
    required this.revenueByCategory,
    required this.costByCategory,
    required this.profitByProduct,
    required this.profitByCustomer,
    required this.trends,
    required this.createdAt,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
      'total_revenue': totalRevenue,
      'total_cost': totalCost,
      'gross_profit': grossProfit,
      'operating_expenses': operatingExpenses,
      'net_profit': netProfit,
      'gross_profit_margin': grossProfitMargin,
      'net_profit_margin': netProfitMargin,
      'revenue_by_category': revenueByCategory,
      'cost_by_category': costByCategory,
      'profit_by_product': profitByProduct,
      'profit_by_customer': profitByCustomer,
      'trends': trends.map((t) => t.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory ProfitabilityAnalysis.fromJson(Map<String, dynamic> json) {
    return ProfitabilityAnalysis(
      id: json['id'] ?? 0,
      periodStart: DateTime.parse(json['period_start']),
      periodEnd: DateTime.parse(json['period_end']),
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      totalCost: (json['total_cost'] ?? 0.0).toDouble(),
      grossProfit: (json['gross_profit'] ?? 0.0).toDouble(),
      operatingExpenses: (json['operating_expenses'] ?? 0.0).toDouble(),
      netProfit: (json['net_profit'] ?? 0.0).toDouble(),
      grossProfitMargin: (json['gross_profit_margin'] ?? 0.0).toDouble(),
      netProfitMargin: (json['net_profit_margin'] ?? 0.0).toDouble(),
      revenueByCategory: Map<String, double>.from(json['revenue_by_category'] ?? {}),
      costByCategory: Map<String, double>.from(json['cost_by_category'] ?? {}),
      profitByProduct: Map<String, double>.from(json['profit_by_product'] ?? {}),
      profitByCustomer: Map<String, double>.from(json['profit_by_customer'] ?? {}),
      trends: (json['trends'] as List<dynamic>?)
          ?.map((t) => ProfitTrend.fromJson(t))
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  /// حساب معدل العائد على الاستثمار
  double get roi {
    if (totalCost == 0) return 0;
    return (netProfit / totalCost) * 100;
  }

  /// حساب نقطة التعادل
  double get breakEvenPoint {
    if (grossProfitMargin == 0) return 0;
    return operatingExpenses / (grossProfitMargin / 100);
  }

  /// الحصول على أفضل المنتجات ربحية
  List<MapEntry<String, double>> get topProfitableProducts {
    var entries = profitByProduct.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(10).toList();
  }

  /// الحصول على أفضل العملاء ربحية
  List<MapEntry<String, double>> get topProfitableCustomers {
    var entries = profitByCustomer.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(10).toList();
  }
}

/// نموذج اتجاه الربحية
@HiveType(typeId: 17)
class ProfitTrend extends HiveObject {
  @HiveField(0)
  final DateTime date;

  @HiveField(1)
  final double revenue;

  @HiveField(2)
  final double cost;

  @HiveField(3)
  final double profit;

  @HiveField(4)
  final double profitMargin;

  ProfitTrend({
    required this.date,
    required this.revenue,
    required this.cost,
    required this.profit,
    required this.profitMargin,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'revenue': revenue,
      'cost': cost,
      'profit': profit,
      'profit_margin': profitMargin,
    };
  }

  /// إنشاء من JSON
  factory ProfitTrend.fromJson(Map<String, dynamic> json) {
    return ProfitTrend(
      date: DateTime.parse(json['date']),
      revenue: (json['revenue'] ?? 0.0).toDouble(),
      cost: (json['cost'] ?? 0.0).toDouble(),
      profit: (json['profit'] ?? 0.0).toDouble(),
      profitMargin: (json['profit_margin'] ?? 0.0).toDouble(),
    );
  }
}

/// نموذج تحليل المنتج
@HiveType(typeId: 18)
class ProductProfitability extends HiveObject {
  @HiveField(0)
  final int productId;

  @HiveField(1)
  final String productName;

  @HiveField(2)
  final int quantitySold;

  @HiveField(3)
  final double totalRevenue;

  @HiveField(4)
  final double totalCost;

  @HiveField(5)
  final double grossProfit;

  @HiveField(6)
  final double profitMargin;

  @HiveField(7)
  final double averageSellingPrice;

  @HiveField(8)
  final double averageCost;

  @HiveField(9)
  final DateTime periodStart;

  @HiveField(10)
  final DateTime periodEnd;

  ProductProfitability({
    required this.productId,
    required this.productName,
    required this.quantitySold,
    required this.totalRevenue,
    required this.totalCost,
    required this.grossProfit,
    required this.profitMargin,
    required this.averageSellingPrice,
    required this.averageCost,
    required this.periodStart,
    required this.periodEnd,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'product_name': productName,
      'quantity_sold': quantitySold,
      'total_revenue': totalRevenue,
      'total_cost': totalCost,
      'gross_profit': grossProfit,
      'profit_margin': profitMargin,
      'average_selling_price': averageSellingPrice,
      'average_cost': averageCost,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory ProductProfitability.fromJson(Map<String, dynamic> json) {
    return ProductProfitability(
      productId: json['product_id'] ?? 0,
      productName: json['product_name'] ?? '',
      quantitySold: json['quantity_sold'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      totalCost: (json['total_cost'] ?? 0.0).toDouble(),
      grossProfit: (json['gross_profit'] ?? 0.0).toDouble(),
      profitMargin: (json['profit_margin'] ?? 0.0).toDouble(),
      averageSellingPrice: (json['average_selling_price'] ?? 0.0).toDouble(),
      averageCost: (json['average_cost'] ?? 0.0).toDouble(),
      periodStart: DateTime.parse(json['period_start']),
      periodEnd: DateTime.parse(json['period_end']),
    );
  }

  /// حساب العائد لكل وحدة
  double get profitPerUnit {
    if (quantitySold == 0) return 0;
    return grossProfit / quantitySold;
  }

  /// حساب معدل الدوران
  double get turnoverRate {
    final days = periodEnd.difference(periodStart).inDays;
    if (days == 0) return 0;
    return quantitySold / days;
  }
}

/// نموذج تحليل العميل
@HiveType(typeId: 19)
class CustomerProfitability extends HiveObject {
  @HiveField(0)
  final int customerId;

  @HiveField(1)
  final String customerName;

  @HiveField(2)
  final int totalOrders;

  @HiveField(3)
  final double totalRevenue;

  @HiveField(4)
  final double totalCost;

  @HiveField(5)
  final double grossProfit;

  @HiveField(6)
  final double profitMargin;

  @HiveField(7)
  final double averageOrderValue;

  @HiveField(8)
  final double customerLifetimeValue;

  @HiveField(9)
  final DateTime firstOrderDate;

  @HiveField(10)
  final DateTime lastOrderDate;

  CustomerProfitability({
    required this.customerId,
    required this.customerName,
    required this.totalOrders,
    required this.totalRevenue,
    required this.totalCost,
    required this.grossProfit,
    required this.profitMargin,
    required this.averageOrderValue,
    required this.customerLifetimeValue,
    required this.firstOrderDate,
    required this.lastOrderDate,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'customer_id': customerId,
      'customer_name': customerName,
      'total_orders': totalOrders,
      'total_revenue': totalRevenue,
      'total_cost': totalCost,
      'gross_profit': grossProfit,
      'profit_margin': profitMargin,
      'average_order_value': averageOrderValue,
      'customer_lifetime_value': customerLifetimeValue,
      'first_order_date': firstOrderDate.toIso8601String(),
      'last_order_date': lastOrderDate.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory CustomerProfitability.fromJson(Map<String, dynamic> json) {
    return CustomerProfitability(
      customerId: json['customer_id'] ?? 0,
      customerName: json['customer_name'] ?? '',
      totalOrders: json['total_orders'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
      totalCost: (json['total_cost'] ?? 0.0).toDouble(),
      grossProfit: (json['gross_profit'] ?? 0.0).toDouble(),
      profitMargin: (json['profit_margin'] ?? 0.0).toDouble(),
      averageOrderValue: (json['average_order_value'] ?? 0.0).toDouble(),
      customerLifetimeValue: (json['customer_lifetime_value'] ?? 0.0).toDouble(),
      firstOrderDate: DateTime.parse(json['first_order_date']),
      lastOrderDate: DateTime.parse(json['last_order_date']),
    );
  }

  /// حساب تكرار الطلبات
  double get orderFrequency {
    final days = lastOrderDate.difference(firstOrderDate).inDays;
    if (days == 0 || totalOrders <= 1) return 0;
    return totalOrders / days;
  }

  /// حساب الربح لكل طلب
  double get profitPerOrder {
    if (totalOrders == 0) return 0;
    return grossProfit / totalOrders;
  }
}
