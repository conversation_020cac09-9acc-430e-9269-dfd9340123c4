import 'package:flutter/foundation.dart';

/// إعدادات التطبيق المبسطة
class AppConfig {
  static const String appName = 'نظام المحاسبة الذكي';
  static const String appVersion = '1.0.0';
  static const String companyName = '<PERSON>';
  
  // إعدادات التحميل
  static const bool enableSplashScreen = true;
  static const Duration splashDuration = Duration(seconds: 3);
  static const bool enableLazyLoading = true;
  
  // إعدادات الأداء
  static const bool enableDebugMode = kDebugMode;
  static const bool enableErrorReporting = true;
  static const bool enableAnalytics = false; // معطل للخصوصية
  
  // إعدادات الواجهة
  static const bool enableAnimations = true;
  static const bool enableHapticFeedback = true;
  static const bool enableSoundEffects = false;
  
  // إعدادات قاعدة البيانات
  static const String databaseName = 'smart_accounting';
  static const int databaseVersion = 1;
  static const bool enableEncryption = true;
  
  // إعدادات الإشعارات
  static const bool enableNotifications = true;
  static const bool enableSmartNotifications = true;
  static const Duration notificationInterval = Duration(hours: 1);
  
  // إعدادات النسخ الاحتياطي
  static const bool enableAutoBackup = true;
  static const Duration backupInterval = Duration(days: 1);
  static const int maxBackupFiles = 5;
  
  // إعدادات الأمان
  static const bool enablePinLock = true;
  static const bool enableBiometrics = false; // معطل مؤقتاً
  static const Duration sessionTimeout = Duration(minutes: 30);
  
  // إعدادات العملة
  static const String defaultCurrency = 'SYP';
  static const String secondaryCurrency = 'USD';
  static const double defaultDollarRate = 15000.0;
  
  // إعدادات التقارير
  static const bool enableAdvancedReports = true;
  static const bool enableAIInsights = true;
  static const int maxReportHistory = 100;
  
  // إعدادات الشبكة
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const bool enableOfflineMode = true;
  
  // إعدادات التطوير
  static const bool enableDebugPrint = kDebugMode;
  static const bool enablePerformanceMonitoring = kDebugMode;
  static const bool enableMemoryOptimization = true;
  
  /// التحقق من صحة الإعدادات
  static bool validateConfig() {
    try {
      // فحص الإعدادات الأساسية
      if (appName.isEmpty || appVersion.isEmpty) {
        return false;
      }
      
      // فحص إعدادات قاعدة البيانات
      if (databaseName.isEmpty || databaseVersion < 1) {
        return false;
      }
      
      // فحص إعدادات العملة
      if (defaultDollarRate <= 0) {
        return false;
      }
      
      return true;
    } catch (e) {
      if (enableDebugPrint) {
        debugPrint('خطأ في فحص الإعدادات: $e');
      }
      return false;
    }
  }
  
  /// الحصول على معلومات التطبيق
  static Map<String, dynamic> getAppInfo() {
    return {
      'name': appName,
      'version': appVersion,
      'company': companyName,
      'debug_mode': enableDebugMode,
      'database_version': databaseVersion,
      'default_currency': defaultCurrency,
      'offline_mode': enableOfflineMode,
    };
  }
  
  /// الحصول على إعدادات الأداء
  static Map<String, dynamic> getPerformanceSettings() {
    return {
      'lazy_loading': enableLazyLoading,
      'animations': enableAnimations,
      'memory_optimization': enableMemoryOptimization,
      'performance_monitoring': enablePerformanceMonitoring,
    };
  }
  
  /// الحصول على إعدادات الأمان
  static Map<String, dynamic> getSecuritySettings() {
    return {
      'pin_lock': enablePinLock,
      'biometrics': enableBiometrics,
      'encryption': enableEncryption,
      'session_timeout': sessionTimeout.inMinutes,
    };
  }
  
  /// طباعة معلومات التطبيق (للتطوير فقط)
  static void printAppInfo() {
    if (!enableDebugPrint) return;
    
    debugPrint('=== معلومات التطبيق ===');
    debugPrint('الاسم: $appName');
    debugPrint('الإصدار: $appVersion');
    debugPrint('الشركة: $companyName');
    debugPrint('وضع التطوير: $enableDebugMode');
    debugPrint('الوضع المحلي: $enableOfflineMode');
    debugPrint('========================');
  }
}
